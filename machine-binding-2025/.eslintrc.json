{"env": {"browser": true, "es2020": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "warn", "react-refresh/only-export-components": "off"}, "settings": {"react": {"version": "detect"}}}