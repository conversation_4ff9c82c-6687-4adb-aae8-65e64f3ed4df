import { api } from './api';
import { ApiResponse, UploadProgress } from '../types';

export interface UploadResult {
  url: string;
  filename: string;
  size: number;
}

export interface MultipleUploadResult {
  urls: string[];
  filenames: string[];
  totalSize: number;
}

export const uploadService = {
  // Upload single image
  uploadImage: async (
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<ApiResponse<UploadResult>> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'image');

    return api.upload<UploadResult>('/api/upload/image', formData, (percentage) => {
      if (onProgress) {
        onProgress({
          loaded: (file.size * percentage) / 100,
          total: file.size,
          percentage,
        });
      }
    });
  },

  // Upload multiple images
  uploadMultipleImages: async (
    files: File[],
    onProgress?: (progress: UploadProgress) => void
  ): Promise<ApiResponse<MultipleUploadResult>> => {
    const formData = new FormData();
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });
    formData.append('type', 'images');

    return api.upload<MultipleUploadResult>('/api/upload/multiple', formData, (percentage) => {
      if (onProgress) {
        onProgress({
          loaded: (totalSize * percentage) / 100,
          total: totalSize,
          percentage,
        });
      }
    });
  },

  // Validate file type
  validateFileType: (file: File, allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp']): boolean => {
    return allowedTypes.includes(file.type);
  },

  // Validate file size (default 5MB)
  validateFileSize: (file: File, maxSizeInMB: number = 5): boolean => {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  },

  // Compress image before upload
  compressImage: (file: File, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions (max 1920px width)
        const maxWidth = 1920;
        const maxHeight = 1920;
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              resolve(file);
            }
          },
          file.type,
          quality
        );
      };

      img.src = URL.createObjectURL(file);
    });
  },

  // Create image preview URL
  createPreviewUrl: (file: File): string => {
    return URL.createObjectURL(file);
  },

  // Revoke preview URL to free memory
  revokePreviewUrl: (url: string): void => {
    URL.revokeObjectURL(url);
  },

  // Format file size for display
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Get file extension
  getFileExtension: (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
  },

  // Generate unique filename
  generateUniqueFilename: (originalName: string): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    const extension = uploadService.getFileExtension(originalName);
    return `${timestamp}_${random}.${extension}`;
  },
};