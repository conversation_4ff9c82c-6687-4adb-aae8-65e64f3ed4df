import { api, retryRequest } from './api';
import { User, LoginCredentials, ApiResponse } from '../types';

export interface LoginResponse {
  token: string;
  user: User;
  expires_in?: number;
}

export interface TaskCountResponse {
  count_info: number;
  count_task: number;
}

export const authService = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {
    return retryRequest(() =>
      api.post<LoginResponse>('/auth/login', credentials)
    );
  },

  // Logout user
  logout: async (): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/logout');
  },

  // Get current user profile
  getProfile: async (): Promise<ApiResponse<User>> => {
    return api.get<User>('/auth/profile');
  },

  // Refresh authentication token
  refreshToken: async (): Promise<ApiResponse<{ token: string }>> => {
    return api.post<{ token: string }>('/auth/refresh');
  },

  // Get task and info counts for badge display
  getTaskCount: async (): Promise<ApiResponse<TaskCountResponse>> => {
    return retryRequest(() =>
      api.get<TaskCountResponse>('/api/info_task_count')
    );
  },

  // Get company users (admin function)
  getCompanyUsers: async (): Promise<ApiResponse<User[]>> => {
    return api.get<User[]>('/api/company/users');
  },

  // Validate user session
  validateSession: async (): Promise<boolean> => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return false;

      await api.get('/auth/validate');
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  },

  // Store authentication data
  storeAuthData: (token: string, user: User): void => {
    localStorage.setItem('auth_token', token);
    localStorage.setItem('user_data', JSON.stringify(user));
  },

  // Clear authentication data
  clearAuthData: (): void => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  },

  // Get stored user data
  getStoredUser: (): User | null => {
    try {
      const userData = localStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      return null;
    }
  },

  // Get stored token
  getStoredToken: (): string | null => {
    return localStorage.getItem('auth_token');
  },
};