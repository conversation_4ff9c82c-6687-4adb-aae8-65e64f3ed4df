import { api, retryRequest } from './api';
import { 
  SAPDeviceData, 
  QRCodeValidationResponse, 
  BindingRecord,
  ApiResponse 
} from '../types';

export interface DeviceBindingSubmission {
  brand: string;
  serial_num: string;
  nameplate: string; // Comma-separated photo URLs
  whole: string; // Comma-separated photo URLs
  pt_gps: string; // Comma-separated photo URLs
  qr_code_complete: string;
  // SAP data fields
  fl_street?: string;
  equnr?: string;
  pgroup_name?: string;
  sales_off?: string;
  plangroup?: string;
  enduser_name?: string;
  enduser?: string;
  inbdt?: string;
  typbz?: string;
  end_date?: string;
}

export interface QRCodeRequest {
  appqrcode: string;
}

export interface SAPSearchRequest {
  brand: string;
  serial_num: string;
}

export interface BindingSearchRequest {
  name?: string;
  state?: string;
}

export const deviceService = {
  // Validate QR code
  validateQRCode: async (qrCode: string): Promise<ApiResponse<QRCodeValidationResponse>> => {
    // Import QR code utilities
    const { qrCodeUtils, qrCodeCache } = await import('../utils/qrcode');
    
    // Validate format first
    const formatValidation = qrCodeUtils.validateBusinessRules(qrCode);
    if (!formatValidation.isValid) {
      throw new Error(formatValidation.errorMessage || 'Invalid QR code format');
    }

    // Check cache first
    const cacheKey = qrCodeUtils.generateHash(qrCode);
    const cachedResult = qrCodeCache.get(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }

    // Make API request
    const request: QRCodeRequest = { appqrcode: formatValidation.code };
    const result = await retryRequest(() => 
      api.post<QRCodeValidationResponse>('/api/v2/qrcode', request)
    );

    // Cache the result
    qrCodeCache.set(cacheKey, result);

    return result;
  },

  // Search SAP data for device
  searchSAPData: async (brand: string, serialNum: string): Promise<ApiResponse<SAPDeviceData[]>> => {
    const request: SAPSearchRequest = { 
      brand: brand.toUpperCase(), 
      serial_num: serialNum 
    };
    return retryRequest(() => 
      api.post<SAPDeviceData[]>('/api/v2/bind_sap', request)
    );
  },

  // Submit device binding
  submitBinding: async (bindingData: DeviceBindingSubmission): Promise<ApiResponse<any>> => {
    return retryRequest(() => 
      api.post('/api/v2/engineer', bindingData)
    );
  },

  // Get binding records list
  getBindingRecords: async (): Promise<ApiResponse<BindingRecord[]>> => {
    return retryRequest(() => 
      api.get<BindingRecord[]>('/api/v2/bind_list')
    );
  },

  // Search binding records
  searchBindingRecords: async (searchParams: BindingSearchRequest): Promise<ApiResponse<BindingRecord[]>> => {
    return api.post<BindingRecord[]>('/api/device_maintenance_search', searchParams);
  },

  // Get detailed binding record
  getBindingRecordDetail: async (id: string): Promise<ApiResponse<any>> => {
    // For now, we'll simulate getting detailed record data
    // In a real implementation, this would be an API call to get full record details
    // Since we don't have a specific API endpoint for this, we'll create mock data
    // based on the record ID and existing data structure
    
    try {
      // Try to get the record from the list first
      const recordsResponse = await deviceService.getBindingRecords();
      const record = recordsResponse.data.find(r => r.id === id);
      
      if (!record) {
        throw new Error('Record not found');
      }

      // Transform the basic record into a detailed DeviceBinding format
      const detailedRecord = {
        id: record.id,
        qrCode: record.code || 'QR_' + record.serial_num,
        brand: 'BROTHER', // Default brand since we don't have this in the basic record
        serialNumber: record.serial_num || record.code,
        engineer: record.customer_name || '未知工程师',
        customerName: record.customer_name,
        createdAt: record.created_at || record.time,
        status: 'pending' as const, // Default status
        // Mock image arrays - in real implementation these would come from the API
        nameplatePhotos: [
          '/api/uploads/nameplate_1.jpg',
          '/api/uploads/nameplate_2.jpg'
        ],
        wholePhotos: [
          '/api/uploads/whole_1.jpg',
          '/api/uploads/whole_2.jpg',
          '/api/uploads/whole_3.jpg'
        ],
        gpsPhotos: [
          '/api/uploads/gps_1.jpg'
        ]
      };

      return {
        data: detailedRecord,
        message: 'Success',
        status: 200
      };
    } catch (error) {
      console.error('Error getting record detail:', error);
      throw error;
    }
  },

  // Get banner images for carousel
  getBannerImages: async (): Promise<ApiResponse<Array<{ photo_url: string }>>> => {
    return api.get<Array<{ photo_url: string }>>('/api/banner');
  },

  // Upload device images
  uploadDeviceImage: async (
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ url: string }>> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'device_image');

    return api.upload<{ url: string }>('/api/upload', formData, onProgress);
  },

  // Upload multiple device images
  uploadMultipleImages: async (
    files: File[],
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<{ urls: string[] }>> => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });
    formData.append('type', 'device_images');

    return api.upload<{ urls: string[] }>('/api/upload/multiple', formData, onProgress);
  },

  // Process photo URLs for API submission
  processPhotoUrls: (photoUrls: string[]): string[] => {
    return photoUrls.map(url => {
      // Extract filename from full URL (remove upload/ prefix)
      const parts = url.split('upload/');
      return parts.length > 1 ? parts[1] : url;
    });
  },

  // Format QR code for display (last 20 characters)
  formatQRCodeDisplay: (qrCode: string): string => {
    return qrCode.slice(-20);
  },

  // Extract QR code from URL
  extractQRCodeFromUrl: (url: string): string => {
    const parts = url.split('/');
    return parts[parts.length - 1];
  },

  // Validate brand name (uppercase English letters)
  validateBrandName: (brand: string): { isValid: boolean; error?: string } => {
    if (!brand) {
      return { isValid: false, error: '品牌不能为空' };
    }
    
    if (!/^[A-Z]+$/.test(brand)) {
      return { 
        isValid: false, 
        error: '品牌必须是英文大写字母，例如：BROTHER' 
      };
    }
    
    return { isValid: true };
  },

  // Validate serial number
  validateSerialNumber: (serialNum: string): { isValid: boolean; error?: string } => {
    if (!serialNum) {
      return { isValid: false, error: '机身编号不能为空' };
    }
    
    return { isValid: true };
  },

  // Format date for display
  formatDate: (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return dateString;
    }
  },
};