import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ApiResponse, ApiError } from '../types';
import { errorHandler } from '../utils/errorHandler';
import { networkStatusDetector } from '../utils/networkStatus';

// API Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://service.ptmes.cn',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Create Axios instance
const apiClient: AxiosInstance = axios.create(API_CONFIG);

// Request queue for offline handling
interface QueuedRequest {
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

let requestQueue: QueuedRequest[] = [];
let isProcessingQueue = false;

// Process queued requests when back online
const processRequestQueue = async (): Promise<void> => {
  if (isProcessingQueue || requestQueue.length === 0) return;
  
  isProcessingQueue = true;
  
  while (requestQueue.length > 0) {
    const { config, resolve, reject } = requestQueue.shift()!;
    
    try {
      const response = await apiClient.request(config);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  }
  
  isProcessingQueue = false;
};

// Listen for network status changes
networkStatusDetector.addListener((status) => {
  if (status.isOnline && requestQueue.length > 0) {
    processRequestQueue();
  }
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    // Add request ID for tracking
    config.metadata = {
      ...config.metadata,
      requestId: Date.now().toString(36) + Math.random().toString(36).substr(2),
      startTime: Date.now(),
    };

    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url} [${config.metadata.requestId}]`);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    const duration = Date.now() - (response.config.metadata?.startTime || 0);
    console.log(`✅ API Response: ${response.status} ${response.config.url} [${response.config.metadata?.requestId}] (${duration}ms)`);
    return response;
  },
  (error) => {
    const duration = Date.now() - (error.config?.metadata?.startTime || 0);
    console.error(`❌ Response Error: ${error.config?.url} [${error.config?.metadata?.requestId}] (${duration}ms)`, error);

    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const apiError: ApiError = {
        code: error.response.status.toString(),
        message: error.response.data?.message || error.response.data?.detail || 'Server Error',
        details: error.response.data,
        status: error.response.status,
      };

      // Handle authentication errors
      if (error.response.status === 401) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        // Don't redirect immediately, let the auth context handle it
        window.dispatchEvent(new CustomEvent('auth:logout'));
      }

      return Promise.reject(apiError);
    } else if (error.request) {
      // Network error or timeout
      let apiError: ApiError;
      
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        apiError = {
          code: 'TIMEOUT',
          message: 'Request timeout. Please try again.',
          details: error.request,
        };
      } else {
        apiError = {
          code: 'NETWORK_ERROR',
          message: 'Network connection failed. Please check your internet connection.',
          details: error.request,
        };
      }
      
      return Promise.reject(apiError);
    } else {
      // Other error
      const unknownError: ApiError = {
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unexpected error occurred',
        details: error,
      };
      return Promise.reject(unknownError);
    }
  }
);

// Enhanced API methods with error handling and offline support
export const api = {
  // GET request
  get: async <T = any>(
    url: string,
    config?: AxiosRequestConfig & { offline?: boolean }
  ): Promise<ApiResponse<T>> => {
    try {
      // Check if offline and request should be queued
      if (!networkStatusDetector.getStatus().isOnline && config?.offline !== false) {
        return new Promise((resolve, reject) => {
          requestQueue.push({
            config: { ...config, method: 'get', url },
            resolve: (response) => resolve({
              data: response.data,
              status: response.status,
              message: 'Success',
            }),
            reject,
          });
        });
      }

      const response = await apiClient.get<T>(url, config);
      return {
        data: response.data,
        status: response.status,
        message: 'Success',
      };
    } catch (error) {
      errorHandler.handleError(error as ApiError, { showToast: false });
      throw error;
    }
  },

  // POST request
  post: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig & { offline?: boolean }
  ): Promise<ApiResponse<T>> => {
    try {
      // Check if offline and request should be queued
      if (!networkStatusDetector.getStatus().isOnline && config?.offline !== false) {
        return new Promise((resolve, reject) => {
          requestQueue.push({
            config: { ...config, method: 'post', url, data },
            resolve: (response) => resolve({
              data: response.data,
              status: response.status,
              message: 'Success',
            }),
            reject,
          });
        });
      }

      const response = await apiClient.post<T>(url, data, config);
      return {
        data: response.data,
        status: response.status,
        message: 'Success',
      };
    } catch (error) {
      errorHandler.handleError(error as ApiError, { showToast: false });
      throw error;
    }
  },

  // PUT request
  put: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig & { offline?: boolean }
  ): Promise<ApiResponse<T>> => {
    try {
      // Check if offline and request should be queued
      if (!networkStatusDetector.getStatus().isOnline && config?.offline !== false) {
        return new Promise((resolve, reject) => {
          requestQueue.push({
            config: { ...config, method: 'put', url, data },
            resolve: (response) => resolve({
              data: response.data,
              status: response.status,
              message: 'Success',
            }),
            reject,
          });
        });
      }

      const response = await apiClient.put<T>(url, data, config);
      return {
        data: response.data,
        status: response.status,
        message: 'Success',
      };
    } catch (error) {
      errorHandler.handleError(error as ApiError, { showToast: false });
      throw error;
    }
  },

  // DELETE request
  delete: async <T = any>(
    url: string,
    config?: AxiosRequestConfig & { offline?: boolean }
  ): Promise<ApiResponse<T>> => {
    try {
      // Check if offline and request should be queued
      if (!networkStatusDetector.getStatus().isOnline && config?.offline !== false) {
        return new Promise((resolve, reject) => {
          requestQueue.push({
            config: { ...config, method: 'delete', url },
            resolve: (response) => resolve({
              data: response.data,
              status: response.status,
              message: 'Success',
            }),
            reject,
          });
        });
      }

      const response = await apiClient.delete<T>(url, config);
      return {
        data: response.data,
        status: response.status,
        message: 'Success',
      };
    } catch (error) {
      errorHandler.handleError(error as ApiError, { showToast: false });
      throw error;
    }
  },

  // Upload file with progress and retry
  upload: async <T = any>(
    url: string,
    formData: FormData,
    onProgress?: (progress: number) => void,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await errorHandler.retryOperation(
        async () => {
          return await apiClient.post<T>(url, formData, {
            ...config,
            headers: {
              'Content-Type': 'multipart/form-data',
              ...config?.headers,
            },
            onUploadProgress: (progressEvent) => {
              if (onProgress && progressEvent.total) {
                const progress = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                onProgress(progress);
              }
            },
          });
        },
        {
          maxRetries: 2,
          delay: 2000,
          retryCondition: (error: ApiError) => {
            // Retry on network errors but not on validation errors
            return error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT';
          },
        }
      );

      return {
        data: response.data,
        status: response.status,
        message: 'Upload successful',
      };
    } catch (error) {
      errorHandler.handleError(error as ApiError, { showToast: false });
      throw error;
    }
  },

  // Batch requests
  batch: async <T = any>(
    requests: Array<() => Promise<ApiResponse<any>>>
  ): Promise<ApiResponse<T[]>> => {
    try {
      const results = await Promise.allSettled(requests.map(req => req()));
      
      const successful = results
        .filter((result): result is PromiseFulfilledResult<ApiResponse<any>> => 
          result.status === 'fulfilled'
        )
        .map(result => result.value.data);

      const failed = results
        .filter((result): result is PromiseRejectedResult => 
          result.status === 'rejected'
        )
        .map(result => result.reason);

      if (failed.length > 0) {
        console.warn('Some batch requests failed:', failed);
      }

      return {
        data: successful as T[],
        status: 200,
        message: `${successful.length}/${requests.length} requests successful`,
      };
    } catch (error) {
      errorHandler.handleError(error as ApiError, { showToast: false });
      throw error;
    }
  },
};

// Enhanced retry mechanism
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  backoff: 'linear' | 'exponential' = 'exponential'
): Promise<T> => {
  return errorHandler.retryOperation(requestFn, {
    maxRetries,
    delay,
    backoff,
  });
};

// Utility functions
export const clearRequestQueue = (): void => {
  requestQueue = [];
};

export const getQueuedRequestCount = (): number => {
  return requestQueue.length;
};

export const isRequestQueueProcessing = (): boolean => {
  return isProcessingQueue;
};

export default apiClient;