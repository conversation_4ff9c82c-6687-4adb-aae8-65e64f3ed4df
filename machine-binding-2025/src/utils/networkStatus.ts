export interface NetworkStatus {
  isOnline: boolean;
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

export interface NetworkStatusOptions {
  onStatusChange?: (status: NetworkStatus) => void;
  onOffline?: () => void;
  onOnline?: () => void;
  checkInterval?: number;
}

export class NetworkStatusDetector {
  private static instance: NetworkStatusDetector;
  private listeners: Set<(status: NetworkStatus) => void> = new Set();
  private currentStatus: NetworkStatus;
  private checkInterval?: NodeJS.Timeout;

  private constructor() {
    this.currentStatus = this.getCurrentStatus();
    this.setupEventListeners();
  }

  static getInstance(): NetworkStatusDetector {
    if (!NetworkStatusDetector.instance) {
      NetworkStatusDetector.instance = new NetworkStatusDetector();
    }
    return NetworkStatusDetector.instance;
  }

  private getCurrentStatus(): NetworkStatus {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    return {
      isOnline: navigator.onLine,
      connectionType: connection?.type,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt,
    };
  }

  private setupEventListeners(): void {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);

    // Listen for connection changes if supported
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      connection.addEventListener('change', this.handleConnectionChange);
    }
  }

  private handleOnline = (): void => {
    this.updateStatus({ ...this.getCurrentStatus(), isOnline: true });
  };

  private handleOffline = (): void => {
    this.updateStatus({ ...this.getCurrentStatus(), isOnline: false });
  };

  private handleConnectionChange = (): void => {
    this.updateStatus(this.getCurrentStatus());
  };

  private updateStatus(newStatus: NetworkStatus): void {
    const statusChanged = JSON.stringify(this.currentStatus) !== JSON.stringify(newStatus);
    
    if (statusChanged) {
      this.currentStatus = newStatus;
      this.notifyListeners(newStatus);
    }
  }

  private notifyListeners(status: NetworkStatus): void {
    this.listeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('Error in network status listener:', error);
      }
    });
  }

  getStatus(): NetworkStatus {
    return { ...this.currentStatus };
  }

  addListener(listener: (status: NetworkStatus) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  removeListener(listener: (status: NetworkStatus) => void): void {
    this.listeners.delete(listener);
  }

  startPeriodicCheck(interval: number = 30000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(() => {
      this.performConnectivityCheck();
    }, interval);
  }

  stopPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = undefined;
    }
  }

  private async performConnectivityCheck(): Promise<void> {
    try {
      // Try to fetch a small resource to verify actual connectivity
      const response = await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000),
      });

      const isActuallyOnline = response.ok;
      
      if (isActuallyOnline !== this.currentStatus.isOnline) {
        this.updateStatus({
          ...this.getCurrentStatus(),
          isOnline: isActuallyOnline,
        });
      }
    } catch (error) {
      // If fetch fails, we're likely offline
      if (this.currentStatus.isOnline) {
        this.updateStatus({
          ...this.getCurrentStatus(),
          isOnline: false,
        });
      }
    }
  }

  async testConnectivity(url: string = '/favicon.ico', timeout: number = 5000): Promise<boolean> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(timeout),
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  getConnectionQuality(): 'excellent' | 'good' | 'fair' | 'poor' | 'unknown' {
    const { effectiveType, rtt, downlink } = this.currentStatus;

    if (!effectiveType && !rtt && !downlink) {
      return 'unknown';
    }

    // Based on effective connection type
    if (effectiveType === '4g' && (rtt === undefined || rtt < 150) && (downlink === undefined || downlink > 1.5)) {
      return 'excellent';
    }

    if (effectiveType === '4g' || (effectiveType === '3g' && (rtt === undefined || rtt < 300))) {
      return 'good';
    }

    if (effectiveType === '3g' || effectiveType === '2g') {
      return 'fair';
    }

    if (effectiveType === 'slow-2g') {
      return 'poor';
    }

    // Fallback based on RTT and downlink
    if (rtt !== undefined && downlink !== undefined) {
      if (rtt < 100 && downlink > 2) return 'excellent';
      if (rtt < 200 && downlink > 1) return 'good';
      if (rtt < 500 && downlink > 0.5) return 'fair';
      return 'poor';
    }

    return 'unknown';
  }

  destroy(): void {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);

    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      connection.removeEventListener('change', this.handleConnectionChange);
    }

    this.stopPeriodicCheck();
    this.listeners.clear();
  }
}

// Export singleton instance
export const networkStatusDetector = NetworkStatusDetector.getInstance();

// Utility functions
export const getNetworkStatus = (): NetworkStatus => {
  return networkStatusDetector.getStatus();
};

export const isOnline = (): boolean => {
  return networkStatusDetector.getStatus().isOnline;
};

export const getConnectionQuality = (): 'excellent' | 'good' | 'fair' | 'poor' | 'unknown' => {
  return networkStatusDetector.getConnectionQuality();
};

export const testConnectivity = async (url?: string, timeout?: number): Promise<boolean> => {
  return networkStatusDetector.testConnectivity(url, timeout);
};