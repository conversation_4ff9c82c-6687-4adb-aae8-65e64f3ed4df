// QR Code validation and processing utilities

export interface QRCodeValidationResult {
  isValid: boolean;
  code: string;
  displayCode: string;
  errorMessage?: string;
}

export const qrCodeUtils = {
  // Validate QR code format
  validateFormat: (qrCode: string): QRCodeValidationResult => {
    if (!qrCode || typeof qrCode !== 'string') {
      return {
        isValid: false,
        code: '',
        displayCode: '',
        errorMessage: '二维码内容为空',
      };
    }

    const trimmedCode = qrCode.trim();
    
    if (trimmedCode.length === 0) {
      return {
        isValid: false,
        code: '',
        displayCode: '',
        errorMessage: '二维码内容为空',
      };
    }

    // Check minimum length (should be at least 20 characters for display)
    if (trimmedCode.length < 20) {
      return {
        isValid: false,
        code: trimmedCode,
        displayCode: trimmedCode,
        errorMessage: '二维码格式不正确',
      };
    }

    return {
      isValid: true,
      code: trimmedCode,
      displayCode: trimmedCode.slice(-20),
    };
  },

  // Extract QR code from URL
  extractFromUrl: (url: string): string => {
    if (!url) return '';
    
    try {
      // Handle different URL formats
      if (url.includes('/')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      }
      
      return url;
    } catch (error) {
      console.error('Error extracting QR code from URL:', error);
      return url;
    }
  },

  // Format QR code for display (last 20 characters)
  formatForDisplay: (qrCode: string): string => {
    if (!qrCode) return '';
    return qrCode.length > 20 ? qrCode.slice(-20) : qrCode;
  },

  // Check if QR code looks like a URL
  isUrl: (qrCode: string): boolean => {
    try {
      new URL(qrCode);
      return true;
    } catch {
      return false;
    }
  },

  // Check if QR code matches expected pattern
  matchesPattern: (qrCode: string, pattern?: RegExp): boolean => {
    if (!pattern) {
      // Default pattern: alphanumeric with hyphens
      pattern = /^[a-zA-Z0-9\-_]+$/;
    }
    
    return pattern.test(qrCode);
  },

  // Sanitize QR code input
  sanitize: (qrCode: string): string => {
    if (!qrCode) return '';
    
    return qrCode
      .trim()
      .replace(/\s+/g, '') // Remove all whitespace
      .replace(/[^\w\-_.:/]/g, ''); // Keep only safe characters
  },

  // Generate QR code hash for caching
  generateHash: (qrCode: string): string => {
    let hash = 0;
    if (qrCode.length === 0) return hash.toString();
    
    for (let i = 0; i < qrCode.length; i++) {
      const char = qrCode.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString();
  },

  // Check if QR code is from expected domain
  isFromExpectedDomain: (qrCode: string, expectedDomain: string = 'service.ptmes.cn'): boolean => {
    if (!qrCodeUtils.isUrl(qrCode)) return true; // Non-URL codes are acceptable
    
    try {
      const url = new URL(qrCode);
      return url.hostname === expectedDomain;
    } catch {
      return false;
    }
  },

  // Parse QR code data if it contains structured information
  parseStructuredData: (qrCode: string): Record<string, any> | null => {
    try {
      // Try to parse as JSON
      return JSON.parse(qrCode);
    } catch {
      // Try to parse as query string
      if (qrCode.includes('=')) {
        const params = new URLSearchParams(qrCode);
        const result: Record<string, any> = {};
        params.forEach((value, key) => {
          result[key] = value;
        });
        return Object.keys(result).length > 0 ? result : null;
      }
      
      return null;
    }
  },

  // Validate QR code against business rules
  validateBusinessRules: (qrCode: string): QRCodeValidationResult => {
    const formatResult = qrCodeUtils.validateFormat(qrCode);
    if (!formatResult.isValid) {
      return formatResult;
    }

    const sanitizedCode = qrCodeUtils.sanitize(qrCode);
    
    // Check if it's a URL and from expected domain
    if (qrCodeUtils.isUrl(sanitizedCode)) {
      if (!qrCodeUtils.isFromExpectedDomain(sanitizedCode)) {
        return {
          isValid: false,
          code: sanitizedCode,
          displayCode: qrCodeUtils.formatForDisplay(sanitizedCode),
          errorMessage: '二维码来源不可信',
        };
      }
    }

    // Check pattern matching
    const extractedCode = qrCodeUtils.isUrl(sanitizedCode) 
      ? qrCodeUtils.extractFromUrl(sanitizedCode)
      : sanitizedCode;

    if (!qrCodeUtils.matchesPattern(extractedCode)) {
      return {
        isValid: false,
        code: extractedCode,
        displayCode: qrCodeUtils.formatForDisplay(extractedCode),
        errorMessage: '二维码格式不符合要求',
      };
    }

    return {
      isValid: true,
      code: extractedCode,
      displayCode: qrCodeUtils.formatForDisplay(extractedCode),
    };
  },
};

// QR Code cache for performance
class QRCodeCache {
  private cache = new Map<string, { result: any; timestamp: number }>();
  private maxAge = 5 * 60 * 1000; // 5 minutes
  private maxSize = 100;

  set(key: string, value: any): void {
    // Clean old entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      result: value,
      timestamp: Date.now(),
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }

    return entry.result;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

export const qrCodeCache = new QRCodeCache();