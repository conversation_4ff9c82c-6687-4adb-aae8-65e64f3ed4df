import React from 'react';
import type { ApiError } from '../types';

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  retryable?: boolean;
  fallbackMessage?: string;
}

export interface RetryOptions {
  maxRetries?: number;
  delay?: number;
  backoff?: 'linear' | 'exponential';
  retryCondition?: (error: ApiError) => boolean;
}

export class ErrorHandler {
  private static instance: <PERSON>rrorHandler;
  private toastHandler?: (message: { type: 'error' | 'warning' | 'info'; title: string; message?: string }) => void;

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  setToastHandler(handler: (message: { type: 'error' | 'warning' | 'info'; title: string; message?: string }) => void): void {
    this.toastHandler = handler;
  }

  handleError(error: ApiError, options: ErrorHandlerOptions = {}): void {
    const {
      showToast = true,
      logError = true,
      fallbackMessage = '操作失败，请稍后重试'
    } = options;

    // Log error for debugging
    if (logError) {
      console.error('Error handled:', error);
    }

    // Get user-friendly error message
    const userMessage = this.getUserFriendlyMessage(error, fallbackMessage);

    // Show toast notification
    if (showToast && this.toastHandler) {
      this.toastHandler({
        type: this.getErrorType(error),
        title: userMessage.title,
        message: userMessage.message,
      });
    }
  }

  private getUserFriendlyMessage(error: ApiError, fallback: string): { title: string; message?: string } {
    // Network errors
    if (error.code === 'NETWORK_ERROR') {
      return {
        title: '网络连接失败',
        message: '请检查您的网络连接后重试',
      };
    }

    // Authentication errors
    if (error.status === 401) {
      return {
        title: '登录已过期',
        message: '请重新登录',
      };
    }

    // Permission errors
    if (error.status === 403) {
      return {
        title: '权限不足',
        message: '您没有执行此操作的权限',
      };
    }

    // Not found errors
    if (error.status === 404) {
      return {
        title: '资源不存在',
        message: '请求的资源未找到',
      };
    }

    // Server errors
    if (error.status && error.status >= 500) {
      return {
        title: '服务器错误',
        message: '服务器暂时无法处理请求，请稍后重试',
      };
    }

    // Validation errors
    if (error.status === 400) {
      return {
        title: '请求参数错误',
        message: error.message || '请检查输入信息',
      };
    }

    // Timeout errors
    if (error.code === 'TIMEOUT') {
      return {
        title: '请求超时',
        message: '请求处理时间过长，请稍后重试',
      };
    }

    // Rate limiting
    if (error.status === 429) {
      return {
        title: '请求过于频繁',
        message: '请稍后再试',
      };
    }

    // Default error message
    return {
      title: error.message || fallback,
    };
  }

  private getErrorType(error: ApiError): 'error' | 'warning' | 'info' {
    if (error.status === 401 || error.status === 403) {
      return 'warning';
    }
    
    if (error.code === 'NETWORK_ERROR') {
      return 'warning';
    }

    return 'error';
  }

  async retryOperation<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      delay = 1000,
      backoff = 'exponential',
      retryCondition = (error: ApiError) => this.isRetryableError(error),
    } = options;

    let lastError: ApiError;
    let currentDelay = delay;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as ApiError;

        // Don't retry if it's the last attempt or error is not retryable
        if (attempt === maxRetries || !retryCondition(lastError)) {
          throw lastError;
        }

        // Wait before retrying
        await this.sleep(currentDelay);

        // Adjust delay for next attempt
        if (backoff === 'exponential') {
          currentDelay *= 2;
        } else {
          currentDelay += delay;
        }
      }
    }

    throw lastError!;
  }

  private isRetryableError(error: ApiError): boolean {
    // Don't retry authentication or permission errors
    if (error.status === 401 || error.status === 403) {
      return false;
    }

    // Don't retry client errors (4xx except 408, 429)
    if (error.status && error.status >= 400 && error.status < 500) {
      return error.status === 408 || error.status === 429; // Timeout or rate limit
    }

    // Retry network errors and server errors
    return error.code === 'NETWORK_ERROR' || 
           (error.status && error.status >= 500) ||
           error.code === 'TIMEOUT';
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Specific error handlers for common scenarios
  handleNetworkError(): void {
    this.handleError({
      code: 'NETWORK_ERROR',
      message: 'Network connection failed',
    });
  }

  handleAuthError(): void {
    this.handleError({
      code: 'AUTH_ERROR',
      message: 'Authentication failed',
      status: 401,
    });
  }

  handleValidationError(message: string): void {
    this.handleError({
      code: 'VALIDATION_ERROR',
      message,
      status: 400,
    });
  }

  handleServerError(): void {
    this.handleError({
      code: 'SERVER_ERROR',
      message: 'Internal server error',
      status: 500,
    });
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Utility functions for common error scenarios
export const handleApiError = (error: ApiError, options?: ErrorHandlerOptions): void => {
  errorHandler.handleError(error, options);
};

export const retryApiCall = async <T>(
  operation: () => Promise<T>,
  options?: RetryOptions
): Promise<T> => {
  return errorHandler.retryOperation(operation, options);
};

// Error boundary helper
export const createErrorBoundary = (fallback: React.ComponentType<{ error: Error }>) => {
  return class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error }
  > {
    constructor(props: { children: React.ReactNode }) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      console.error('Error boundary caught error:', error, errorInfo);
      errorHandler.handleError({
        code: 'COMPONENT_ERROR',
        message: error.message,
        details: errorInfo,
      });
    }

    render() {
      if (this.state.hasError) {
        return React.createElement(fallback, { error: this.state.error! });
      }

      return this.props.children;
    }
  };
};