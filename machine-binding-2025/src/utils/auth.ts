import { User } from '../types';

// Token storage keys
const TOKEN_KEY = 'auth_token';
const USER_KEY = 'user_data';
const TOKEN_EXPIRY_KEY = 'token_expiry';

// Token management utilities
export const tokenUtils = {
  // Store token with expiry
  setToken: (token: string, expiresIn?: number): void => {
    localStorage.setItem(TOKEN_KEY, token);
    
    if (expiresIn) {
      const expiryTime = Date.now() + (expiresIn * 1000);
      localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
    }
  },

  // Get token if not expired
  getToken: (): string | null => {
    const token = localStorage.getItem(TOKEN_KEY);
    const expiry = localStorage.getItem(TOKEN_EXPIRY_KEY);

    if (!token) return null;

    // Check if token is expired
    if (expiry && Date.now() > parseInt(expiry)) {
      tokenUtils.clearToken();
      return null;
    }

    return token;
  },

  // Clear token and expiry
  clearToken: (): void => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  },

  // Check if token exists and is valid
  hasValidToken: (): boolean => {
    return tokenUtils.getToken() !== null;
  },

  // Get token expiry time
  getTokenExpiry: (): number | null => {
    const expiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
    return expiry ? parseInt(expiry) : null;
  },

  // Check if token will expire soon (within 5 minutes)
  isTokenExpiringSoon: (): boolean => {
    const expiry = tokenUtils.getTokenExpiry();
    if (!expiry) return false;
    
    const fiveMinutes = 5 * 60 * 1000;
    return (expiry - Date.now()) < fiveMinutes;
  },
};

// User data management utilities
export const userUtils = {
  // Store user data
  setUser: (user: User): void => {
    localStorage.setItem(USER_KEY, JSON.stringify(user));
  },

  // Get stored user data
  getUser: (): User | null => {
    try {
      const userData = localStorage.getItem(USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      userUtils.clearUser();
      return null;
    }
  },

  // Clear user data
  clearUser: (): void => {
    localStorage.removeItem(USER_KEY);
  },

  // Update specific user fields
  updateUser: (updates: Partial<User>): User | null => {
    const currentUser = userUtils.getUser();
    if (!currentUser) return null;

    const updatedUser = { ...currentUser, ...updates };
    userUtils.setUser(updatedUser);
    return updatedUser;
  },
};

// Authentication state utilities
export const authUtils = {
  // Clear all auth data
  clearAuthData: (): void => {
    tokenUtils.clearToken();
    userUtils.clearUser();
  },

  // Store complete auth data
  setAuthData: (token: string, user: User, expiresIn?: number): void => {
    tokenUtils.setToken(token, expiresIn);
    userUtils.setUser(user);
  },

  // Get complete auth data
  getAuthData: (): { token: string; user: User } | null => {
    const token = tokenUtils.getToken();
    const user = userUtils.getUser();

    if (!token || !user) {
      authUtils.clearAuthData();
      return null;
    }

    return { token, user };
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    const authData = authUtils.getAuthData();
    return authData !== null;
  },

  // Check if user has admin privileges
  isAdmin: (): boolean => {
    const user = userUtils.getUser();
    return user?.isAdmin || false;
  },

  // Get user role for route protection
  getUserRole: (): 'admin' | 'user' | null => {
    const user = userUtils.getUser();
    if (!user) return null;
    return user.isAdmin ? 'admin' : 'user';
  },
};

// Session management utilities
export const sessionUtils = {
  // Start session monitoring
  startSessionMonitoring: (onSessionExpired: () => void): (() => void) => {
    const checkSession = () => {
      if (!tokenUtils.hasValidToken()) {
        onSessionExpired();
        return;
      }

      // Check if token is expiring soon and needs refresh
      if (tokenUtils.isTokenExpiringSoon()) {
        console.warn('Token expiring soon, should refresh');
        // Could trigger token refresh here
      }
    };

    // Check session every minute
    const intervalId = setInterval(checkSession, 60000);

    // Return cleanup function
    return () => clearInterval(intervalId);
  },

  // Handle session expiry
  handleSessionExpiry: (): void => {
    authUtils.clearAuthData();
    
    // Redirect to login page
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  },

  // Extend session (called on user activity)
  extendSession: (): void => {
    const token = tokenUtils.getToken();
    if (token) {
      // Could make API call to refresh token here
      console.log('Session extended');
    }
  },
};

// Activity monitoring for session extension
export const activityUtils = {
  // Track user activity
  startActivityMonitoring: (): (() => void) => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    let lastActivity = Date.now();

    const handleActivity = () => {
      const now = Date.now();
      // Only extend session if more than 5 minutes since last activity
      if (now - lastActivity > 5 * 60 * 1000) {
        sessionUtils.extendSession();
      }
      lastActivity = now;
    };

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Return cleanup function
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  },
};

// Validation utilities
export const validationUtils = {
  // Validate email format
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate password strength
  isValidPassword: (password: string): { isValid: boolean; message?: string } => {
    if (password.length < 6) {
      return { isValid: false, message: '密码至少需要6位字符' };
    }

    if (password.length > 128) {
      return { isValid: false, message: '密码不能超过128位字符' };
    }

    return { isValid: true };
  },

  // Validate username
  isValidUsername: (username: string): { isValid: boolean; message?: string } => {
    if (!username.trim()) {
      return { isValid: false, message: '用户名不能为空' };
    }

    if (username.length < 2) {
      return { isValid: false, message: '用户名至少需要2位字符' };
    }

    if (username.length > 50) {
      return { isValid: false, message: '用户名不能超过50位字符' };
    }

    return { isValid: true };
  },
};