// Device detection and adaptation utilities

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isPhone: boolean;
  isPhoneLarge: boolean;
  isTabletSmall: boolean;
  isTabletLarge: boolean;
  isPortrait: boolean;
  isLandscape: boolean;
  isTouchDevice: boolean;
  isRetina: boolean;
  screenWidth: number;
  screenHeight: number;
  devicePixelRatio: number;
}

export const deviceUtils = {
  // Get current device information
  getDeviceInfo: (): DeviceInfo => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const isPortrait = height > width;
    const isLandscape = width > height;
    const devicePixelRatio = window.devicePixelRatio || 1;

    return {
      // Device type detection
      isMobile: width < 768,
      isTablet: width >= 768 && width < 1200,
      isDesktop: width >= 1200,
      isPhone: width < 480,
      isPhoneLarge: width >= 480 && width < 768,
      isTabletSmall: width >= 768 && width < 1024,
      isTabletLarge: width >= 1024 && width < 1200,
      
      // Orientation
      isPortrait,
      isLandscape,
      
      // Capabilities
      isTouchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      isRetina: devicePixelRatio > 1,
      
      // Dimensions
      screenWidth: width,
      screenHeight: height,
      devicePixelRatio,
    };
  },

  // Check if device is in a specific category
  isPhone: (): boolean => window.innerWidth < 480,
  isPhoneLarge: (): boolean => window.innerWidth >= 480 && window.innerWidth < 768,
  isMobile: (): boolean => window.innerWidth < 768,
  isTablet: (): boolean => window.innerWidth >= 768 && window.innerWidth < 1200,
  isDesktop: (): boolean => window.innerWidth >= 1200,
  
  // Orientation checks
  isPortrait: (): boolean => window.innerHeight > window.innerWidth,
  isLandscape: (): boolean => window.innerWidth > window.innerHeight,
  
  // Capability checks
  isTouchDevice: (): boolean => 'ontouchstart' in window || navigator.maxTouchPoints > 0,
  isRetina: (): boolean => (window.devicePixelRatio || 1) > 1,
  
  // Get optimal grid columns based on device and orientation
  getOptimalColumns: (maxColumns: number = 4): number => {
    const { isPhone, isPhoneLarge, isTabletSmall, isTabletLarge, isPortrait } = deviceUtils.getDeviceInfo();
    
    // Phone-specific logic
    if (isPhone) {
      return isPortrait ? 1 : 2;
    }
    
    if (isPhoneLarge) {
      return isPortrait ? 1 : Math.min(2, maxColumns);
    }
    
    // Tablet-specific logic
    if (isTabletSmall) {
      return isPortrait ? Math.min(2, maxColumns) : Math.min(3, maxColumns);
    }
    
    if (isTabletLarge) {
      return isPortrait ? Math.min(3, maxColumns) : Math.min(4, maxColumns);
    }
    
    // Desktop and larger
    return maxColumns;
  },

  // Get optimal columns for specific content types
  getOptimalColumnsForContent: (contentType: 'cards' | 'list' | 'grid' | 'form', maxColumns: number = 4): number => {
    const { isPhone, isPhoneLarge, isTabletSmall, isTabletLarge, isPortrait } = deviceUtils.getDeviceInfo();
    
    switch (contentType) {
      case 'cards':
        if (isPhone) return isPortrait ? 1 : 2;
        if (isPhoneLarge) return isPortrait ? 1 : 2;
        if (isTabletSmall) return isPortrait ? 2 : 3;
        if (isTabletLarge) return isPortrait ? 2 : 4;
        return maxColumns;
        
      case 'list':
        if (isPhone || isPhoneLarge) return 1;
        if (isTabletSmall) return isPortrait ? 1 : 2;
        if (isTabletLarge) return isPortrait ? 1 : 2;
        return Math.min(2, maxColumns);
        
      case 'grid':
        if (isPhone) return isPortrait ? 2 : 3;
        if (isPhoneLarge) return isPortrait ? 2 : 4;
        if (isTabletSmall) return isPortrait ? 3 : 4;
        if (isTabletLarge) return isPortrait ? 4 : 5;
        return maxColumns;
        
      case 'form':
        if (isPhone || isPhoneLarge) return 1;
        if (isTabletSmall) return isPortrait ? 1 : 2;
        if (isTabletLarge) return isPortrait ? 2 : 3;
        return Math.min(3, maxColumns);
        
      default:
        return deviceUtils.getOptimalColumns(maxColumns);
    }
  },

  // Get optimal touch target size
  getOptimalTouchTarget: (): number => {
    const { isMobile } = deviceUtils.getDeviceInfo();
    return isMobile ? 48 : 44; // 48px for mobile, 44px for desktop
  },

  // Get optimal spacing based on device and orientation
  getOptimalSpacing: (): { small: number; medium: number; large: number; xlarge: number } => {
    const { isPhone, isPhoneLarge, isTabletSmall, isTabletLarge, isPortrait } = deviceUtils.getDeviceInfo();
    
    if (isPhone) {
      return isPortrait 
        ? { small: 4, medium: 8, large: 16, xlarge: 24 }
        : { small: 2, medium: 4, large: 8, xlarge: 16 };
    }
    
    if (isPhoneLarge) {
      return isPortrait 
        ? { small: 8, medium: 16, large: 24, xlarge: 32 }
        : { small: 4, medium: 8, large: 16, xlarge: 24 };
    }
    
    if (isTabletSmall) {
      return isPortrait 
        ? { small: 12, medium: 20, large: 32, xlarge: 48 }
        : { small: 8, medium: 16, large: 24, xlarge: 40 };
    }
    
    if (isTabletLarge) {
      return isPortrait 
        ? { small: 16, medium: 24, large: 40, xlarge: 56 }
        : { small: 12, medium: 20, large: 32, xlarge: 48 };
    }
    
    // Desktop and larger
    return { small: 16, medium: 24, large: 48, xlarge: 64 };
  },

  // Get spacing for specific use cases
  getSpacingForContext: (context: 'tight' | 'normal' | 'comfortable' | 'spacious'): number => {
    const spacing = deviceUtils.getOptimalSpacing();
    
    switch (context) {
      case 'tight':
        return spacing.small;
      case 'normal':
        return spacing.medium;
      case 'comfortable':
        return spacing.large;
      case 'spacious':
        return spacing.xlarge;
      default:
        return spacing.medium;
    }
  },

  // Adaptive font size based on device
  getAdaptiveFontSize: (baseSize: number): number => {
    const { isMobile, devicePixelRatio } = deviceUtils.getDeviceInfo();
    
    let adaptedSize = baseSize;
    
    // Slightly smaller on mobile for better readability
    if (isMobile) {
      adaptedSize = Math.max(14, baseSize - 2);
    }
    
    // Adjust for high DPI displays
    if (devicePixelRatio > 2) {
      adaptedSize = Math.round(adaptedSize * 0.95);
    }
    
    return adaptedSize;
  },

  // Get safe area insets for devices with notches
  getSafeAreaInsets: () => {
    const style = getComputedStyle(document.documentElement);
    
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0'),
    };
  },

  // Debounced resize handler for performance
  onResize: (callback: (deviceInfo: DeviceInfo) => void, delay: number = 250) => {
    let timeoutId: number;
    
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => {
        callback(deviceUtils.getDeviceInfo());
      }, delay);
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    
    // Return cleanup function
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  },

  // Check if device supports specific features
  supportsFeature: (feature: 'hover' | 'touch' | 'orientation' | 'devicePixelRatio'): boolean => {
    switch (feature) {
      case 'hover':
        return window.matchMedia('(hover: hover)').matches;
      case 'touch':
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      case 'orientation':
        return 'orientation' in window || 'onorientationchange' in window;
      case 'devicePixelRatio':
        return 'devicePixelRatio' in window;
      default:
        return false;
    }
  },

  // Get viewport dimensions accounting for mobile browser UI
  getViewportDimensions: () => {
    // Use visualViewport API if available (better for mobile)
    if (window.visualViewport) {
      return {
        width: window.visualViewport.width,
        height: window.visualViewport.height,
      };
    }
    
    // Fallback to window dimensions
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  },

  // Check if device is in standalone mode (PWA)
  isStandalone: (): boolean => {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  },

  // Get device orientation angle
  getOrientationAngle: (): number => {
    return (screen as any).orientation?.angle || window.orientation || 0;
  },

  // Get layout configuration based on device and orientation
  getLayoutConfig: () => {
    const deviceInfo = deviceUtils.getDeviceInfo();
    const { isPhone, isPhoneLarge, isTabletSmall, isTabletLarge, isPortrait, isLandscape } = deviceInfo;
    
    return {
      // Grid configurations
      gridColumns: deviceUtils.getOptimalColumns(),
      cardColumns: deviceUtils.getOptimalColumnsForContent('cards'),
      listColumns: deviceUtils.getOptimalColumnsForContent('list'),
      formColumns: deviceUtils.getOptimalColumnsForContent('form'),
      
      // Spacing configurations
      spacing: deviceUtils.getOptimalSpacing(),
      
      // Layout preferences
      preferVerticalLayout: isPhone || (isTabletSmall && isPortrait),
      preferHorizontalLayout: isTabletLarge && isLandscape,
      useCompactLayout: isPhone || (isLandscape && !isTabletLarge),
      
      // Navigation preferences
      useBottomNavigation: isPhone && isPortrait,
      useTopNavigation: (isPhone && isLandscape) || isTabletSmall || isTabletLarge,
      useSideNavigation: false, // Reserved for desktop
      
      // Content preferences
      maxContentWidth: isPhone ? '100%' : isTabletSmall ? '600px' : isTabletLarge ? '800px' : '1200px',
      preferSingleColumn: isPhone || (isTabletSmall && isPortrait),
      preferTwoColumn: (isPhoneLarge && isLandscape) || (isTabletSmall && isLandscape) || (isTabletLarge && isPortrait),
      preferMultiColumn: isTabletLarge && isLandscape,
      
      // Interaction preferences
      touchTargetSize: deviceUtils.getOptimalTouchTarget(),
      useHoverEffects: deviceUtils.supportsFeature('hover'),
      useTouchFeedback: deviceUtils.supportsFeature('touch'),
      
      // Typography preferences
      baseFontSize: isPhone ? 14 : isTabletSmall ? 16 : 18,
      lineHeight: isPhone ? 1.5 : 1.6,
      
      // Device info for reference
      deviceInfo,
    };
  },

  // Get responsive breakpoint information
  getBreakpointInfo: () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      current: width < 480 ? 'phone' : 
               width < 768 ? 'phone-large' :
               width < 1024 ? 'tablet-small' :
               width < 1200 ? 'tablet-large' : 'desktop',
      width,
      height,
      isPhone: width < 480,
      isPhoneLarge: width >= 480 && width < 768,
      isTabletSmall: width >= 768 && width < 1024,
      isTabletLarge: width >= 1024 && width < 1200,
      isDesktop: width >= 1200,
      isPortrait: height > width,
      isLandscape: width > height,
    };
  },

  // Adaptive image sizing
  getOptimalImageSize: (baseWidth: number, baseHeight: number) => {
    const { isPhone, isPhoneLarge, isTabletSmall, isTabletLarge, isPortrait, devicePixelRatio } = deviceUtils.getDeviceInfo();
    
    let scaleFactor = 1;
    
    if (isPhone) {
      scaleFactor = isPortrait ? 0.8 : 0.6;
    } else if (isPhoneLarge) {
      scaleFactor = isPortrait ? 0.9 : 0.7;
    } else if (isTabletSmall) {
      scaleFactor = isPortrait ? 1.0 : 0.8;
    } else if (isTabletLarge) {
      scaleFactor = isPortrait ? 1.1 : 1.0;
    }
    
    // Adjust for high DPI displays
    if (devicePixelRatio > 2) {
      scaleFactor *= 1.2;
    }
    
    return {
      width: Math.round(baseWidth * scaleFactor),
      height: Math.round(baseHeight * scaleFactor),
      scaleFactor,
    };
  },

  // Get optimal modal/dialog sizing
  getOptimalModalSize: () => {
    const { isPhone, isPhoneLarge, isTabletSmall, isTabletLarge, isPortrait } = deviceUtils.getDeviceInfo();
    
    if (isPhone) {
      return {
        width: isPortrait ? '95%' : '90%',
        height: isPortrait ? '80%' : '85%',
        maxWidth: isPortrait ? '350px' : '500px',
        maxHeight: isPortrait ? '600px' : '400px',
        fullScreen: isPortrait,
      };
    }
    
    if (isPhoneLarge) {
      return {
        width: isPortrait ? '90%' : '85%',
        height: isPortrait ? '75%' : '80%',
        maxWidth: isPortrait ? '400px' : '600px',
        maxHeight: isPortrait ? '650px' : '450px',
        fullScreen: false,
      };
    }
    
    if (isTabletSmall) {
      return {
        width: isPortrait ? '80%' : '75%',
        height: isPortrait ? '70%' : '75%',
        maxWidth: isPortrait ? '500px' : '700px',
        maxHeight: isPortrait ? '700px' : '500px',
        fullScreen: false,
      };
    }
    
    if (isTabletLarge) {
      return {
        width: isPortrait ? '70%' : '65%',
        height: isPortrait ? '65%' : '70%',
        maxWidth: isPortrait ? '600px' : '800px',
        maxHeight: isPortrait ? '750px' : '600px',
        fullScreen: false,
      };
    }
    
    // Desktop
    return {
      width: '60%',
      height: '60%',
      maxWidth: '800px',
      maxHeight: '600px',
      fullScreen: false,
    };
  },
};

// React hook for device information
export const useDeviceInfo = () => {
  const [deviceInfo, setDeviceInfo] = React.useState<DeviceInfo>(() => 
    deviceUtils.getDeviceInfo()
  );

  React.useEffect(() => {
    const cleanup = deviceUtils.onResize((newDeviceInfo) => {
      setDeviceInfo(newDeviceInfo);
    });

    return cleanup;
  }, []);

  return deviceInfo;
};

// React hook for specific device checks
export const useDeviceType = () => {
  const deviceInfo = useDeviceInfo();
  
  return {
    isMobile: deviceInfo.isMobile,
    isTablet: deviceInfo.isTablet,
    isDesktop: deviceInfo.isDesktop,
    isPhone: deviceInfo.isPhone,
    isPortrait: deviceInfo.isPortrait,
    isLandscape: deviceInfo.isLandscape,
    isTouchDevice: deviceInfo.isTouchDevice,
  };
};

// React hook for adaptive values
export const useAdaptiveValues = () => {
  return {
    columns: deviceUtils.getOptimalColumns(),
    touchTarget: deviceUtils.getOptimalTouchTarget(),
    spacing: deviceUtils.getOptimalSpacing(),
    fontSize: (baseSize: number) => deviceUtils.getAdaptiveFontSize(baseSize),
    layoutConfig: deviceUtils.getLayoutConfig(),
    breakpointInfo: deviceUtils.getBreakpointInfo(),
    imageSize: (width: number, height: number) => deviceUtils.getOptimalImageSize(width, height),
    modalSize: deviceUtils.getOptimalModalSize(),
  };
};

// React hook for layout configuration
export const useLayoutConfig = () => {
  const [layoutConfig, setLayoutConfig] = React.useState(() => deviceUtils.getLayoutConfig());
  
  React.useEffect(() => {
    const updateLayout = () => {
      setLayoutConfig(deviceUtils.getLayoutConfig());
    };
    
    const cleanup = deviceUtils.onResize(updateLayout);
    return cleanup;
  }, []);
  
  return layoutConfig;
};

// React hook for responsive grid columns
export const useResponsiveColumns = (contentType: 'cards' | 'list' | 'grid' | 'form' = 'cards', maxColumns: number = 4) => {
  const [columns, setColumns] = React.useState(() => 
    deviceUtils.getOptimalColumnsForContent(contentType, maxColumns)
  );
  
  React.useEffect(() => {
    const updateColumns = () => {
      setColumns(deviceUtils.getOptimalColumnsForContent(contentType, maxColumns));
    };
    
    const cleanup = deviceUtils.onResize(updateColumns);
    return cleanup;
  }, [contentType, maxColumns]);
  
  return columns;
};

// React hook for orientation changes
export const useOrientation = () => {
  const [orientation, setOrientation] = React.useState(() => ({
    isPortrait: deviceUtils.isPortrait(),
    isLandscape: deviceUtils.isLandscape(),
    angle: deviceUtils.getOrientationAngle(),
  }));
  
  React.useEffect(() => {
    const updateOrientation = () => {
      setOrientation({
        isPortrait: deviceUtils.isPortrait(),
        isLandscape: deviceUtils.isLandscape(),
        angle: deviceUtils.getOrientationAngle(),
      });
    };
    
    window.addEventListener('orientationchange', updateOrientation);
    window.addEventListener('resize', updateOrientation);
    
    return () => {
      window.removeEventListener('orientationchange', updateOrientation);
      window.removeEventListener('resize', updateOrientation);
    };
  }, []);
  
  return orientation;
};

// CSS-in-JS helper for media queries
export const mediaQueries = {
  mobile: `(max-width: 767px)`,
  mobilePortrait: `(max-width: 767px) and (orientation: portrait)`,
  mobileLandscape: `(max-width: 767px) and (orientation: landscape)`,
  tablet: `(min-width: 768px) and (max-width: 1199px)`,
  tabletPortrait: `(min-width: 768px) and (max-width: 1199px) and (orientation: portrait)`,
  tabletLandscape: `(min-width: 768px) and (max-width: 1199px) and (orientation: landscape)`,
  desktop: `(min-width: 1200px)`,
  touch: `(hover: none) and (pointer: coarse)`,
  hover: `(hover: hover) and (pointer: fine)`,
  retina: `(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)`,
};

// Add React import for hooks
import React from 'react';