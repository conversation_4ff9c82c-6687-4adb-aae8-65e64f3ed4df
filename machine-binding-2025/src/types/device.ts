export interface DeviceBinding {
  id: string;
  qrCode: string;
  brand: string;
  serialNumber: string;
  engineer: string;
  nameplatePhotos: string[];
  wholePhotos: string[];
  gpsPhotos: string[];
  createdAt: string;
  status: 'pending' | 'confirmed' | 'rejected';
  customerName?: string;
}

export interface SAPDeviceData {
  equnr: string;
  typbz: string;
  enduser_name: string;
  enduser: string;
  fl_street: string;
  pgroup_name: string;
  sales_off: string;
  plangroup: string;
  inbdt: string;
  end_date: string;
}

export interface DeviceBindingFormData {
  qrCode: string;
  brand: string;
  serialNumber: string;
  nameplatePhotos: File[];
  wholePhotos: File[];
  gpsPhotos: File[];
}

export interface QRCodeValidationResponse {
  id: string;
  qrcode_url: string;
  created_at: string | null;
  updated_at: string | null;
  detail?: string;
}

export interface BindingRecord {
  id: string;
  name: string;
  time: string;
  code: string;
  customer_name?: string;
  serial_num: string;
  created_at: string;
}