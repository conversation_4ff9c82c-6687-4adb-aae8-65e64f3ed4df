export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: number;
  success?: boolean;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  status?: number;
}

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
}