export interface FormValidation {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormField<T = any> {
  value: T;
  error: string | null;
  touched: boolean;
  required: boolean;
}

export interface FormState<T = Record<string, any>> {
  values: T;
  errors: Record<keyof T, string>;
  touched: Record<keyof T, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

export type ValidationRule<T = any> = (value: T) => string | null;

export interface FieldConfig<T = any> {
  initialValue: T;
  required?: boolean;
  validators?: ValidationRule<T>[];
}