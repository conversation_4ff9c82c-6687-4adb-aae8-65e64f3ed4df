export interface User {
  id: string;
  name: string;
  userName: string;
  email: string;
  department: string;
  headerImg: string;
  isAdmin: boolean;
  created_at?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}