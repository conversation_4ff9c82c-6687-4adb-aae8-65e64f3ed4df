export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  autoClose?: boolean;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnBackdrop?: boolean;
  showCloseButton?: boolean;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface UIState {
  toasts: ToastMessage[];
  modals: Record<string, boolean>;
  loading: LoadingState;
}

export interface ActionGridItem {
  id: string;
  text: string;
  icon: string;
  iconSize?: string | number;
  iconColor?: string;
  link: string;
  disabled?: boolean;
  badgeCount?: number;
}

export interface GridConfig {
  show: boolean;
  iconMain: string;
  data: ActionGridItem[];
  line: number;
  customStyle?: React.CSSProperties;
  gridBoxStyle?: React.CSSProperties;
}

export interface CarouselItem {
  id?: string;
  image: string;
  title?: string;
  description?: string;
  link?: string;
}