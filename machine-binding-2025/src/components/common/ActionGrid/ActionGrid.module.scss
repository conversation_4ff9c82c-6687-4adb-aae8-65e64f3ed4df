@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.actionGrid {
  @include adaptive-content-area;
  width: 100%;
  
  // Enhanced adaptive grid with orientation support
  @include orientation-adaptive(1, 2, 2, 3);
  
  // Device and orientation specific spacing
  @include mobile-portrait {
    gap: $spacing-md;
    padding: $spacing-md;
    
    // Optimize for single-handed use
    .actionButton {
      min-height: 80px;
    }
  }
  
  @include mobile-landscape {
    gap: $spacing-sm;
    padding: $spacing-sm $spacing-md;
    
    // More compact in landscape
    .actionButton {
      min-height: 60px;
      padding: $spacing-sm;
    }
  }
  
  @include tablet-portrait {
    gap: $spacing-lg;
    padding: $spacing-lg;
    max-width: 600px;
    margin: 0 auto;
  }
  
  @include tablet-landscape {
    gap: $spacing-lg;
    padding: $spacing-md $spacing-xl;
    max-width: 900px;
    margin: 0 auto;
  }
}

// Enhanced grid layouts with device and orientation awareness
.actionGrid.cols2 {
  @include orientation-adaptive(1, 2, 2, 2);
}

.actionGrid.cols3 {
  @include orientation-adaptive(1, 2, 2, 3);
}

.actionGrid.cols4 {
  @include orientation-adaptive(1, 2, 3, 4);
  
  @include phone-only {
    grid-template-columns: 1fr;
  }
}

.actionGrid.cols5 {
  @include orientation-adaptive(2, 3, 3, 5);
  
  @include phone-only {
    grid-template-columns: 1fr;
  }
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  border: none;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: $text-primary;
  position: relative;
  overflow: hidden;
  
  // Adaptive sizing and spacing
  @include phone-optimized;
  @include tablet-optimized;
  @include touch-target;
  
  // Default sizing
  padding: $spacing-lg;
  min-height: 100px;
  
  // Enhanced mobile optimizations with orientation support
  @include mobile-portrait {
    padding: $spacing-md;
    min-height: 80px;
    flex-direction: column;
    text-align: center;
    justify-content: center;
    
    // Larger touch targets in portrait
    @include touch-target($touch-target-comfortable);
    
    // Optimize for thumb reach
    .actionIcon {
      font-size: 2rem;
      margin-bottom: $spacing-sm;
    }
    
    .actionText {
      font-size: $font-size-sm;
      line-height: 1.3;
    }
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-md;
    min-height: 60px;
    
    // Horizontal layout in landscape
    flex-direction: row;
    text-align: left;
    justify-content: flex-start;
    align-items: center;
    gap: $spacing-sm;
    
    .actionIcon {
      font-size: 1.5rem;
      margin-bottom: 0;
      flex-shrink: 0;
    }
    
    .actionText {
      font-size: $font-size-xs;
      line-height: 1.2;
      flex: 1;
    }
  }
  
  // Tablet optimizations
  @include tablet-portrait {
    padding: $spacing-lg;
    min-height: 120px;
  }
  
  @include tablet-landscape {
    padding: $spacing-lg $spacing-xl;
    min-height: 100px;
  }
  
  // Hover effects only on hover-capable devices
  @include hover-device {
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-md;
      background: rgba($primary-color, 0.02);
    }
  }
  
  // Touch feedback for touch devices
  @include touch-device {
    &:active {
      transform: scale(0.98);
      background: rgba($primary-color, 0.05);
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: $shadow-sm;
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: $shadow-sm;
  }
}

.actionIcon {
  font-size: 2.5rem;
  margin-bottom: $spacing-md;
  transition: transform 0.3s ease;
}

.actionButton:hover .actionIcon {
  transform: scale(1.1);
}

.actionText {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  text-align: center;
  margin: 0;
  line-height: 1.2;
}

// Badge for notifications
.badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: $danger-color;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: $font-weight-bold;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

// Loading state
.actionButton.loading {
  .actionIcon,
  .actionText {
    opacity: 0.5;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Compact variant
.actionGrid.compact {
  gap: $spacing-md;
  padding: $spacing-md;

  .actionButton {
    padding: $spacing-md;
    min-height: 80px;
  }

  .actionIcon {
    font-size: 2rem;
    margin-bottom: $spacing-sm;
  }

  .actionText {
    font-size: $font-size-sm;
  }
}

// Large variant
.actionGrid.large {
  gap: $spacing-xl;
  padding: $spacing-xl;

  .actionButton {
    padding: $spacing-xl;
    min-height: 120px;
  }

  .actionIcon {
    font-size: 3rem;
    margin-bottom: $spacing-lg;
  }

  .actionText {
    font-size: $font-size-lg;
  }
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .actionGrid {
    gap: $spacing-md;
    padding: $spacing-md;
  }

  // Force 2 columns on mobile for better touch targets
  .actionGrid.cols3,
  .actionGrid.cols4,
  .actionGrid.cols5 {
    grid-template-columns: repeat(2, 1fr);
  }

  .actionButton {
    min-height: $touch-target-min * 2;
    padding: $spacing-md;
  }

  .actionIcon {
    font-size: 2rem;
    margin-bottom: $spacing-sm;
  }

  .actionText {
    font-size: $font-size-sm;
  }

  .badge {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .actionGrid.cols4,
  .actionGrid.cols5 {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Desktop optimizations
@media (min-width: $tablet-large) {
  .actionGrid {
    max-width: 800px;
    margin: 0 auto;
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

// Ripple effect for touch feedback
.actionButton {
  position: relative;
  overflow: hidden;
}

.actionButton::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba($primary-color, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.actionButton:active::before {
  width: 300px;
  height: 300px;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .actionButton,
  .actionIcon,
  .badge {
    animation: none;
    transition: none;
  }

  .actionButton:hover {
    transform: none;
  }

  .actionButton:hover .actionIcon {
    transform: none;
  }
}