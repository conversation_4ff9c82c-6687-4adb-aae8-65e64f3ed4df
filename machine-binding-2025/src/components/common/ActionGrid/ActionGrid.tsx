import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ActionGridItem, BaseComponentProps } from '../../../types';
import styles from './ActionGrid.module.scss';

export interface ActionGridProps extends BaseComponentProps {
  items: ActionGridItem[];
  columns?: 2 | 3 | 4 | 5;
  variant?: 'default' | 'compact' | 'large';
  loading?: boolean;
  onItemClick?: (item: ActionGridItem) => void;
}

export const ActionGrid: React.FC<ActionGridProps> = ({
  items = [],
  columns = 3,
  variant = 'default',
  loading = false,
  onItemClick,
  className = '',
  testId,
}) => {
  const navigate = useNavigate();

  const handleItemClick = (item: ActionGridItem) => {
    if (item.disabled || loading) return;

    if (onItemClick) {
      onItemClick(item);
    } else if (item.link) {
      // Handle navigation
      if (item.link.startsWith('http')) {
        // External link
        window.open(item.link, '_blank', 'noopener,noreferrer');
      } else {
        // Internal navigation
        navigate(item.link);
      }
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent, item: ActionGridItem) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemClick(item);
    }
  };

  const gridClasses = [
    styles.actionGrid,
    styles[`cols${columns}`],
    styles[variant],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  if (items.length === 0) {
    return (
      <div className={gridClasses} data-testid={testId}>
        <div style={{ 
          gridColumn: '1 / -1', 
          textAlign: 'center', 
          padding: '40px', 
          color: '#666' 
        }}>
          暂无功能项
        </div>
      </div>
    );
  }

  return (
    <div className={gridClasses} data-testid={testId}>
      {items.map((item) => {
        const buttonClasses = [
          styles.actionButton,
          loading && styles.loading,
        ]
          .filter(Boolean)
          .join(' ');

        return (
          <button
            key={item.id}
            className={buttonClasses}
            onClick={() => handleItemClick(item)}
            onKeyDown={(e) => handleKeyDown(e, item)}
            disabled={item.disabled || loading}
            aria-label={item.text}
            data-testid={`${testId}-item-${item.id}`}
          >
            <div 
              className={styles.actionIcon}
              style={{ 
                color: item.iconColor,
                fontSize: item.iconSize ? `${item.iconSize}px` : undefined 
              }}
            >
              {item.icon}
            </div>
            
            <span className={styles.actionText}>
              {item.text}
            </span>

            {/* Badge for notifications */}
            {item.badgeCount && item.badgeCount > 0 && (
              <span className={styles.badge}>
                {item.badgeCount > 99 ? '99+' : item.badgeCount}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
};

// Predefined action grids for common use cases
export const HomeActionGrid: React.FC<{
  onDeviceBinding?: () => void;
  onBindingRecords?: () => void;
  onProfile?: () => void;
  bindingRecordsCount?: number;
}> = ({
  onDeviceBinding,
  onBindingRecords,
  onProfile,
  bindingRecordsCount,
}) => {
  const items: ActionGridItem[] = [
    {
      id: 'device-binding',
      text: '绑定设备',
      icon: '📱',
      iconSize: 40,
      iconColor: '#007bff',
      link: '/device-binding',
    },
    {
      id: 'binding-records',
      text: '绑定记录',
      icon: '📋',
      iconSize: 40,
      iconColor: '#28a745',
      link: '/binding-records',
      badgeCount: bindingRecordsCount,
    },
    {
      id: 'profile',
      text: '个人中心',
      icon: '👤',
      iconSize: 40,
      iconColor: '#6c757d',
      link: '/profile',
    },
  ];

  const handleItemClick = (item: ActionGridItem) => {
    switch (item.id) {
      case 'device-binding':
        onDeviceBinding?.();
        break;
      case 'binding-records':
        onBindingRecords?.();
        break;
      case 'profile':
        onProfile?.();
        break;
    }
  };

  return (
    <ActionGrid
      items={items}
      columns={3}
      onItemClick={handleItemClick}
      testId="home-action-grid"
    />
  );
};

export const AdminActionGrid: React.FC<{
  onItemClick?: (item: ActionGridItem) => void;
}> = ({ onItemClick }) => {
  const items: ActionGridItem[] = [
    {
      id: 'account-management',
      text: '账号管理',
      icon: '👥',
      iconSize: 40,
      iconColor: '#007bff',
      link: '/admin/accounts',
    },
    {
      id: 'department-management',
      text: '部门管理',
      icon: '🏢',
      iconSize: 40,
      iconColor: '#28a745',
      link: '/admin/departments',
    },
    {
      id: 'device-management',
      text: '设备管理',
      icon: '⚙️',
      iconSize: 40,
      iconColor: '#ffc107',
      link: '/admin/devices',
    },
    {
      id: 'task-management',
      text: '任务管理',
      icon: '📝',
      iconSize: 40,
      iconColor: '#dc3545',
      link: '/admin/tasks',
    },
    {
      id: 'maintenance-management',
      text: '保养管理',
      icon: '🔧',
      iconSize: 40,
      iconColor: '#17a2b8',
      link: '/admin/maintenance',
    },
    {
      id: 'fault-categories',
      text: '故障类别',
      icon: '⚠️',
      iconSize: 40,
      iconColor: '#fd7e14',
      link: '/admin/fault-categories',
    },
    {
      id: 'status-management',
      text: '状态管理',
      icon: '📊',
      iconSize: 40,
      iconColor: '#6f42c1',
      link: '/admin/status',
    },
  ];

  return (
    <ActionGrid
      items={items}
      columns={4}
      variant="compact"
      onItemClick={onItemClick}
      testId="admin-action-grid"
    />
  );
};