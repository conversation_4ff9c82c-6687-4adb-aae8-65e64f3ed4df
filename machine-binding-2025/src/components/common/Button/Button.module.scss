@use 'sass:color';
@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: $touch-target-min;
  padding: $spacing-sm $spacing-lg;
  border: none;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  @include focus-style;

  // Button variants
  &.primary {
    background-color: $primary-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust($primary-color, $lightness: -10%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($primary-color, $lightness: -15%);
    }
  }

  &.secondary {
    background-color: $secondary-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust($secondary-color, $lightness: -10%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust($secondary-color, $lightness: -15%);
    }
  }

  &.success {
    background-color: $success-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust($success-color, $lightness: -10%);
    }
  }

  &.danger {
    background-color: $danger-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust($danger-color, $lightness: -10%);
    }
  }

  &.outline {
    background-color: transparent;
    border: 2px solid $primary-color;
    color: $primary-color;

    &:hover:not(:disabled) {
      background-color: $primary-color;
      color: white;
    }
  }

  &.ghost {
    background-color: transparent;
    color: $primary-color;

    &:hover:not(:disabled) {
      background-color: rgba($primary-color, 0.1);
    }
  }

  // Button sizes
  &.small {
    @include button-size('sm');
  }

  &.medium {
    @include button-size('md');
  }

  &.large {
    @include button-size('lg');
  }

  &.fullWidth {
    width: 100%;
  }

  // Loading state
  &.loading {
    @include loading-state;
    color: transparent;
  }

  // Icon spacing
  .icon {
    margin-right: $spacing-xs;
    
    &:last-child {
      margin-right: 0;
      margin-left: $spacing-xs;
    }

    &:only-child {
      margin: 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Mobile optimizations
@include mobile-only {
  .button {
    @include touch-target;
    
    // Ensure adequate spacing between buttons on mobile
    & + & {
      margin-top: $spacing-sm;
    }
    
    // Better touch feedback
    &:active {
      transform: scale(0.98);
    }
  }
}