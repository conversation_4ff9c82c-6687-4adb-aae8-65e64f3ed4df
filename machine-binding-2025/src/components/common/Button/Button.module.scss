@use 'sass:color';
@use '../../../styles/variables' as vars;
@use '../../../styles/mixins' as mixins;

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: vars.$touch-target-min;
  padding: vars.$spacing-sm vars.$spacing-lg;
  border: none;
  border-radius: vars.$border-radius-md;
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  @include mixins.focus-style;

  // Button variants
  &.primary {
    background-color: vars.$primary-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust(vars.$primary-color, vars.$lightness: -10%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust(vars.$primary-color, vars.$lightness: -15%);
    }
  }

  &.secondary {
    background-color: vars.$secondary-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust(vars.$secondary-color, vars.$lightness: -10%);
    }

    &:active:not(:disabled) {
      background-color: color.adjust(vars.$secondary-color, vars.$lightness: -15%);
    }
  }

  &.success {
    background-color: vars.$success-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust(vars.$success-color, vars.$lightness: -10%);
    }
  }

  &.danger {
    background-color: vars.$danger-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust(vars.$danger-color, vars.$lightness: -10%);
    }
  }

  &.outline {
    background-color: transparent;
    border: 2px solid vars.$primary-color;
    color: vars.$primary-color;

    &:hover:not(:disabled) {
      background-color: vars.$primary-color;
      color: white;
    }
  }

  &.ghost {
    background-color: transparent;
    color: vars.$primary-color;

    &:hover:not(:disabled) {
      background-color: rgba(vars.$primary-color, 0.1);
    }
  }

  // Button sizes
  &.small {
    @include mixins.button-size('sm');
  }

  &.medium {
    @include mixins.button-size('md');
  }

  &.large {
    @include mixins.button-size('lg');
  }

  &.fullWidth {
    width: 100%;
  }

  // Loading state
  &.loading {
    @include mixins.loading-state;
    color: transparent;
  }

  // Icon spacing
  .icon {
    margin-right: vars.$spacing-xs;

    &:last-child {
      margin-right: 0;
      margin-left: vars.$spacing-xs;
    }

    &:only-child {
      margin: 0;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Mobile optimizations
@include mixins.mobile-only {
  .button {
    @include mixins.touch-target;

    // Ensure adequate spacing between buttons on mobile
    &+& {
      margin-top: vars.$spacing-sm;
    }

    // Better touch feedback
    &:active {
      transform: scale(0.98);
    }
  }
}