@use "../../../styles/variables" as vars;

.toastContainer {
  position: fixed;
  top: 80px; // Below navigation bar
  right: vars.$spacing-lg;
  z-index: vars.$z-index-tooltip;
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-sm;
  max-width: 400px;
  width: calc(100vw - #{vars.$spacing-lg * 2});
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: vars.$spacing-sm;
  padding: vars.$spacing-md;
  border-radius: vars.$border-radius-md;
  box-shadow: vars.$shadow-lg;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(white, 0.2);
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.toast.success {
  background: rgba(vars.$success-color, 0.95);
  color: white;
  border-color: rgba(vars.$success-color, 0.3);
}

.toast.error {
  background: rgba(vars.$danger-color, 0.95);
  color: white;
  border-color: rgba(vars.$danger-color, 0.3);
}

.toast.warning {
  background: rgba(vars.$warning-color, 0.95);
  color: vars.$text-primary;
  border-color: rgba(vars.$warning-color, 0.3);
}

.toast.info {
  background: rgba(vars.$info-color, 0.95);
  color: white;
  border-color: rgba(vars.$info-color, 0.3);
}

.icon {
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.content {
  flex: 1;
  min-width: 0;
}

.title {
  font-weight: vars.$font-weight-semibold;
  font-size: vars.$font-size-md;
  margin: 0 0 vars.$spacing-xs 0;
  line-height: 1.2;
}

.message {
  font-size: vars.$font-size-sm;
  margin: 0;
  opacity: 0.9;
  line-height: 1.4;
}

.closeButton {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: vars.$spacing-xs;
  border-radius: vars.$border-radius-sm;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  font-size: 16px;
  line-height: 1;

  &:hover {
    opacity: 1;
  }

  &:focus {
    outline: 2px solid rgba(white, 0.5);
    outline-offset: 2px;
  }
}

// Progress bar for auto-close
.progressBar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(white, 0.3);
  animation: progress linear;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .toastContainer {
    top: 70px;
    right: vars.$spacing-md;
    left: vars.$spacing-md;
    width: auto;
    max-width: none;
  }

  .toast {
    padding: vars.$spacing-sm vars.$spacing-md;
  }

  .title {
    font-size: vars.$font-size-sm;
  }

  .message {
    font-size: vars.$font-size-xs;
  }
}

// Animations
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.toast.exiting {
  animation: slideOut 0.3s ease-in forwards;
}