@import '../../../styles/variables.scss';

.toastContainer {
  position: fixed;
  top: 80px; // Below navigation bar
  right: $spacing-lg;
  z-index: $z-index-tooltip;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
  max-width: 400px;
  width: calc(100vw - #{$spacing-lg * 2});
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;
  padding: $spacing-md;
  border-radius: $border-radius-md;
  box-shadow: $shadow-lg;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(white, 0.2);
  animation: slideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.toast.success {
  background: rgba($success-color, 0.95);
  color: white;
  border-color: rgba($success-color, 0.3);
}

.toast.error {
  background: rgba($danger-color, 0.95);
  color: white;
  border-color: rgba($danger-color, 0.3);
}

.toast.warning {
  background: rgba($warning-color, 0.95);
  color: $text-primary;
  border-color: rgba($warning-color, 0.3);
}

.toast.info {
  background: rgba($info-color, 0.95);
  color: white;
  border-color: rgba($info-color, 0.3);
}

.icon {
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.content {
  flex: 1;
  min-width: 0;
}

.title {
  font-weight: $font-weight-semibold;
  font-size: $font-size-md;
  margin: 0 0 $spacing-xs 0;
  line-height: 1.2;
}

.message {
  font-size: $font-size-sm;
  margin: 0;
  opacity: 0.9;
  line-height: 1.4;
}

.closeButton {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: $border-radius-sm;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  font-size: 16px;
  line-height: 1;

  &:hover {
    opacity: 1;
  }

  &:focus {
    outline: 2px solid rgba(white, 0.5);
    outline-offset: 2px;
  }
}

// Progress bar for auto-close
.progressBar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(white, 0.3);
  animation: progress linear;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .toastContainer {
    top: 70px;
    right: $spacing-md;
    left: $spacing-md;
    width: auto;
    max-width: none;
  }

  .toast {
    padding: $spacing-sm $spacing-md;
  }

  .title {
    font-size: $font-size-sm;
  }

  .message {
    font-size: $font-size-xs;
  }
}

// Animations
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.toast.exiting {
  animation: slideOut 0.3s ease-in forwards;
}