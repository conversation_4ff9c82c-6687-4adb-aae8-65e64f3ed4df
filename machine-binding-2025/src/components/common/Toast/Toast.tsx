import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useToast } from '../../../hooks';
import { ToastMessage } from '../../../types';
import styles from './Toast.module.scss';

const getToastIcon = (type: ToastMessage['type']): string => {
  switch (type) {
    case 'success':
      return '✅';
    case 'error':
      return '❌';
    case 'warning':
      return '⚠️';
    case 'info':
      return 'ℹ️';
    default:
      return 'ℹ️';
  }
};

interface ToastItemProps {
  toast: ToastMessage;
  onClose: (id: string) => void;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, onClose }) => {
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (toast.autoClose && toast.duration) {
      const timer = setTimeout(() => {
        handleClose();
      }, toast.duration);

      return () => clearTimeout(timer);
    }
  }, [toast.autoClose, toast.duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(toast.id);
    }, 300); // Match animation duration
  };

  return (
    <div
      className={`${styles.toast} ${styles[toast.type]} ${isExiting ? styles.exiting : ''}`}
      role="alert"
      aria-live="polite"
    >
      <div className={styles.icon}>
        {getToastIcon(toast.type)}
      </div>
      
      <div className={styles.content}>
        <div className={styles.title}>{toast.title}</div>
        {toast.message && (
          <div className={styles.message}>{toast.message}</div>
        )}
      </div>

      <button
        className={styles.closeButton}
        onClick={handleClose}
        aria-label="关闭通知"
      >
        ×
      </button>

      {toast.autoClose && toast.duration && (
        <div
          className={styles.progressBar}
          style={{
            animationDuration: `${toast.duration}ms`,
          }}
        />
      )}
    </div>
  );
};

export const ToastContainer: React.FC = () => {
  const { toasts, hideToast } = useToast();

  // Create portal to render toasts at document body level
  return createPortal(
    <div className={styles.toastContainer}>
      {toasts.map((toast) => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onClose={hideToast}
        />
      ))}
    </div>,
    document.body
  );
};