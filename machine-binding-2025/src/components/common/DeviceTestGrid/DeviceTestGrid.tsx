import React from 'react';
import { useDeviceInfo, useLayoutConfig, useResponsiveColumns, useOrientation } from '../../../utils/device';
import styles from './DeviceTestGrid.module.scss';

interface DeviceTestGridProps {
  contentType?: 'cards' | 'list' | 'grid' | 'form';
  maxColumns?: number;
}

const DeviceTestGrid: React.FC<DeviceTestGridProps> = ({ 
  contentType = 'cards', 
  maxColumns = 4 
}) => {
  const deviceInfo = useDeviceInfo();
  const layoutConfig = useLayoutConfig();
  const columns = useResponsiveColumns(contentType, maxColumns);
  const orientation = useOrientation();

  const testItems = Array.from({ length: 12 }, (_, i) => ({
    id: i + 1,
    title: `Item ${i + 1}`,
    description: `Test item for ${contentType} layout`,
  }));

  return (
    <div className={styles.deviceTestGrid}>
      <div className={styles.deviceInfo}>
        <h3>Device Information</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>Device Type:</span>
            <span className={styles.value}>
              {deviceInfo.isPhone ? 'Phone' : 
               deviceInfo.isTablet ? 'Tablet' : 'Desktop'}
            </span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Screen Size:</span>
            <span className={styles.value}>
              {deviceInfo.screenWidth} × {deviceInfo.screenHeight}
            </span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Orientation:</span>
            <span className={styles.value}>
              {orientation.isPortrait ? 'Portrait' : 'Landscape'} ({orientation.angle}°)
            </span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Touch Device:</span>
            <span className={styles.value}>
              {deviceInfo.isTouchDevice ? 'Yes' : 'No'}
            </span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Retina Display:</span>
            <span className={styles.value}>
              {deviceInfo.isRetina ? 'Yes' : 'No'} ({deviceInfo.devicePixelRatio}x)
            </span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Optimal Columns:</span>
            <span className={styles.value}>{columns}</span>
          </div>
        </div>
      </div>

      <div className={styles.layoutInfo}>
        <h3>Layout Configuration</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>Grid Columns:</span>
            <span className={styles.value}>{layoutConfig.gridColumns}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Card Columns:</span>
            <span className={styles.value}>{layoutConfig.cardColumns}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Touch Target:</span>
            <span className={styles.value}>{layoutConfig.touchTargetSize}px</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Base Font Size:</span>
            <span className={styles.value}>{layoutConfig.baseFontSize}px</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Max Content Width:</span>
            <span className={styles.value}>{layoutConfig.maxContentWidth}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>Navigation:</span>
            <span className={styles.value}>
              {layoutConfig.useBottomNavigation ? 'Bottom' :
               layoutConfig.useTopNavigation ? 'Top' : 'Side'}
            </span>
          </div>
        </div>
      </div>

      <div className={styles.testContent}>
        <h3>Adaptive Content Grid ({contentType})</h3>
        <div 
          className={`${styles.contentGrid} ${styles[contentType]}`}
          style={{ 
            gridTemplateColumns: `repeat(${columns}, 1fr)`,
            gap: `${layoutConfig.spacing.medium}px`
          }}
        >
          {testItems.map((item) => (
            <div 
              key={item.id} 
              className={styles.contentItem}
              style={{
                minHeight: `${layoutConfig.touchTargetSize}px`,
                fontSize: `${layoutConfig.baseFontSize}px`,
                padding: `${layoutConfig.spacing.small}px ${layoutConfig.spacing.medium}px`
              }}
            >
              <div className={styles.itemTitle}>{item.title}</div>
              <div className={styles.itemDescription}>{item.description}</div>
            </div>
          ))}
        </div>
      </div>

      <div className={styles.orientationTest}>
        <h3>Orientation-Specific Elements</h3>
        <div className={styles.orientationContent}>
          <div className="portrait-only">
            <div className={styles.orientationItem}>
              📱 Portrait Only Content
              <p>This content is only visible in portrait orientation</p>
            </div>
          </div>
          <div className="landscape-only">
            <div className={styles.orientationItem}>
              📺 Landscape Only Content
              <p>This content is only visible in landscape orientation</p>
            </div>
          </div>
          <div className={styles.adaptiveLayout}>
            <div className="portrait-stack landscape-row">
              <div className={styles.adaptiveItem}>Item 1</div>
              <div className={styles.adaptiveItem}>Item 2</div>
              <div className={styles.adaptiveItem}>Item 3</div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.deviceSpecificTest}>
        <h3>Device-Specific Elements</h3>
        <div className={styles.deviceContent}>
          <div className="phone-only">
            <div className={styles.deviceItem}>📱 Phone Only</div>
          </div>
          <div className="phone-large-only">
            <div className={styles.deviceItem}>📱+ Large Phone Only</div>
          </div>
          <div className="tablet-small-only">
            <div className={styles.deviceItem}>📱 Small Tablet Only</div>
          </div>
          <div className="tablet-large-up-only">
            <div className={styles.deviceItem}>📱 Large Tablet+ Only</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceTestGrid;