@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.deviceTestGrid {
  @include adaptive-content-area;
  padding: $spacing-lg;
  
  h3 {
    @include adaptive-typography;
    margin-bottom: $spacing-md;
    color: $primary-color;
    border-bottom: 2px solid $border-color-light;
    padding-bottom: $spacing-sm;
  }
}

.deviceInfo,
.layoutInfo,
.testContent,
.orientationTest,
.deviceSpecificTest {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
  
  @include mobile-portrait {
    padding: $spacing-md;
    margin-bottom: $spacing-md;
    border-radius: $border-radius-md;
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-md;
    margin-bottom: $spacing-sm;
  }
}

.infoGrid {
  @include adaptive-grid(1, 2, 3);
  gap: $spacing-md;
  
  @include mobile-portrait {
    grid-template-columns: 1fr;
    gap: $spacing-sm;
  }
  
  @include mobile-landscape {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }
  
  @include tablet-portrait {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-md;
  }
  
  @include tablet-landscape {
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-lg;
  }
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  padding: $spacing-sm;
  background: $background-secondary;
  border-radius: $border-radius-sm;
  
  @include mobile-landscape {
    padding: $spacing-xs;
  }
}

.label {
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  color: $text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.value {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-primary;
  
  @include mobile-landscape {
    font-size: $font-size-xs;
  }
}

.contentGrid {
  display: grid;
  width: 100%;
  
  &.cards {
    @include adaptive-card-layout;
  }
  
  &.list {
    grid-template-columns: 1fr !important;
    
    .contentItem {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      border-bottom: 1px solid $border-color-light;
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
  
  &.form {
    @include adaptive-form-layout;
    
    .contentItem {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
    }
  }
}

.contentItem {
  background: $background-light;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  
  @include touch-device {
    &:active {
      transform: scale(0.98);
      background: rgba($primary-color, 0.05);
    }
  }
  
  @include hover-device {
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-md;
      border-color: $primary-color;
    }
  }
  
  @include mobile-portrait {
    min-height: $touch-target-comfortable;
    padding: $spacing-md;
  }
  
  @include mobile-landscape {
    min-height: $touch-target-min;
    padding: $spacing-sm;
    flex-direction: row;
    text-align: left;
    gap: $spacing-sm;
  }
}

.itemTitle {
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin-bottom: $spacing-xs;
  
  @include mobile-landscape {
    margin-bottom: 0;
    flex-shrink: 0;
  }
}

.itemDescription {
  font-size: $font-size-sm;
  color: $text-muted;
  
  @include mobile-landscape {
    font-size: $font-size-xs;
    flex: 1;
  }
}

.orientationContent,
.deviceContent {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  
  @include tablet-landscape {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.orientationItem,
.deviceItem {
  background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($secondary-color, 0.1) 100%);
  border: 2px solid rgba($primary-color, 0.2);
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  text-align: center;
  
  @include mobile-portrait {
    padding: $spacing-md;
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-md;
  }
  
  p {
    margin-top: $spacing-sm;
    font-size: $font-size-sm;
    color: $text-muted;
    margin-bottom: 0;
  }
}

.adaptiveLayout {
  margin-top: $spacing-lg;
}

.adaptiveItem {
  background: $background-secondary;
  border: 1px solid $border-color;
  border-radius: $border-radius-sm;
  padding: $spacing-md;
  text-align: center;
  font-weight: $font-weight-medium;
  
  @include mobile-portrait {
    margin-bottom: $spacing-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  @include mobile-landscape {
    flex: 1;
    margin-right: $spacing-sm;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// Responsive test indicators
.deviceTestGrid {
  position: relative;
  
  &::before {
    content: '';
    position: fixed;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    z-index: $z-index-tooltip;
    
    @include phone-only {
      background: #ff4444;
      content: '📱';
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
    }
    
    @include phone-large {
      background: #ff8844;
      content: '📱+';
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 35px;
      height: 30px;
    }
    
    @include tablet-small {
      background: #44ff44;
      content: '📱';
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 30px;
    }
    
    @include tablet-large {
      background: #4444ff;
      content: '📱';
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 45px;
      height: 30px;
    }
  }
  
  &::after {
    content: '';
    position: fixed;
    top: 40px;
    right: 10px;
    padding: 2px 6px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 10px;
    border-radius: 4px;
    z-index: $z-index-tooltip;
    
    @include portrait {
      content: 'Portrait';
    }
    
    @include landscape {
      content: 'Landscape';
    }
  }
}

// Animation for layout changes
.contentGrid {
  transition: grid-template-columns 0.3s ease, gap 0.3s ease;
}

.contentItem {
  transition: all 0.3s ease;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .contentGrid,
  .contentItem {
    transition: none;
  }
  
  .contentItem:hover {
    transform: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .contentItem {
    border-width: 2px;
  }
  
  .orientationItem,
  .deviceItem {
    border-width: 3px;
  }
}