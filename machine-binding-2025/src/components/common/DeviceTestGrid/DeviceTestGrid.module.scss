@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.deviceTestGrid {
  @include mixins.adaptive-content-area;
  padding: vars.$spacing-lg;
  
  h3 {
    @include mixins.adaptive-typography;
    margin-bottom: vars.$spacing-md;
    color: vars.$primary-color;
    border-bottom: 2px solid vars.$border-color-light;
    padding-bottom: vars.$spacing-sm;
  }
}

.deviceInfo,
.layoutInfo,
.testContent,
.orientationTest,
.deviceSpecificTest {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-lg;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-md;
    margin-bottom: vars.$spacing-md;
    border-radius: vars.$border-radius-md;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-md;
    margin-bottom: vars.$spacing-sm;
  }
}

.infoGrid {
  @include mixins.adaptive-grid(1, 2, 3);
  gap: vars.$spacing-md;
  
  @include mixins.mobile-portrait {
    grid-template-columns: 1fr;
    gap: vars.$spacing-sm;
  }
  
  @include mixins.mobile-landscape {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
  }
  
  @include mixins.tablet-portrait {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-md;
  }
  
  @include mixins.tablet-landscape {
    grid-template-columns: repeat(3, 1fr);
    gap: vars.$spacing-lg;
  }
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
  padding: vars.$spacing-sm;
  background: vars.$background-secondary;
  border-radius: vars.$border-radius-sm;
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-xs;
  }
}

.label {
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.value {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;
  
  @include mixins.mobile-landscape {
    font-size: vars.$font-size-xs;
  }
}

.contentGrid {
  display: grid;
  width: 100%;
  
  &.cards {
    @include mixins.adaptive-card-layout;
  }
  
  &.list {
    grid-template-columns: 1fr !important;
    
    .contentItem {
      display: flex;
      align-items: center;
      gap: vars.$spacing-md;
      border-bottom: 1px solid vars.$border-color-light;
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
  
  &.form {
    @include mixins.adaptive-form-layout;
    
    .contentItem {
      display: flex;
      flex-direction: column;
      gap: vars.$spacing-xs;
    }
  }
}

.contentItem {
  background: vars.$background-light;
  border: 1px solid vars.$border-color;
  border-radius: vars.$border-radius-md;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  
  @include mixins.touch-device {
    &:active {
      transform: scale(0.98);
      background: rgba(vars.$primary-color, 0.05);
    }
  }
  
  @include mixins.hover-device {
    &:hover {
      transform: translateY(-2px);
      box-shadow: vars.$shadow-md;
      border-color: vars.$primary-color;
    }
  }
  
  @include mixins.mobile-portrait {
    min-height: vars.$touch-target-comfortable;
    padding: vars.$spacing-md;
  }
  
  @include mixins.mobile-landscape {
    min-height: vars.$touch-target-min;
    padding: vars.$spacing-sm;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-sm;
  }
}

.itemTitle {
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin-bottom: vars.$spacing-xs;
  
  @include mixins.mobile-landscape {
    margin-bottom: 0;
    flex-shrink: 0;
  }
}

.itemDescription {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  
  @include mixins.mobile-landscape {
    font-size: vars.$font-size-xs;
    flex: 1;
  }
}

.orientationContent,
.deviceContent {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-md;
  
  @include mixins.tablet-landscape {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.orientationItem,
.deviceItem {
  background: linear-gradient(135deg, rgba(vars.$primary-color, 0.1) 0%, rgba(vars.$secondary-color, 0.1) 100%);
  border: 2px solid rgba(vars.$primary-color, 0.2);
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-lg;
  text-align: center;
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-md;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-md;
  }
  
  p {
    margin-top: vars.$spacing-sm;
    font-size: vars.$font-size-sm;
    color: vars.$text-muted;
    margin-bottom: 0;
  }
}

.adaptiveLayout {
  margin-top: vars.$spacing-lg;
}

.adaptiveItem {
  background: vars.$background-secondary;
  border: 1px solid vars.$border-color;
  border-radius: vars.$border-radius-sm;
  padding: vars.$spacing-md;
  text-align: center;
  font-weight: vars.$font-weight-medium;
  
  @include mixins.mobile-portrait {
    margin-bottom: vars.$spacing-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  @include mixins.mobile-landscape {
    flex: 1;
    margin-right: vars.$spacing-sm;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// Responsive test indicators
.deviceTestGrid {
  position: relative;
  
  &::before {
    content: '';
    position: fixed;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    z-index: vars.$z-index-tooltip;
    
    @include mixins.phone-only {
      background: #ff4444;
      content: '📱';
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
    }
    
    @include mixins.phone-large {
      background: #ff8844;
      content: '📱+';
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 35px;
      height: 30px;
    }
    
    @include mixins.tablet-small {
      background: #44ff44;
      content: '📱';
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 30px;
    }
    
    @include mixins.tablet-large {
      background: #4444ff;
      content: '📱';
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 45px;
      height: 30px;
    }
  }
  
  &::after {
    content: '';
    position: fixed;
    top: 40px;
    right: 10px;
    padding: 2px 6px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 10px;
    border-radius: 4px;
    z-index: vars.$z-index-tooltip;
    
    @include mixins.portrait {
      content: 'Portrait';
    }
    
    @include mixins.landscape {
      content: 'Landscape';
    }
  }
}

// Animation for layout changes
.contentGrid {
  transition: grid-template-columns 0.3s ease, gap 0.3s ease;
}

.contentItem {
  transition: all 0.3s ease;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .contentGrid,
  .contentItem {
    transition: none;
  }
  
  .contentItem:hover {
    transform: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .contentItem {
    border-width: 2px;
  }
  
  .orientationItem,
  .deviceItem {
    border-width: 3px;
  }
}