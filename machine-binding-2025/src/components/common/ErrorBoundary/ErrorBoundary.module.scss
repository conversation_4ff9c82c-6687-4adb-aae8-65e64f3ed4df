@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.errorFallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: $spacing-lg;
  background-color: $color-background;

  @include mobile-only {
    min-height: 300px;
    padding: $spacing-md;
  }
}

.errorContainer {
  max-width: 500px;
  text-align: center;
  background: $color-white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-md;
  border: 1px solid $color-border;

  @include mobile-only {
    padding: $spacing-lg;
    margin: 0 $spacing-sm;
  }
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: $spacing-md;
  
  @include mobile-only {
    font-size: 2.5rem;
  }
}

.errorTitle {
  color: $color-error;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: $spacing-md;
  
  @include mobile-only {
    font-size: 1.25rem;
  }
}

.errorMessage {
  color: $color-text-secondary;
  line-height: 1.6;
  margin-bottom: $spacing-lg;
  
  @include mobile-only {
    font-size: 0.9rem;
  }
}

.errorDetails {
  margin-bottom: $spacing-lg;
  text-align: left;

  details {
    border: 1px solid $color-border;
    border-radius: $border-radius-sm;
    padding: $spacing-sm;
    background-color: $color-background;
  }

  summary {
    cursor: pointer;
    font-weight: 500;
    color: $color-text-secondary;
    padding: $spacing-xs;
    
    &:hover {
      color: $color-primary;
    }
  }
}

.errorStack {
  background-color: $color-background;
  border: 1px solid $color-border;
  border-radius: $border-radius-sm;
  padding: $spacing-sm;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: $color-text-secondary;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  margin-top: $spacing-sm;

  @include mobile-only {
    font-size: 0.7rem;
    max-height: 150px;
  }
}

.errorActions {
  display: flex;
  gap: $spacing-sm;
  justify-content: center;
  flex-wrap: wrap;

  @include mobile-only {
    flex-direction: column;
    gap: $spacing-xs;
  }
}

.actionButton {
  min-width: 120px;

  @include mobile-only {
    min-width: auto;
    width: 100%;
  }
}

// Specialized error styles
.networkError,
.formError {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  
  .errorContainer {
    max-width: 400px;
    padding: $spacing-lg;
    
    h3 {
      color: $color-warning;
      margin-bottom: $spacing-sm;
      font-size: 1.25rem;
    }
    
    p {
      color: $color-text-secondary;
      margin-bottom: $spacing-md;
      line-height: 1.5;
    }
  }

  @include mobile-only {
    padding: $spacing-md;
    
    .errorContainer {
      padding: $spacing-md;
      margin: 0 $spacing-sm;
      
      h3 {
        font-size: 1.1rem;
      }
      
      p {
        font-size: 0.9rem;
      }
    }
  }
}

.networkError {
  .errorIcon {
    color: $color-warning;
  }
}

.formError {
  .errorIcon {
    color: $color-error;
  }
}

// Loading state for error recovery
.errorRecovering {
  opacity: 0.6;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid $color-primary;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive adjustments
@include tablet-up {
  .errorContainer {
    padding: $spacing-lg;
  }
  
  .errorActions {
    flex-direction: row;
    justify-content: center;
  }
  
  .actionButton {
    min-width: 100px;
  }
}

@include mobile-large-up {
  .errorFallback {
    min-height: 350px;
  }
  
  .errorTitle {
    font-size: 1.3rem;
  }
}