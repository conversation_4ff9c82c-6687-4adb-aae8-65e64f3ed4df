@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.errorFallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: vars.$spacing-lg;
  background-color: vars.$color-background;

  @include mixins.mobile-only {
    min-height: 300px;
    padding: vars.$spacing-md;
  }
}

.errorContainer {
  max-width: 500px;
  text-align: center;
  background: vars.$color-white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  box-shadow: vars.$shadow-md;
  border: 1px solid vars.$color-border;

  @include mixins.mobile-only {
    padding: vars.$spacing-lg;
    margin: 0 vars.$spacing-sm;
  }
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: vars.$spacing-md;
  
  @include mixins.mobile-only {
    font-size: 2.5rem;
  }
}

.errorTitle {
  color: vars.$color-error;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: vars.$spacing-md;
  
  @include mixins.mobile-only {
    font-size: 1.25rem;
  }
}

.errorMessage {
  color: vars.$color-text-secondary;
  line-height: 1.6;
  margin-bottom: vars.$spacing-lg;
  
  @include mixins.mobile-only {
    font-size: 0.9rem;
  }
}

.errorDetails {
  margin-bottom: vars.$spacing-lg;
  text-align: left;

  details {
    border: 1px solid vars.$color-border;
    border-radius: vars.$border-radius-sm;
    padding: vars.$spacing-sm;
    background-color: vars.$color-background;
  }

  summary {
    cursor: pointer;
    font-weight: 500;
    color: vars.$color-text-secondary;
    padding: vars.$spacing-xs;
    
    &:hover {
      color: vars.$color-primary;
    }
  }
}

.errorStack {
  background-color: vars.$color-background;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-sm;
  padding: vars.$spacing-sm;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  color: vars.$color-text-secondary;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  margin-top: vars.$spacing-sm;

  @include mixins.mobile-only {
    font-size: 0.7rem;
    max-height: 150px;
  }
}

.errorActions {
  display: flex;
  gap: vars.$spacing-sm;
  justify-content: center;
  flex-wrap: wrap;

  @include mixins.mobile-only {
    flex-direction: column;
    gap: vars.$spacing-xs;
  }
}

.actionButton {
  min-width: 120px;

  @include mixins.mobile-only {
    min-width: auto;
    width: 100%;
  }
}

// Specialized error styles
.networkError,
.formError {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: vars.$spacing-lg;
  
  .errorContainer {
    max-width: 400px;
    padding: vars.$spacing-lg;
    
    h3 {
      color: vars.$color-warning;
      margin-bottom: vars.$spacing-sm;
      font-size: 1.25rem;
    }
    
    p {
      color: vars.$color-text-secondary;
      margin-bottom: vars.$spacing-md;
      line-height: 1.5;
    }
  }

  @include mixins.mobile-only {
    padding: vars.$spacing-md;
    
    .errorContainer {
      padding: vars.$spacing-md;
      margin: 0 vars.$spacing-sm;
      
      h3 {
        font-size: 1.1rem;
      }
      
      p {
        font-size: 0.9rem;
      }
    }
  }
}

.networkError {
  .errorIcon {
    color: vars.$color-warning;
  }
}

.formError {
  .errorIcon {
    color: vars.$color-error;
  }
}

// Loading state for error recovery
.errorRecovering {
  opacity: 0.6;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid vars.$color-primary;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive adjustments
@include mixins.tablet-up {
  .errorContainer {
    padding: vars.$spacing-lg;
  }
  
  .errorActions {
    flex-direction: row;
    justify-content: center;
  }
  
  .actionButton {
    min-width: 100px;
  }
}

@include mixins.mobile-large-up {
  .errorFallback {
    min-height: 350px;
  }
  
  .errorTitle {
    font-size: 1.3rem;
  }
}