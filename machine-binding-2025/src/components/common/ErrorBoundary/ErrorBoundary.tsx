import React, { Component, ReactNode } from 'react';
import { Button } from '../Button/Button';
import { errorHandler } from '../../../utils/errorHandler';
import type { ApiError } from '../../../types';
import styles from './ErrorBoundary.module.scss';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

export interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  hasError: boolean;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // Handle error through error handler
    const apiError: ApiError = {
      code: 'COMPONENT_ERROR',
      message: error.message,
      details: {
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      },
    };

    errorHandler.handleError(apiError, {
      showToast: false, // Don't show toast for component errors
      logError: true,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetOnPropsChange) {
        this.resetErrorBoundary();
      } else if (resetKeys) {
        const hasResetKeyChanged = resetKeys.some(
          (key, index) => prevProps.resetKeys?.[index] !== key
        );
        if (hasResetKeyChanged) {
          this.resetErrorBoundary();
        }
      }
    }
  }

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.resetTimeoutId = window.setTimeout(() => {
      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
      });
    }, 100);
  };

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  render() {
    const { hasError, error } = this.state;
    const { children, fallback: Fallback } = this.props;

    if (hasError && error) {
      if (Fallback) {
        return (
          <Fallback
            error={error}
            resetError={this.resetErrorBoundary}
            hasError={hasError}
          />
        );
      }

      return (
        <DefaultErrorFallback
          error={error}
          resetError={this.resetErrorBoundary}
          hasError={hasError}
        />
      );
    }

    return children;
  }
}

// Default error fallback component
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
}) => {
  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className={styles.errorFallback}>
      <div className={styles.errorContainer}>
        <div className={styles.errorIcon}>⚠️</div>
        
        <h2 className={styles.errorTitle}>出现了一些问题</h2>
        
        <p className={styles.errorMessage}>
          应用程序遇到了意外错误。我们已经记录了这个问题，请尝试以下解决方案：
        </p>

        <div className={styles.errorDetails}>
          <details>
            <summary>错误详情</summary>
            <pre className={styles.errorStack}>
              {error.message}
              {error.stack && (
                <>
                  <br />
                  <br />
                  {error.stack}
                </>
              )}
            </pre>
          </details>
        </div>

        <div className={styles.errorActions}>
          <Button
            variant="primary"
            onClick={resetError}
            className={styles.actionButton}
          >
            重试
          </Button>
          
          <Button
            variant="secondary"
            onClick={handleReload}
            className={styles.actionButton}
          >
            刷新页面
          </Button>
          
          <Button
            variant="outline"
            onClick={handleGoHome}
            className={styles.actionButton}
          >
            返回首页
          </Button>
        </div>
      </div>
    </div>
  );
};

// Specialized error boundaries for different scenarios
export const NetworkErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className={styles.networkError}>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>📡</div>
            <h3>网络连接问题</h3>
            <p>请检查您的网络连接后重试</p>
            <Button onClick={resetError}>重试</Button>
          </div>
        </div>
      )}
    />
  );
};

export const FormErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className={styles.formError}>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>📝</div>
            <h3>表单处理错误</h3>
            <p>表单提交时出现问题，请重试</p>
            <Button onClick={resetError}>重新填写</Button>
          </div>
        </div>
      )}
    />
  );
};

// HOC for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};