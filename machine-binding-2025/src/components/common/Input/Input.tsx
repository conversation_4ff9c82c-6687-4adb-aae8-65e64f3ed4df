import React, { useState, forwardRef } from 'react';
import { FormComponentProps } from '../../../types';
import styles from './Input.module.scss';

export interface InputProps extends FormComponentProps {
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'search';
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  helpText?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      name,
      label,
      type = 'text',
      value,
      defaultValue,
      placeholder,
      error,
      helpText,
      required = false,
      disabled = false,
      readOnly = false,
      autoComplete,
      autoFocus = false,
      maxLength,
      minLength,
      pattern,
      leftIcon,
      rightIcon,
      className = '',
      testId,
      onChange,
      onBlur,
      onFocus,
      onKeyDown,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);

    const inputType = type === 'password' && showPassword ? 'text' : type;
    const hasError = Boolean(error);
    const hasLeftIcon = Boolean(leftIcon);
    const hasRightIcon = Boolean(rightIcon) || type === 'password';

    const inputClasses = [
      styles.input,
      hasError && styles.error,
      hasLeftIcon && styles.hasLeftIcon,
      hasRightIcon && styles.hasRightIcon,
      className,
    ]
      .filter(Boolean)
      .join(' ');

    const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
      onFocus?.(event);
    };

    const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
      onBlur?.(event);
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className={styles.inputGroup}>
        {label && (
          <label htmlFor={name} className={`${styles.label} ${required ? styles.required : ''}`}>
            {label}
          </label>
        )}
        
        <div className={styles.inputWrapper}>
          {leftIcon && <div className={styles.leftIcon}>{leftIcon}</div>}
          
          <input
            ref={ref}
            id={name}
            name={name}
            type={inputType}
            value={value}
            defaultValue={defaultValue}
            placeholder={placeholder}
            className={inputClasses}
            required={required}
            disabled={disabled}
            readOnly={readOnly}
            autoComplete={autoComplete}
            autoFocus={autoFocus}
            maxLength={maxLength}
            minLength={minLength}
            pattern={pattern}
            data-testid={testId}
            onChange={onChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={onKeyDown}
            {...props}
          />

          {type === 'password' && (
            <button
              type="button"
              className={styles.passwordToggle}
              onClick={togglePasswordVisibility}
              tabIndex={-1}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          )}

          {rightIcon && type !== 'password' && (
            <div className={styles.rightIcon}>{rightIcon}</div>
          )}
        </div>

        {error && (
          <div className={styles.errorMessage} role="alert">
            ⚠️ {error}
          </div>
        )}

        {helpText && !error && (
          <div className={styles.helpText}>{helpText}</div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';