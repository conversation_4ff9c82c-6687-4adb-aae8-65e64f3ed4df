@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.inputGroup {
  margin-bottom: vars.$spacing-md;
}

.label {
  display: block;
  margin-bottom: vars.$spacing-xs;
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;

  &.required::after {
    content: ' *';
    color: vars.$danger-color;
  }
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  width: 100%;
  border: 2px solid vars.$border-color;
  border-radius: vars.$border-radius-md;
  font-family: inherit;
  background-color: vars.$background-primary;
  transition: all 0.2s ease;
  @include mixins.input-size('md');

  &::placeholder {
    color: vars.$text-muted;
  }

  &:focus {
    outline: none;
    border-color: vars.$primary-color;
    box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
  }

  @include mixins.focus-style;

  &:disabled {
    background-color: vars.$background-secondary;
    color: vars.$text-muted;
    cursor: not-allowed;
  }

  &.error {
    border-color: vars.$danger-color;

    &:focus {
      border-color: vars.$danger-color;
      box-shadow: 0 0 0 3px rgba(vars.$danger-color, 0.1);
    }
  }

  &.success {
    border-color: vars.$success-color;

    &:focus {
      border-color: vars.$success-color;
      box-shadow: 0 0 0 3px rgba(vars.$success-color, 0.1);
    }
  }
}

.leftIcon,
.rightIcon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: vars.$text-muted;
  pointer-events: none;
}

.leftIcon {
  left: 0;
}

.rightIcon {
  right: 0;
}

.input.hasLeftIcon {
  padding-left: 48px;
}

.input.hasRightIcon {
  padding-right: 48px;
}

.errorMessage {
  margin-top: vars.$spacing-xs;
  font-size: vars.$font-size-xs;
  color: vars.$danger-color;
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
}

.helpText {
  margin-top: vars.$spacing-xs;
  font-size: vars.$font-size-xs;
  color: vars.$text-muted;
}

// Password input specific styles
.passwordToggle {
  position: absolute;
  right: vars.$spacing-sm;
  background: none;
  border: none;
  color: vars.$text-muted;
  cursor: pointer;
  padding: vars.$spacing-xs;
  border-radius: vars.$border-radius-sm;
  transition: color 0.2s ease;

  &:hover {
    color: vars.$text-primary;
  }

  &:focus {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

// Mobile optimizations
@include mixins.mobile-only {
  .input {
    @include mixins.touch-target;
    // Prevent zoom on iOS
    font-size: 16px;
  }

  .leftIcon,
  .rightIcon {
    @include mixins.touch-target;
  }

  .input.hasLeftIcon {
    padding-left: vars.$touch-target-comfortable + 8px;
  }

  .input.hasRightIcon {
    padding-right: vars.$touch-target-comfortable + 8px;
  }

  .passwordToggle {
    @include mixins.touch-target;
    right: vars.$spacing-xs;
  }
}