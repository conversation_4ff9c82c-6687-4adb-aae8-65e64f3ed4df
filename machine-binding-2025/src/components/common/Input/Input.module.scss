@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.inputGroup {
  margin-bottom: $spacing-md;
}

.label {
  display: block;
  margin-bottom: $spacing-xs;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-primary;

  &.required::after {
    content: ' *';
    color: $danger-color;
  }
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  width: 100%;
  @include input-size('md');
  border: 2px solid $border-color;
  border-radius: $border-radius-md;
  font-family: inherit;
  background-color: $background-primary;
  transition: all 0.2s ease;

  &::placeholder {
    color: $text-muted;
  }

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }
  
  @include focus-style;

  &:disabled {
    background-color: $background-secondary;
    color: $text-muted;
    cursor: not-allowed;
  }

  &.error {
    border-color: $danger-color;

    &:focus {
      border-color: $danger-color;
      box-shadow: 0 0 0 3px rgba($danger-color, 0.1);
    }
  }

  &.success {
    border-color: $success-color;

    &:focus {
      border-color: $success-color;
      box-shadow: 0 0 0 3px rgba($success-color, 0.1);
    }
  }
}

.leftIcon,
.rightIcon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: $text-muted;
  pointer-events: none;
}

.leftIcon {
  left: 0;
}

.rightIcon {
  right: 0;
}

.input.hasLeftIcon {
  padding-left: 48px;
}

.input.hasRightIcon {
  padding-right: 48px;
}

.errorMessage {
  margin-top: $spacing-xs;
  font-size: $font-size-xs;
  color: $danger-color;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.helpText {
  margin-top: $spacing-xs;
  font-size: $font-size-xs;
  color: $text-muted;
}

// Password input specific styles
.passwordToggle {
  position: absolute;
  right: $spacing-sm;
  background: none;
  border: none;
  color: $text-muted;
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: $border-radius-sm;
  transition: color 0.2s ease;

  &:hover {
    color: $text-primary;
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// Mobile optimizations
@include mobile-only {
  .input {
    @include touch-target;
    // Prevent zoom on iOS
    font-size: 16px;
  }

  .leftIcon,
  .rightIcon {
    @include touch-target;
  }

  .input.hasLeftIcon {
    padding-left: $touch-target-comfortable + 8px;
  }

  .input.hasRightIcon {
    padding-right: $touch-target-comfortable + 8px;
  }
  
  .passwordToggle {
    @include touch-target;
    right: $spacing-xs;
  }
}