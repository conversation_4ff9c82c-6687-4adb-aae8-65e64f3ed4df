@use 'sass:color';
@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

// Upload progress styles
.uploadProgress {
  background: $color-white;
  border: 1px solid $color-border;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-sm;

  @include mobile-only {
    padding: $spacing-md;
  }
}

.uploadHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }
}

.uploadTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: $color-text-primary;
  margin: 0;

  @include mobile-only {
    font-size: 1rem;
  }
}

.uploadSummary {
  display: flex;
  gap: $spacing-sm;
  font-size: 0.875rem;
}

.errorCount {
  color: $color-error;
  font-weight: 500;
}

.fileList {
  margin-top: $spacing-lg;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-sm;
  border: 1px solid $color-border;
  border-radius: $border-radius-md;
  transition: all 0.2s ease;

  &.uploading {
    border-color: $color-primary;
    background-color: rgba($color-primary, 0.05);
  }

  &.completed {
    border-color: $color-success;
    background-color: rgba($color-success, 0.05);
  }

  &.error {
    border-color: $color-error;
    background-color: rgba($color-error, 0.05);
  }

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.fileInfo {
  flex: 1;
  min-width: 0;

  @include mobile-only {
    width: 100%;
  }
}

.fileName {
  font-weight: 500;
  color: $color-text-primary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: $spacing-xs;
}

.fileError {
  font-size: 0.875rem;
  color: $color-error;
  line-height: 1.4;
}

.fileProgress {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  min-width: 200px;

  @include mobile-only {
    width: 100%;
    min-width: auto;
  }
}

.fileActions {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.statusIcon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancelButton,
.retryButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: $border-radius-sm;
  transition: background-color 0.2s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;

  &:hover {
    background-color: rgba($color-text-secondary, 0.1);
  }
}

.cancelButton {
  color: $color-error;
  font-size: 1.25rem;
  font-weight: bold;
}

.retryButton {
  color: $color-primary;
}

// Action feedback styles
.actionFeedback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  border-radius: $border-radius-lg;
  transition: all 0.3s ease;

  &.loading {
    background-color: rgba($color-primary, 0.05);
    border: 1px solid rgba($color-primary, 0.2);
  }

  &.success {
    background-color: rgba($color-success, 0.05);
    border: 1px solid rgba($color-success, 0.2);
  }

  &.error {
    background-color: rgba($color-error, 0.05);
    border: 1px solid rgba($color-error, 0.2);
  }

  @include mobile-only {
    padding: $spacing-md;
  }
}

.loadingState,
.errorState {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  flex-direction: column;
  text-align: center;

  @include tablet-up {
    flex-direction: row;
    text-align: left;
  }
}

.feedbackText {
  font-weight: 500;
  color: $color-text-primary;
}

.errorIcon {
  font-size: 1.5rem;
}

.retryButton {
  background-color: $color-primary;
  color: white;
  border: none;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  margin-top: $spacing-sm;

  &:hover {
    background-color: color.adjust($color-primary, $lightness: -10%);
  }

  @include tablet-up {
    margin-top: 0;
    margin-left: $spacing-sm;
  }
}

// Form feedback styles
.formFeedback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md;
  border-radius: $border-radius-md;
  margin-top: $spacing-md;

  &.success {
    background-color: rgba($color-success, 0.1);
    border: 1px solid rgba($color-success, 0.3);
  }

  &.error {
    background-color: rgba($color-error, 0.1);
    border: 1px solid rgba($color-error, 0.3);
  }

  &.submitting {
    background-color: rgba($color-primary, 0.05);
    border: 1px solid rgba($color-primary, 0.2);
  }

  @include mobile-only {
    padding: $spacing-sm;
  }
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  text-align: center;

  @include tablet-up {
    text-align: left;
  }
}

.errorIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.errorText {
  color: $color-error;
  font-weight: 500;
  line-height: 1.4;
}

.submittingText {
  color: $color-primary;
  font-weight: 500;
  margin-left: $spacing-sm;
}

// Data skeleton styles
.dataSkeleton {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;

  &.list {
    gap: $spacing-sm;
  }

  &.card {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: $spacing-lg;

    @include mobile-only {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
  }

  &.table {
    gap: $spacing-xs;
  }

  &.profile {
    gap: $spacing-lg;
  }
}

// Skeleton item styles
.listItem {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-md;
  border: 1px solid $color-border;
  border-radius: $border-radius-md;

  @include mobile-only {
    padding: $spacing-sm;
    gap: $spacing-sm;
  }
}

.cardItem {
  border: 1px solid $color-border;
  border-radius: $border-radius-lg;
  overflow: hidden;
}

.tableRow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $spacing-md;
  padding: $spacing-md;
  border-bottom: 1px solid $color-border;

  @include mobile-only {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
    padding: $spacing-sm;
  }
}

.tableCell {
  display: flex;
  align-items: center;
}

.profileItem {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  padding: $spacing-lg;
  border: 1px solid $color-border;
  border-radius: $border-radius-lg;

  @include mobile-only {
    flex-direction: column;
    text-align: center;
    gap: $spacing-md;
    padding: $spacing-md;
  }
}

.defaultItem {
  padding: $spacing-md;
  border: 1px solid $color-border;
  border-radius: $border-radius-md;
}

// Skeleton elements
.skeletonAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(
    90deg,
    rgba($color-border, 0.8) 25%,
    rgba($color-border, 0.4) 50%,
    rgba($color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  flex-shrink: 0;
}

.skeletonImage {
  width: 100%;
  height: 160px;
  background: linear-gradient(
    90deg,
    rgba($color-border, 0.8) 25%,
    rgba($color-border, 0.4) 50%,
    rgba($color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeletonContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  padding: $spacing-md;

  .cardItem & {
    padding: $spacing-md;
  }

  .listItem & {
    padding: 0;
  }
}

.skeletonTitle {
  height: 1.25rem;
  width: 70%;
  border-radius: $border-radius-sm;
  background: linear-gradient(
    90deg,
    rgba($color-border, 0.8) 25%,
    rgba($color-border, 0.4) 50%,
    rgba($color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeletonText {
  height: 1rem;
  width: 100%;
  border-radius: $border-radius-sm;
  background: linear-gradient(
    90deg,
    rgba($color-border, 0.8) 25%,
    rgba($color-border, 0.4) 50%,
    rgba($color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

  &:last-child {
    width: 60%;
  }
}

// Animations
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Responsive adjustments
@include mobile-only {
  .uploadProgress {
    border-radius: $border-radius-md;
  }

  .fileItem {
    padding: $spacing-sm;
  }

  .actionFeedback {
    padding: $spacing-md;
    border-radius: $border-radius-md;
  }

  .formFeedback {
    padding: $spacing-sm;
    border-radius: $border-radius-sm;
  }
}