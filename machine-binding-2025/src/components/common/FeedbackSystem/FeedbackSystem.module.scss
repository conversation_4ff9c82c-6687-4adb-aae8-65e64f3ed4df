@use 'sass:color';
@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

// Upload progress styles
.uploadProgress {
  background: vars.$color-white;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;

  @include mixins.mobile-only {
    padding: vars.$spacing-md;
  }
}

.uploadHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: vars.$spacing-md;

  @include mixins.mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: vars.$spacing-xs;
  }
}

.uploadTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: vars.$color-text-primary;
  margin: 0;

  @include mixins.mobile-only {
    font-size: 1rem;
  }
}

.uploadSummary {
  display: flex;
  gap: vars.$spacing-sm;
  font-size: 0.875rem;
}

.errorCount {
  color: vars.$color-error;
  font-weight: 500;
}

.fileList {
  margin-top: vars.$spacing-lg;
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-sm;
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: vars.$spacing-sm;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-md;
  transition: all 0.2s ease;

  &.uploading {
    border-color: vars.$color-primary;
    background-color: rgba(vars.$color-primary, 0.05);
  }

  &.completed {
    border-color: vars.$color-success;
    background-color: rgba(vars.$color-success, 0.05);
  }

  &.error {
    border-color: vars.$color-error;
    background-color: rgba(vars.$color-error, 0.05);
  }

  @include mixins.mobile-only {
    flex-direction: column;
    align-items: flex-start;
    gap: vars.$spacing-sm;
  }
}

.fileInfo {
  flex: 1;
  min-width: 0;

  @include mixins.mobile-only {
    width: 100%;
  }
}

.fileName {
  font-weight: 500;
  color: vars.$color-text-primary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: vars.$spacing-xs;
}

.fileError {
  font-size: 0.875rem;
  color: vars.$color-error;
  line-height: 1.4;
}

.fileProgress {
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
  min-width: 200px;

  @include mixins.mobile-only {
    width: 100%;
    min-width: auto;
  }
}

.fileActions {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
}

.statusIcon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancelButton,
.retryButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: vars.$spacing-xs;
  border-radius: vars.$border-radius-sm;
  transition: background-color 0.2s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;

  &:hover {
    background-color: rgba(vars.$color-text-secondary, 0.1);
  }
}

.cancelButton {
  color: vars.$color-error;
  font-size: 1.25rem;
  font-weight: bold;
}

.retryButton {
  color: vars.$color-primary;
}

// Action feedback styles
.actionFeedback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: vars.$spacing-lg;
  border-radius: vars.$border-radius-lg;
  transition: all 0.3s ease;

  &.loading {
    background-color: rgba(vars.$color-primary, 0.05);
    border: 1px solid rgba(vars.$color-primary, 0.2);
  }

  &.success {
    background-color: rgba(vars.$color-success, 0.05);
    border: 1px solid rgba(vars.$color-success, 0.2);
  }

  &.error {
    background-color: rgba(vars.$color-error, 0.05);
    border: 1px solid rgba(vars.$color-error, 0.2);
  }

  @include mixins.mobile-only {
    padding: vars.$spacing-md;
  }
}

.loadingState,
.errorState {
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
  flex-direction: column;
  text-align: center;

  @include mixins.tablet-up {
    flex-direction: row;
    text-align: left;
  }
}

.feedbackText {
  font-weight: 500;
  color: vars.$color-text-primary;
}

.errorIcon {
  font-size: 1.5rem;
}

.retryButton {
  background-color: vars.$color-primary;
  color: white;
  border: none;
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  margin-top: vars.$spacing-sm;

  &:hover {
    background-color: color.adjust(vars.$color-primary, vars.$lightness: -10%);
  }

  @include mixins.tablet-up {
    margin-top: 0;
    margin-left: vars.$spacing-sm;
  }
}

// Form feedback styles
.formFeedback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: vars.$spacing-md;
  border-radius: vars.$border-radius-md;
  margin-top: vars.$spacing-md;

  &.success {
    background-color: rgba(vars.$color-success, 0.1);
    border: 1px solid rgba(vars.$color-success, 0.3);
  }

  &.error {
    background-color: rgba(vars.$color-error, 0.1);
    border: 1px solid rgba(vars.$color-error, 0.3);
  }

  &.submitting {
    background-color: rgba(vars.$color-primary, 0.05);
    border: 1px solid rgba(vars.$color-primary, 0.2);
  }

  @include mixins.mobile-only {
    padding: vars.$spacing-sm;
  }
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
  text-align: center;

  @include mixins.tablet-up {
    text-align: left;
  }
}

.errorIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.errorText {
  color: vars.$color-error;
  font-weight: 500;
  line-height: 1.4;
}

.submittingText {
  color: vars.$color-primary;
  font-weight: 500;
  margin-left: vars.$spacing-sm;
}

// Data skeleton styles
.dataSkeleton {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-md;

  &.list {
    gap: vars.$spacing-sm;
  }

  &.card {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: vars.$spacing-lg;

    @include mixins.mobile-only {
      grid-template-columns: 1fr;
      gap: vars.$spacing-md;
    }
  }

  &.table {
    gap: vars.$spacing-xs;
  }

  &.profile {
    gap: vars.$spacing-lg;
  }
}

// Skeleton item styles
.listItem {
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  padding: vars.$spacing-md;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-md;

  @include mixins.mobile-only {
    padding: vars.$spacing-sm;
    gap: vars.$spacing-sm;
  }
}

.cardItem {
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-lg;
  overflow: hidden;
}

.tableRow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: vars.$spacing-md;
  padding: vars.$spacing-md;
  border-bottom: 1px solid vars.$color-border;

  @include mixins.mobile-only {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
    padding: vars.$spacing-sm;
  }
}

.tableCell {
  display: flex;
  align-items: center;
}

.profileItem {
  display: flex;
  align-items: center;
  gap: vars.$spacing-lg;
  padding: vars.$spacing-lg;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-lg;

  @include mixins.mobile-only {
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-md;
    padding: vars.$spacing-md;
  }
}

.defaultItem {
  padding: vars.$spacing-md;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-md;
}

// Skeleton elements
.skeletonAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(
    90deg,
    rgba(vars.$color-border, 0.8) 25%,
    rgba(vars.$color-border, 0.4) 50%,
    rgba(vars.$color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  flex-shrink: 0;
}

.skeletonImage {
  width: 100%;
  height: 160px;
  background: linear-gradient(
    90deg,
    rgba(vars.$color-border, 0.8) 25%,
    rgba(vars.$color-border, 0.4) 50%,
    rgba(vars.$color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeletonContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
  padding: vars.$spacing-md;

  .cardItem & {
    padding: vars.$spacing-md;
  }

  .listItem & {
    padding: 0;
  }
}

.skeletonTitle {
  height: 1.25rem;
  width: 70%;
  border-radius: vars.$border-radius-sm;
  background: linear-gradient(
    90deg,
    rgba(vars.$color-border, 0.8) 25%,
    rgba(vars.$color-border, 0.4) 50%,
    rgba(vars.$color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeletonText {
  height: 1rem;
  width: 100%;
  border-radius: vars.$border-radius-sm;
  background: linear-gradient(
    90deg,
    rgba(vars.$color-border, 0.8) 25%,
    rgba(vars.$color-border, 0.4) 50%,
    rgba(vars.$color-border, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

  &:last-child {
    width: 60%;
  }
}

// Animations
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Responsive adjustments
@include mixins.mobile-only {
  .uploadProgress {
    border-radius: vars.$border-radius-md;
  }

  .fileItem {
    padding: vars.$spacing-sm;
  }

  .actionFeedback {
    padding: vars.$spacing-md;
    border-radius: vars.$border-radius-md;
  }

  .formFeedback {
    padding: vars.$spacing-sm;
    border-radius: vars.$border-radius-sm;
  }
}