import React, { useState, useEffect } from 'react';
import { LoadingSpinner, ProgressIndicator, SuccessFeedback } from '../LoadingSpinner/LoadingSpinner';
import type { BaseComponentProps } from '../../../types';
import styles from './FeedbackSystem.module.scss';

// Upload progress feedback
export interface UploadProgressProps extends BaseComponentProps {
  files: Array<{
    name: string;
    progress: number;
    status: 'uploading' | 'completed' | 'error';
    error?: string;
  }>;
  onCancel?: (fileName: string) => void;
  onRetry?: (fileName: string) => void;
}

export const UploadProgress: React.FC<UploadProgressProps> = ({
  files,
  onCancel,
  onRetry,
  className = '',
  testId,
}) => {
  const totalProgress = files.length > 0 
    ? files.reduce((sum, file) => sum + file.progress, 0) / files.length 
    : 0;

  const completedFiles = files.filter(file => file.status === 'completed').length;
  const errorFiles = files.filter(file => file.status === 'error').length;

  return (
    <div className={`${styles.uploadProgress} ${className}`} data-testid={testId}>
      <div className={styles.uploadHeader}>
        <h4 className={styles.uploadTitle}>
          文件上传 ({completedFiles}/{files.length})
        </h4>
        <div className={styles.uploadSummary}>
          {errorFiles > 0 && (
            <span className={styles.errorCount}>{errorFiles} 个失败</span>
          )}
        </div>
      </div>

      <ProgressIndicator
        progress={totalProgress}
        text={`总进度`}
        color="primary"
        showPercentage
      />

      <div className={styles.fileList}>
        {files.map((file, index) => (
          <div key={index} className={`${styles.fileItem} ${styles[file.status]}`}>
            <div className={styles.fileInfo}>
              <div className={styles.fileName}>{file.name}</div>
              {file.status === 'error' && file.error && (
                <div className={styles.fileError}>{file.error}</div>
              )}
            </div>

            <div className={styles.fileProgress}>
              {file.status === 'uploading' && (
                <>
                  <ProgressIndicator
                    progress={file.progress}
                    variant="linear"
                    size="small"
                    showPercentage={false}
                    color="primary"
                  />
                  <button
                    className={styles.cancelButton}
                    onClick={() => onCancel?.(file.name)}
                    title="取消上传"
                  >
                    ×
                  </button>
                </>
              )}

              {file.status === 'completed' && (
                <div className={styles.statusIcon}>✅</div>
              )}

              {file.status === 'error' && (
                <div className={styles.fileActions}>
                  <div className={styles.statusIcon}>❌</div>
                  <button
                    className={styles.retryButton}
                    onClick={() => onRetry?.(file.name)}
                    title="重试上传"
                  >
                    🔄
                  </button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Action feedback component
export interface ActionFeedbackProps extends BaseComponentProps {
  state: 'idle' | 'loading' | 'success' | 'error';
  loadingText?: string;
  successText?: string;
  errorText?: string;
  onRetry?: () => void;
  onReset?: () => void;
  autoResetDelay?: number;
}

export const ActionFeedback: React.FC<ActionFeedbackProps> = ({
  state,
  loadingText = '处理中...',
  successText = '操作成功！',
  errorText = '操作失败',
  onRetry,
  onReset,
  autoResetDelay = 3000,
  className = '',
  testId,
}) => {
  useEffect(() => {
    if (state === 'success' && autoResetDelay > 0) {
      const timer = setTimeout(() => {
        onReset?.();
      }, autoResetDelay);

      return () => clearTimeout(timer);
    }
  }, [state, autoResetDelay, onReset]);

  if (state === 'idle') {
    return null;
  }

  return (
    <div className={`${styles.actionFeedback} ${styles[state]} ${className}`} data-testid={testId}>
      {state === 'loading' && (
        <div className={styles.loadingState}>
          <LoadingSpinner size="medium" variant="primary" />
          <span className={styles.feedbackText}>{loadingText}</span>
        </div>
      )}

      {state === 'success' && (
        <SuccessFeedback
          message={successText}
          variant="checkmark"
          size="medium"
          onComplete={onReset}
        />
      )}

      {state === 'error' && (
        <div className={styles.errorState}>
          <div className={styles.errorIcon}>❌</div>
          <span className={styles.feedbackText}>{errorText}</span>
          {onRetry && (
            <button className={styles.retryButton} onClick={onRetry}>
              重试
            </button>
          )}
        </div>
      )}
    </div>
  );
};

// Form submission feedback
export interface FormFeedbackProps extends BaseComponentProps {
  isSubmitting: boolean;
  isSuccess: boolean;
  error?: string;
  submitText?: string;
  submittingText?: string;
  successText?: string;
  onReset?: () => void;
}

export const FormFeedback: React.FC<FormFeedbackProps> = ({
  isSubmitting,
  isSuccess,
  error,
  submitText = '提交',
  submittingText = '提交中...',
  successText = '提交成功！',
  onReset,
  className = '',
  testId,
}) => {
  if (isSuccess) {
    return (
      <div className={`${styles.formFeedback} ${styles.success} ${className}`} data-testid={testId}>
        <SuccessFeedback
          message={successText}
          variant="checkmark"
          size="medium"
          onComplete={onReset}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${styles.formFeedback} ${styles.error} ${className}`} data-testid={testId}>
        <div className={styles.errorMessage}>
          <span className={styles.errorIcon}>❌</span>
          <span className={styles.errorText}>{error}</span>
        </div>
      </div>
    );
  }

  if (isSubmitting) {
    return (
      <div className={`${styles.formFeedback} ${styles.submitting} ${className}`} data-testid={testId}>
        <LoadingSpinner size="small" variant="primary" inline />
        <span className={styles.submittingText}>{submittingText}</span>
      </div>
    );
  }

  return null;
};

// Data loading skeleton
export interface DataSkeletonProps extends BaseComponentProps {
  type: 'list' | 'card' | 'table' | 'profile' | 'custom';
  count?: number;
  customLayout?: React.ReactNode;
}

export const DataSkeleton: React.FC<DataSkeletonProps> = ({
  type,
  count = 3,
  customLayout,
  className = '',
  testId,
}) => {
  if (type === 'custom' && customLayout) {
    return (
      <div className={`${styles.dataSkeleton} ${className}`} data-testid={testId}>
        {customLayout}
      </div>
    );
  }

  const renderSkeletonItem = (index: number) => {
    switch (type) {
      case 'list':
        return (
          <div key={index} className={styles.listItem}>
            <div className={styles.skeletonAvatar} />
            <div className={styles.skeletonContent}>
              <div className={styles.skeletonTitle} />
              <div className={styles.skeletonText} />
            </div>
          </div>
        );

      case 'card':
        return (
          <div key={index} className={styles.cardItem}>
            <div className={styles.skeletonImage} />
            <div className={styles.skeletonContent}>
              <div className={styles.skeletonTitle} />
              <div className={styles.skeletonText} />
              <div className={styles.skeletonText} style={{ width: '60%' }} />
            </div>
          </div>
        );

      case 'table':
        return (
          <div key={index} className={styles.tableRow}>
            {[1, 2, 3, 4].map((col) => (
              <div key={col} className={styles.tableCell}>
                <div className={styles.skeletonText} />
              </div>
            ))}
          </div>
        );

      case 'profile':
        return (
          <div key={index} className={styles.profileItem}>
            <div className={styles.skeletonAvatar} style={{ width: '80px', height: '80px' }} />
            <div className={styles.skeletonContent}>
              <div className={styles.skeletonTitle} style={{ width: '40%' }} />
              <div className={styles.skeletonText} />
              <div className={styles.skeletonText} style={{ width: '80%' }} />
            </div>
          </div>
        );

      default:
        return (
          <div key={index} className={styles.defaultItem}>
            <div className={styles.skeletonText} />
          </div>
        );
    }
  };

  return (
    <div className={`${styles.dataSkeleton} ${styles[type]} ${className}`} data-testid={testId}>
      {Array.from({ length: count }, (_, index) => renderSkeletonItem(index))}
    </div>
  );
};