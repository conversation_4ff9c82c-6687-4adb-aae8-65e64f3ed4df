import React from 'react';
import { useNetworkStatus } from '../../../hooks/useNetworkStatus';
import type { BaseComponentProps } from '../../../types';
import styles from './NetworkStatus.module.scss';

export interface NetworkStatusProps extends BaseComponentProps {
  showDetails?: boolean;
  showQuality?: boolean;
  position?: 'top' | 'bottom' | 'inline';
  variant?: 'minimal' | 'detailed' | 'badge';
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({
  showDetails = false,
  showQuality = false,
  position = 'top',
  variant = 'minimal',
  className = '',
  testId,
}) => {
  const {
    isOnline,
    connectionType,
    effectiveType,
    downlink,
    rtt,
    connectionQuality,
  } = useNetworkStatus({
    showOfflineToast: false, // Don't show toast from this component
    showOnlineToast: false,
  });

  const getStatusIcon = () => {
    if (!isOnline) return '📡';
    
    switch (connectionQuality) {
      case 'excellent': return '📶';
      case 'good': return '📶';
      case 'fair': return '📶';
      case 'poor': return '📶';
      default: return '📡';
    }
  };

  const getStatusText = () => {
    if (!isOnline) return '离线';
    
    switch (connectionQuality) {
      case 'excellent': return '网络优秀';
      case 'good': return '网络良好';
      case 'fair': return '网络一般';
      case 'poor': return '网络较差';
      default: return '在线';
    }
  };

  const getQualityColor = () => {
    if (!isOnline) return 'offline';
    
    switch (connectionQuality) {
      case 'excellent': return 'excellent';
      case 'good': return 'good';
      case 'fair': return 'fair';
      case 'poor': return 'poor';
      default: return 'unknown';
    }
  };

  if (variant === 'badge') {
    return (
      <div
        className={`${styles.networkBadge} ${styles[getQualityColor()]} ${className}`}
        data-testid={testId}
      >
        <span className={styles.badgeIcon}>{getStatusIcon()}</span>
        {!isOnline && <span className={styles.badgeText}>离线</span>}
      </div>
    );
  }

  if (variant === 'minimal') {
    return (
      <div
        className={`${styles.networkMinimal} ${styles[position]} ${styles[getQualityColor()]} ${className}`}
        data-testid={testId}
        title={getStatusText()}
      >
        <span className={styles.statusIcon}>{getStatusIcon()}</span>
        <span className={styles.statusText}>{getStatusText()}</span>
      </div>
    );
  }

  // Detailed variant
  return (
    <div
      className={`${styles.networkDetailed} ${styles[position]} ${styles[getQualityColor()]} ${className}`}
      data-testid={testId}
    >
      <div className={styles.statusHeader}>
        <span className={styles.statusIcon}>{getStatusIcon()}</span>
        <span className={styles.statusText}>{getStatusText()}</span>
      </div>

      {showDetails && isOnline && (
        <div className={styles.statusDetails}>
          {connectionType && (
            <div className={styles.detailItem}>
              <span className={styles.detailLabel}>连接类型:</span>
              <span className={styles.detailValue}>{connectionType}</span>
            </div>
          )}
          
          {effectiveType && (
            <div className={styles.detailItem}>
              <span className={styles.detailLabel}>网络类型:</span>
              <span className={styles.detailValue}>{effectiveType.toUpperCase()}</span>
            </div>
          )}
          
          {downlink !== undefined && (
            <div className={styles.detailItem}>
              <span className={styles.detailLabel}>下载速度:</span>
              <span className={styles.detailValue}>{downlink.toFixed(1)} Mbps</span>
            </div>
          )}
          
          {rtt !== undefined && (
            <div className={styles.detailItem}>
              <span className={styles.detailLabel}>延迟:</span>
              <span className={styles.detailValue}>{rtt}ms</span>
            </div>
          )}
        </div>
      )}

      {showQuality && (
        <div className={styles.qualityIndicator}>
          <div className={styles.qualityBars}>
            {[1, 2, 3, 4].map((bar) => (
              <div
                key={bar}
                className={`${styles.qualityBar} ${
                  getQualityLevel() >= bar ? styles.active : ''
                }`}
              />
            ))}
          </div>
          <span className={styles.qualityText}>{connectionQuality}</span>
        </div>
      )}
    </div>
  );

  function getQualityLevel(): number {
    if (!isOnline) return 0;
    
    switch (connectionQuality) {
      case 'excellent': return 4;
      case 'good': return 3;
      case 'fair': return 2;
      case 'poor': return 1;
      default: return 0;
    }
  }
};

// Offline banner component
export const OfflineBanner: React.FC<BaseComponentProps> = ({
  className = '',
  testId,
}) => {
  const { isOnline } = useNetworkStatus({
    showOfflineToast: false,
    showOnlineToast: false,
  });

  if (isOnline) return null;

  return (
    <div
      className={`${styles.offlineBanner} ${className}`}
      data-testid={testId}
      role="alert"
      aria-live="polite"
    >
      <div className={styles.bannerContent}>
        <span className={styles.bannerIcon}>📡</span>
        <span className={styles.bannerText}>
          您当前处于离线状态，某些功能可能不可用
        </span>
      </div>
    </div>
  );
};

// Connection quality indicator
export const ConnectionQualityIndicator: React.FC<BaseComponentProps> = ({
  className = '',
  testId,
}) => {
  const { isOnline, connectionQuality } = useNetworkStatus();

  if (!isOnline) return null;

  const getQualityLevel = (): number => {
    switch (connectionQuality) {
      case 'excellent': return 4;
      case 'good': return 3;
      case 'fair': return 2;
      case 'poor': return 1;
      default: return 0;
    }
  };

  return (
    <div
      className={`${styles.qualityIndicator} ${className}`}
      data-testid={testId}
      title={`网络质量: ${connectionQuality}`}
    >
      <div className={styles.qualityBars}>
        {[1, 2, 3, 4].map((bar) => (
          <div
            key={bar}
            className={`${styles.qualityBar} ${
              getQualityLevel() >= bar ? styles.active : ''
            } ${styles[connectionQuality]}`}
          />
        ))}
      </div>
    </div>
  );
};