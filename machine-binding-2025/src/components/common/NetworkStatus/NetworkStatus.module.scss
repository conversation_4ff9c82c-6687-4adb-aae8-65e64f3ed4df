@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

// Base network status styles
.networkMinimal,
.networkDetailed {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: 0.875rem;
  transition: all 0.3s ease;

  &.top {
    position: fixed;
    top: vars.$spacing-sm;
    right: vars.$spacing-sm;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid vars.$color-border;
    box-shadow: vars.$shadow-sm;
  }

  &.bottom {
    position: fixed;
    bottom: vars.$spacing-sm;
    right: vars.$spacing-sm;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid vars.$color-border;
    box-shadow: vars.$shadow-sm;
  }

  &.inline {
    position: relative;
    background: transparent;
  }

  @include mixins.mobile-only {
    font-size: 0.8rem;
    padding: vars.$spacing-xs;
    
    &.top,
    &.bottom {
      right: vars.$spacing-xs;
    }
    
    &.top {
      top: vars.$spacing-xs;
    }
    
    &.bottom {
      bottom: vars.$spacing-xs;
    }
  }
}

// Status colors
.excellent {
  color: vars.$color-success;
  border-color: vars.$color-success;
  
  .statusIcon {
    color: vars.$color-success;
  }
}

.good {
  color: #52c41a;
  border-color: #52c41a;
  
  .statusIcon {
    color: #52c41a;
  }
}

.fair {
  color: vars.$color-warning;
  border-color: vars.$color-warning;
  
  .statusIcon {
    color: vars.$color-warning;
  }
}

.poor {
  color: #ff7875;
  border-color: #ff7875;
  
  .statusIcon {
    color: #ff7875;
  }
}

.offline {
  color: vars.$color-error;
  border-color: vars.$color-error;
  
  .statusIcon {
    color: vars.$color-error;
  }
}

.unknown {
  color: vars.$color-text-secondary;
  border-color: vars.$color-border;
  
  .statusIcon {
    color: vars.$color-text-secondary;
  }
}

// Status elements
.statusIcon {
  font-size: 1rem;
  
  @include mixins.mobile-only {
    font-size: 0.9rem;
  }
}

.statusText {
  font-weight: 500;
  white-space: nowrap;
  
  @include mixins.mobile-only {
    display: none; // Hide text on mobile for minimal variant
  }
}

// Detailed variant
.networkDetailed {
  flex-direction: column;
  align-items: flex-start;
  min-width: 200px;
  
  @include mixins.mobile-only {
    min-width: 150px;
  }
}

.statusHeader {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  width: 100%;
}

.statusDetails {
  margin-top: vars.$spacing-xs;
  padding-top: vars.$spacing-xs;
  border-top: 1px solid vars.$color-border;
  width: 100%;
}

.detailItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: vars.$spacing-xs;
  font-size: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  @include mixins.mobile-only {
    font-size: 0.7rem;
  }
}

.detailLabel {
  color: vars.$color-text-secondary;
  font-weight: 400;
}

.detailValue {
  color: vars.$color-text-primary;
  font-weight: 500;
}

// Quality indicator
.qualityIndicator {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  margin-top: vars.$spacing-xs;
  padding-top: vars.$spacing-xs;
  border-top: 1px solid vars.$color-border;
  width: 100%;
}

.qualityBars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.qualityBar {
  width: 4px;
  background-color: vars.$color-border;
  border-radius: 2px;
  transition: background-color 0.3s ease;
  
  &:nth-child(1) { height: 6px; }
  &:nth-child(2) { height: 8px; }
  &:nth-child(3) { height: 10px; }
  &:nth-child(4) { height: 12px; }
  
  &.active {
    &.excellent { background-color: vars.$color-success; }
    &.good { background-color: #52c41a; }
    &.fair { background-color: vars.$color-warning; }
    &.poor { background-color: #ff7875; }
  }
}

.qualityText {
  font-size: 0.7rem;
  color: vars.$color-text-secondary;
  text-transform: capitalize;
}

// Badge variant
.networkBadge {
  display: inline-flex;
  align-items: center;
  gap: vars.$spacing-xs;
  padding: vars.$spacing-xs;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  justify-content: center;
  transition: all 0.3s ease;
  
  &.offline {
    background-color: rgba(vars.$color-error, 0.1);
    border: 1px solid vars.$color-error;
    
    .badgeIcon {
      color: vars.$color-error;
    }
  }
  
  &:not(.offline) {
    background-color: rgba(vars.$color-success, 0.1);
    border: 1px solid vars.$color-success;
    
    .badgeIcon {
      color: vars.$color-success;
    }
  }
  
  @include mixins.mobile-only {
    width: 28px;
    height: 28px;
  }
}

.badgeIcon {
  font-size: 0.875rem;
  
  @include mixins.mobile-only {
    font-size: 0.8rem;
  }
}

.badgeText {
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
  margin-left: vars.$spacing-xs;
  
  @include mixins.mobile-only {
    display: none;
  }
}

// Offline banner
.offlineBanner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  z-index: 1001;
  box-shadow: vars.$shadow-md;
  animation: slideDown 0.3s ease-out;
  
  @include mixins.mobile-only {
    font-size: 0.875rem;
  }
}

.bannerContent {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: vars.$spacing-sm;
  padding: vars.$spacing-sm vars.$spacing-md;
  max-width: 1200px;
  margin: 0 auto;
  
  @include mixins.mobile-only {
    padding: vars.$spacing-xs vars.$spacing-sm;
    gap: vars.$spacing-xs;
  }
}

.bannerIcon {
  font-size: 1.1rem;
  
  @include mixins.mobile-only {
    font-size: 1rem;
  }
}

.bannerText {
  font-weight: 500;
  text-align: center;
  
  @include mixins.mobile-only {
    font-size: 0.85rem;
  }
}

// Animations
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Pulse animation for poor connection
.poor {
  .statusIcon {
    animation: pulse 2s infinite;
  }
}

// Responsive adjustments
@include mixins.tablet-up {
  .networkMinimal,
  .networkDetailed {
    &.top,
    &.bottom {
      right: vars.$spacing-md;
    }
  }
  
  .statusText {
    display: inline; // Show text on tablet
  }
}

@include mixins.mobile-large-up {
  .networkDetailed {
    min-width: 180px;
  }
  
  .detailItem {
    font-size: 0.75rem;
  }
}