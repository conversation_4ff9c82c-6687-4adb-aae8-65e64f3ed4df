@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

// Base network status styles
.networkMinimal,
.networkDetailed {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: 0.875rem;
  transition: all 0.3s ease;

  &.top {
    position: fixed;
    top: $spacing-sm;
    right: $spacing-sm;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid $color-border;
    box-shadow: $shadow-sm;
  }

  &.bottom {
    position: fixed;
    bottom: $spacing-sm;
    right: $spacing-sm;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid $color-border;
    box-shadow: $shadow-sm;
  }

  &.inline {
    position: relative;
    background: transparent;
  }

  @include mobile-only {
    font-size: 0.8rem;
    padding: $spacing-xs;
    
    &.top,
    &.bottom {
      right: $spacing-xs;
    }
    
    &.top {
      top: $spacing-xs;
    }
    
    &.bottom {
      bottom: $spacing-xs;
    }
  }
}

// Status colors
.excellent {
  color: $color-success;
  border-color: $color-success;
  
  .statusIcon {
    color: $color-success;
  }
}

.good {
  color: #52c41a;
  border-color: #52c41a;
  
  .statusIcon {
    color: #52c41a;
  }
}

.fair {
  color: $color-warning;
  border-color: $color-warning;
  
  .statusIcon {
    color: $color-warning;
  }
}

.poor {
  color: #ff7875;
  border-color: #ff7875;
  
  .statusIcon {
    color: #ff7875;
  }
}

.offline {
  color: $color-error;
  border-color: $color-error;
  
  .statusIcon {
    color: $color-error;
  }
}

.unknown {
  color: $color-text-secondary;
  border-color: $color-border;
  
  .statusIcon {
    color: $color-text-secondary;
  }
}

// Status elements
.statusIcon {
  font-size: 1rem;
  
  @include mobile-only {
    font-size: 0.9rem;
  }
}

.statusText {
  font-weight: 500;
  white-space: nowrap;
  
  @include mobile-only {
    display: none; // Hide text on mobile for minimal variant
  }
}

// Detailed variant
.networkDetailed {
  flex-direction: column;
  align-items: flex-start;
  min-width: 200px;
  
  @include mobile-only {
    min-width: 150px;
  }
}

.statusHeader {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  width: 100%;
}

.statusDetails {
  margin-top: $spacing-xs;
  padding-top: $spacing-xs;
  border-top: 1px solid $color-border;
  width: 100%;
}

.detailItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-xs;
  font-size: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  @include mobile-only {
    font-size: 0.7rem;
  }
}

.detailLabel {
  color: $color-text-secondary;
  font-weight: 400;
}

.detailValue {
  color: $color-text-primary;
  font-weight: 500;
}

// Quality indicator
.qualityIndicator {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-top: $spacing-xs;
  padding-top: $spacing-xs;
  border-top: 1px solid $color-border;
  width: 100%;
}

.qualityBars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.qualityBar {
  width: 4px;
  background-color: $color-border;
  border-radius: 2px;
  transition: background-color 0.3s ease;
  
  &:nth-child(1) { height: 6px; }
  &:nth-child(2) { height: 8px; }
  &:nth-child(3) { height: 10px; }
  &:nth-child(4) { height: 12px; }
  
  &.active {
    &.excellent { background-color: $color-success; }
    &.good { background-color: #52c41a; }
    &.fair { background-color: $color-warning; }
    &.poor { background-color: #ff7875; }
  }
}

.qualityText {
  font-size: 0.7rem;
  color: $color-text-secondary;
  text-transform: capitalize;
}

// Badge variant
.networkBadge {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  justify-content: center;
  transition: all 0.3s ease;
  
  &.offline {
    background-color: rgba($color-error, 0.1);
    border: 1px solid $color-error;
    
    .badgeIcon {
      color: $color-error;
    }
  }
  
  &:not(.offline) {
    background-color: rgba($color-success, 0.1);
    border: 1px solid $color-success;
    
    .badgeIcon {
      color: $color-success;
    }
  }
  
  @include mobile-only {
    width: 28px;
    height: 28px;
  }
}

.badgeIcon {
  font-size: 0.875rem;
  
  @include mobile-only {
    font-size: 0.8rem;
  }
}

.badgeText {
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
  margin-left: $spacing-xs;
  
  @include mobile-only {
    display: none;
  }
}

// Offline banner
.offlineBanner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  z-index: 1001;
  box-shadow: $shadow-md;
  animation: slideDown 0.3s ease-out;
  
  @include mobile-only {
    font-size: 0.875rem;
  }
}

.bannerContent {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  max-width: 1200px;
  margin: 0 auto;
  
  @include mobile-only {
    padding: $spacing-xs $spacing-sm;
    gap: $spacing-xs;
  }
}

.bannerIcon {
  font-size: 1.1rem;
  
  @include mobile-only {
    font-size: 1rem;
  }
}

.bannerText {
  font-weight: 500;
  text-align: center;
  
  @include mobile-only {
    font-size: 0.85rem;
  }
}

// Animations
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Pulse animation for poor connection
.poor {
  .statusIcon {
    animation: pulse 2s infinite;
  }
}

// Responsive adjustments
@include tablet-up {
  .networkMinimal,
  .networkDetailed {
    &.top,
    &.bottom {
      right: $spacing-md;
    }
  }
  
  .statusText {
    display: inline; // Show text on tablet
  }
}

@include mobile-large-up {
  .networkDetailed {
    min-width: 180px;
  }
  
  .detailItem {
    font-size: 0.75rem;
  }
}