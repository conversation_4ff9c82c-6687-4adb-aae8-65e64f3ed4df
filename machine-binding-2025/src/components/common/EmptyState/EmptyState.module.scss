@import '../../../styles/variables.scss';

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: $spacing-xxl;
  color: $text-muted;
}

.icon {
  font-size: 4rem;
  margin-bottom: $spacing-lg;
  opacity: 0.6;
}

.title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-secondary;
  margin: 0 0 $spacing-md 0;
}

.description {
  font-size: $font-size-md;
  color: $text-muted;
  margin: 0 0 $spacing-lg 0;
  max-width: 400px;
  line-height: 1.5;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  align-items: center;
}

// Size variants
.emptyState.small {
  padding: $spacing-lg;

  .icon {
    font-size: 2.5rem;
    margin-bottom: $spacing-md;
  }

  .title {
    font-size: $font-size-md;
    margin-bottom: $spacing-sm;
  }

  .description {
    font-size: $font-size-sm;
    margin-bottom: $spacing-md;
  }
}

.emptyState.large {
  padding: $spacing-xxl * 2;

  .icon {
    font-size: 6rem;
    margin-bottom: $spacing-xl;
  }

  .title {
    font-size: $font-size-xl;
    margin-bottom: $spacing-lg;
  }

  .description {
    font-size: $font-size-lg;
    margin-bottom: $spacing-xl;
  }
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .emptyState {
    padding: $spacing-xl $spacing-lg;
  }

  .emptyState.large {
    padding: $spacing-xxl $spacing-lg;
  }

  .icon {
    font-size: 3rem;
  }

  .title {
    font-size: $font-size-md;
  }

  .description {
    font-size: $font-size-sm;
  }

  .actions {
    width: 100%;

    button {
      width: 100%;
      max-width: 300px;
    }
  }
}

// Animation
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.emptyState {
  animation: fadeIn 0.5s ease-out;
}