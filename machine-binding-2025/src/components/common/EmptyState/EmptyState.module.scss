@use "../../../styles/variables" as vars;

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: vars.$spacing-xxl;
  color: vars.$text-muted;
}

.icon {
  font-size: 4rem;
  margin-bottom: vars.$spacing-lg;
  opacity: 0.6;
}

.title {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-secondary;
  margin: 0 0 vars.$spacing-md 0;
}

.description {
  font-size: vars.$font-size-md;
  color: vars.$text-muted;
  margin: 0 0 vars.$spacing-lg 0;
  max-width: 400px;
  line-height: 1.5;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-md;
  align-items: center;
}

// Size variants
.emptyState.small {
  padding: vars.$spacing-lg;

  .icon {
    font-size: 2.5rem;
    margin-bottom: vars.$spacing-md;
  }

  .title {
    font-size: vars.$font-size-md;
    margin-bottom: vars.$spacing-sm;
  }

  .description {
    font-size: vars.$font-size-sm;
    margin-bottom: vars.$spacing-md;
  }
}

.emptyState.large {
  padding: vars.$spacing-xxl * 2;

  .icon {
    font-size: 6rem;
    margin-bottom: vars.$spacing-xl;
  }

  .title {
    font-size: vars.$font-size-xl;
    margin-bottom: vars.$spacing-lg;
  }

  .description {
    font-size: vars.$font-size-lg;
    margin-bottom: vars.$spacing-xl;
  }
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .emptyState {
    padding: vars.$spacing-xl vars.$spacing-lg;
  }

  .emptyState.large {
    padding: vars.$spacing-xxl vars.$spacing-lg;
  }

  .icon {
    font-size: 3rem;
  }

  .title {
    font-size: vars.$font-size-md;
  }

  .description {
    font-size: vars.$font-size-sm;
  }

  .actions {
    width: 100%;

    button {
      width: 100%;
      max-width: 300px;
    }
  }
}

// Animation
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.emptyState {
  animation: fadeIn 0.5s ease-out;
}