import React from 'react';
import { BaseComponentProps } from '../../../types';
import styles from './EmptyState.module.scss';

export interface EmptyStateProps extends BaseComponentProps {
  icon?: string | React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = '📭',
  title,
  description,
  actions,
  size = 'medium',
  className = '',
  testId,
}) => {
  const emptyStateClasses = [
    styles.emptyState,
    styles[size],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={emptyStateClasses} data-testid={testId}>
      <div className={styles.icon}>
        {typeof icon === 'string' ? icon : icon}
      </div>
      
      <h3 className={styles.title}>{title}</h3>
      
      {description && (
        <p className={styles.description}>{description}</p>
      )}
      
      {actions && (
        <div className={styles.actions}>
          {actions}
        </div>
      )}
    </div>
  );
};

// Predefined empty states for common scenarios
export const NoDataEmptyState: React.FC<{
  title?: string;
  description?: string;
  onRefresh?: () => void;
}> = ({
  title = '暂无数据',
  description = '当前没有可显示的内容',
  onRefresh,
}) => (
  <EmptyState
    icon="📋"
    title={title}
    description={description}
    actions={
      onRefresh && (
        <button
          onClick={onRefresh}
          style={{
            padding: '12px 24px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
        >
          刷新
        </button>
      )
    }
  />
);

export const NoSearchResultsEmptyState: React.FC<{
  searchTerm?: string;
  onClearSearch?: () => void;
}> = ({
  searchTerm,
  onClearSearch,
}) => (
  <EmptyState
    icon="🔍"
    title="未找到搜索结果"
    description={
      searchTerm
        ? `没有找到与 "${searchTerm}" 相关的内容`
        : '请尝试使用其他关键词搜索'
    }
    actions={
      onClearSearch && (
        <button
          onClick={onClearSearch}
          style={{
            padding: '8px 16px',
            backgroundColor: 'transparent',
            color: '#007bff',
            border: '1px solid #007bff',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
        >
          清除搜索
        </button>
      )
    }
  />
);

export const ErrorEmptyState: React.FC<{
  title?: string;
  description?: string;
  onRetry?: () => void;
}> = ({
  title = '加载失败',
  description = '数据加载时出现错误，请稍后重试',
  onRetry,
}) => (
  <EmptyState
    icon="⚠️"
    title={title}
    description={description}
    actions={
      onRetry && (
        <button
          onClick={onRetry}
          style={{
            padding: '12px 24px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
        >
          重试
        </button>
      )
    }
  />
);

export const NetworkErrorEmptyState: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => (
  <EmptyState
    icon="📡"
    title="网络连接失败"
    description="请检查您的网络连接并重试"
    actions={
      onRetry && (
        <button
          onClick={onRetry}
          style={{
            padding: '12px 24px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
        >
          重新连接
        </button>
      )
    }
  />
);