import React, { useState } from 'react';
import {
  <PERSON>ading<PERSON>pinner,
  ProgressIndicator,
  SuccessFeedback,
  LoadingButton,
  ActionFeedback,
  FormFeedback,
  DataSkeleton,
  UploadProgress,
} from '../';
import { useLoadingState, useAsyncWithLoading, useUploadProgress } from '../../../hooks';
import type { BaseComponentProps } from '../../../types';
import styles from './LoadingDemo.module.scss';

export interface LoadingDemoProps extends BaseComponentProps {
  // Demo props if needed
}

export const LoadingDemo: React.FC<LoadingDemoProps> = ({
  className = '',
  testId,
}) => {
  const [activeDemo, setActiveDemo] = useState<string>('spinners');
  const loadingState = useLoadingState();
  const asyncOperation = useAsyncWithLoading();
  const uploadProgress = useUploadProgress();
  const [actionState, setActionState] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [formState, setFormState] = useState({
    isSubmitting: false,
    isSuccess: false,
    error: '',
  });

  // Demo functions
  const simulateAsyncOperation = async () => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    if (Math.random() > 0.7) {
      throw new Error('模拟操作失败');
    }
    return '操作成功完成';
  };

  const handleActionDemo = async () => {
    setActionState('loading');
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      if (Math.random() > 0.7) {
        throw new Error('操作失败');
      }
      setActionState('success');
      setTimeout(() => setActionState('idle'), 3000);
    } catch (error) {
      setActionState('error');
      setTimeout(() => setActionState('idle'), 3000);
    }
  };

  const handleFormDemo = async () => {
    setFormState({ isSubmitting: true, isSuccess: false, error: '' });
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      if (Math.random() > 0.7) {
        throw new Error('表单提交失败');
      }
      setFormState({ isSubmitting: false, isSuccess: true, error: '' });
      setTimeout(() => {
        setFormState({ isSubmitting: false, isSuccess: false, error: '' });
      }, 3000);
    } catch (error) {
      setFormState({
        isSubmitting: false,
        isSuccess: false,
        error: (error as Error).message,
      });
    }
  };

  const handleUploadDemo = () => {
    const files = ['image1.jpg', 'image2.png', 'document.pdf'];
    
    files.forEach((fileName, index) => {
      const uploadId = `demo-${Date.now()}-${index}`;
      uploadProgress.startUpload(uploadId, fileName);
      
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          
          // Randomly succeed or fail
          if (Math.random() > 0.3) {
            uploadProgress.completeUpload(uploadId);
          } else {
            uploadProgress.failUpload(uploadId, '上传失败');
          }
        } else {
          uploadProgress.updateProgress(uploadId, progress);
        }
      }, 300);
    });
  };

  const handleProgressDemo = () => {
    loadingState.setProgress(0, '开始处理...');
    
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        loadingState.setSuccess('处理完成！');
      } else {
        loadingState.setProgress(progress, `处理中... ${Math.round(progress)}%`);
      }
    }, 200);
  };

  const demoSections = [
    { id: 'spinners', label: '加载动画' },
    { id: 'progress', label: '进度指示器' },
    { id: 'buttons', label: '加载按钮' },
    { id: 'feedback', label: '操作反馈' },
    { id: 'forms', label: '表单反馈' },
    { id: 'skeletons', label: '骨架屏' },
    { id: 'uploads', label: '上传进度' },
  ];

  return (
    <div className={`${styles.loadingDemo} ${className}`} data-testid={testId}>
      <div className={styles.demoHeader}>
        <h2>加载状态和用户反馈演示</h2>
        <div className={styles.demoTabs}>
          {demoSections.map(section => (
            <button
              key={section.id}
              className={`${styles.demoTab} ${activeDemo === section.id ? styles.active : ''}`}
              onClick={() => setActiveDemo(section.id)}
            >
              {section.label}
            </button>
          ))}
        </div>
      </div>

      <div className={styles.demoContent}>
        {/* Loading Spinners Demo */}
        {activeDemo === 'spinners' && (
          <div className={styles.demoSection}>
            <h3>加载动画</h3>
            <div className={styles.spinnerGrid}>
              <div className={styles.spinnerItem}>
                <LoadingSpinner size="small" variant="primary" />
                <span>小型 - 主色</span>
              </div>
              <div className={styles.spinnerItem}>
                <LoadingSpinner size="medium" variant="secondary" />
                <span>中型 - 次色</span>
              </div>
              <div className={styles.spinnerItem}>
                <LoadingSpinner size="large" variant="primary" text="加载中..." />
                <span>大型 - 带文字</span>
              </div>
              <div className={styles.spinnerItem}>
                <LoadingSpinner size="extraLarge" variant="primary" />
                <span>超大型</span>
              </div>
            </div>
          </div>
        )}

        {/* Progress Indicators Demo */}
        {activeDemo === 'progress' && (
          <div className={styles.demoSection}>
            <h3>进度指示器</h3>
            <div className={styles.progressGrid}>
              <div className={styles.progressItem}>
                <h4>线性进度条</h4>
                <ProgressIndicator progress={65} text="下载中" showPercentage />
                <ProgressIndicator progress={30} color="warning" text="处理中" />
                <ProgressIndicator progress={100} color="success" text="完成" />
              </div>
              <div className={styles.progressItem}>
                <h4>圆形进度条</h4>
                <ProgressIndicator 
                  progress={loadingState.progress || 0} 
                  variant="circular" 
                  text={loadingState.message}
                  showPercentage 
                />
                <button 
                  className={styles.demoButton}
                  onClick={handleProgressDemo}
                  disabled={loadingState.isLoading}
                >
                  开始进度演示
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Loading Buttons Demo */}
        {activeDemo === 'buttons' && (
          <div className={styles.demoSection}>
            <h3>加载按钮</h3>
            <div className={styles.buttonGrid}>
              <LoadingButton
                isLoading={asyncOperation.isLoading}
                onClick={() => asyncOperation.execute(simulateAsyncOperation)}
                variant="primary"
              >
                主要按钮
              </LoadingButton>
              <LoadingButton
                isLoading={false}
                variant="secondary"
                size="large"
              >
                次要按钮
              </LoadingButton>
              <LoadingButton
                isLoading={true}
                variant="outline"
                loadingText="处理中..."
                size="small"
              >
                轮廓按钮
              </LoadingButton>
            </div>
          </div>
        )}

        {/* Action Feedback Demo */}
        {activeDemo === 'feedback' && (
          <div className={styles.demoSection}>
            <h3>操作反馈</h3>
            <div className={styles.feedbackGrid}>
              <div className={styles.feedbackItem}>
                <button 
                  className={styles.demoButton}
                  onClick={handleActionDemo}
                  disabled={actionState === 'loading'}
                >
                  触发操作
                </button>
                <ActionFeedback
                  state={actionState}
                  loadingText="正在处理您的请求..."
                  successText="操作成功完成！"
                  errorText="操作失败，请重试"
                  onRetry={handleActionDemo}
                  onReset={() => setActionState('idle')}
                />
              </div>
              <div className={styles.feedbackItem}>
                <SuccessFeedback
                  message="文件上传成功！"
                  variant="celebration"
                  size="large"
                />
              </div>
            </div>
          </div>
        )}

        {/* Form Feedback Demo */}
        {activeDemo === 'forms' && (
          <div className={styles.demoSection}>
            <h3>表单反馈</h3>
            <div className={styles.formDemo}>
              <form onSubmit={(e) => { e.preventDefault(); handleFormDemo(); }}>
                <div className={styles.formField}>
                  <label>用户名</label>
                  <input type="text" placeholder="请输入用户名" />
                </div>
                <div className={styles.formField}>
                  <label>邮箱</label>
                  <input type="email" placeholder="请输入邮箱" />
                </div>
                <button 
                  type="submit" 
                  className={styles.submitButton}
                  disabled={formState.isSubmitting}
                >
                  {formState.isSubmitting ? '提交中...' : '提交'}
                </button>
                <FormFeedback
                  isSubmitting={formState.isSubmitting}
                  isSuccess={formState.isSuccess}
                  error={formState.error}
                  onReset={() => setFormState({ isSubmitting: false, isSuccess: false, error: '' })}
                />
              </form>
            </div>
          </div>
        )}

        {/* Skeleton Demo */}
        {activeDemo === 'skeletons' && (
          <div className={styles.demoSection}>
            <h3>骨架屏</h3>
            <div className={styles.skeletonGrid}>
              <div className={styles.skeletonItem}>
                <h4>列表骨架屏</h4>
                <DataSkeleton type="list" count={3} />
              </div>
              <div className={styles.skeletonItem}>
                <h4>卡片骨架屏</h4>
                <DataSkeleton type="card" count={2} />
              </div>
              <div className={styles.skeletonItem}>
                <h4>表格骨架屏</h4>
                <DataSkeleton type="table" count={4} />
              </div>
            </div>
          </div>
        )}

        {/* Upload Progress Demo */}
        {activeDemo === 'uploads' && (
          <div className={styles.demoSection}>
            <h3>上传进度</h3>
            <div className={styles.uploadDemo}>
              <button 
                className={styles.demoButton}
                onClick={handleUploadDemo}
              >
                开始上传演示
              </button>
              {uploadProgress.uploads.length > 0 && (
                <UploadProgress
                  files={uploadProgress.uploads.map(upload => ({
                    name: upload.name,
                    progress: upload.progress,
                    status: upload.status,
                    error: upload.error,
                  }))}
                  onCancel={(fileName) => {
                    const upload = uploadProgress.uploads.find(u => u.name === fileName);
                    if (upload) {
                      uploadProgress.removeUpload(upload.id);
                    }
                  }}
                  onRetry={(fileName) => {
                    console.log('Retry upload for:', fileName);
                  }}
                />
              )}
              <div className={styles.uploadStats}>
                <p>总进度: {Math.round(uploadProgress.getTotalProgress())}%</p>
                <p>完成: {uploadProgress.getCompletedCount()} / {uploadProgress.uploads.length}</p>
                <p>失败: {uploadProgress.getErrorCount()}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};