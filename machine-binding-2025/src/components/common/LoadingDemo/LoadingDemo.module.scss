@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.loadingDemo {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-lg;

  @include mobile {
    padding: $spacing-md;
  }
}

.demoHeader {
  margin-bottom: $spacing-xl;
  text-align: center;

  h2 {
    color: $color-text-primary;
    margin-bottom: $spacing-lg;
    font-size: 1.75rem;

    @include mobile {
      font-size: 1.5rem;
    }
  }
}

.demoTabs {
  display: flex;
  justify-content: center;
  gap: $spacing-sm;
  flex-wrap: wrap;

  @include mobile {
    gap: $spacing-xs;
  }
}

.demoTab {
  padding: $spacing-sm $spacing-md;
  border: 1px solid $color-border;
  background: $color-white;
  color: $color-text-secondary;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;

  &:hover {
    border-color: $color-primary;
    color: $color-primary;
  }

  &.active {
    background: $color-primary;
    color: white;
    border-color: $color-primary;
  }

  @include mobile {
    padding: $spacing-xs $spacing-sm;
    font-size: 0.8rem;
  }
}

.demoContent {
  background: $color-white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-md;
  border: 1px solid $color-border;

  @include mobile {
    padding: $spacing-lg;
    border-radius: $border-radius-md;
  }
}

.demoSection {
  h3 {
    color: $color-text-primary;
    margin-bottom: $spacing-lg;
    font-size: 1.25rem;
    border-bottom: 2px solid $color-primary;
    padding-bottom: $spacing-sm;

    @include mobile {
      font-size: 1.125rem;
    }
  }
}

// Spinner Grid
.spinnerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;

  @include mobile {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: $spacing-md;
  }
}

.spinnerItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-lg;
  border: 1px solid $color-border;
  border-radius: $border-radius-md;
  background: $color-background;

  span {
    font-size: 0.875rem;
    color: $color-text-secondary;
    text-align: center;
  }

  @include mobile {
    padding: $spacing-md;
    gap: $spacing-sm;
  }
}

// Progress Grid
.progressGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-lg;
  }
}

.progressItem {
  h4 {
    color: $color-text-primary;
    margin-bottom: $spacing-md;
    font-size: 1rem;
  }

  > * + * {
    margin-top: $spacing-md;
  }
}

// Button Grid
.buttonGrid {
  display: flex;
  gap: $spacing-lg;
  flex-wrap: wrap;
  align-items: center;

  @include mobile {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-md;
  }
}

// Feedback Grid
.feedbackGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-lg;
  }
}

.feedbackItem {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  padding: $spacing-lg;
  border: 1px solid $color-border;
  border-radius: $border-radius-md;
  background: $color-background;

  @include mobile {
    padding: $spacing-md;
  }
}

// Form Demo
.formDemo {
  max-width: 400px;
  margin: 0 auto;
}

.formField {
  margin-bottom: $spacing-md;

  label {
    display: block;
    margin-bottom: $spacing-xs;
    font-weight: 500;
    color: $color-text-primary;
  }

  input {
    width: 100%;
    padding: $spacing-sm;
    border: 1px solid $color-border;
    border-radius: $border-radius-sm;
    font-size: 1rem;
    transition: border-color 0.2s ease;

    &:focus {
      outline: none;
      border-color: $color-primary;
    }
  }
}

.submitButton {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  background: $color-primary;
  color: white;
  border: none;
  border-radius: $border-radius-md;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover:not(:disabled) {
    background: darken($color-primary, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Skeleton Grid
.skeletonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-lg;
  }
}

.skeletonItem {
  h4 {
    color: $color-text-primary;
    margin-bottom: $spacing-md;
    font-size: 1rem;
  }
}

// Upload Demo
.uploadDemo {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.uploadStats {
  padding: $spacing-md;
  background: $color-background;
  border-radius: $border-radius-md;
  border: 1px solid $color-border;

  p {
    margin: $spacing-xs 0;
    font-size: 0.875rem;
    color: $color-text-secondary;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Demo Button
.demoButton {
  padding: $spacing-sm $spacing-md;
  background: $color-primary;
  color: white;
  border: none;
  border-radius: $border-radius-md;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;

  &:hover:not(:disabled) {
    background: darken($color-primary, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Responsive adjustments
@include tablet {
  .demoTabs {
    justify-content: center;
  }

  .spinnerGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .progressGrid {
    grid-template-columns: 1fr;
  }

  .buttonGrid {
    justify-content: center;
  }
}

@include mobile-large {
  .loadingDemo {
    padding: $spacing-lg $spacing-md;
  }

  .demoContent {
    padding: $spacing-lg;
  }

  .skeletonGrid {
    grid-template-columns: 1fr;
  }
}