@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.loadingDemo {
  max-width: 1200px;
  margin: 0 auto;
  padding: vars.$spacing-lg;

  @include mixins.mobile {
    padding: vars.$spacing-md;
  }
}

.demoHeader {
  margin-bottom: vars.$spacing-xl;
  text-align: center;

  h2 {
    color: vars.$color-text-primary;
    margin-bottom: vars.$spacing-lg;
    font-size: 1.75rem;

    @include mixins.mobile {
      font-size: 1.5rem;
    }
  }
}

.demoTabs {
  display: flex;
  justify-content: center;
  gap: vars.$spacing-sm;
  flex-wrap: wrap;

  @include mixins.mobile {
    gap: vars.$spacing-xs;
  }
}

.demoTab {
  padding: vars.$spacing-sm vars.$spacing-md;
  border: 1px solid vars.$color-border;
  background: vars.$color-white;
  color: vars.$color-text-secondary;
  border-radius: vars.$border-radius-md;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;

  &:hover {
    border-color: vars.$color-primary;
    color: vars.$color-primary;
  }

  &.active {
    background: vars.$color-primary;
    color: white;
    border-color: vars.$color-primary;
  }

  @include mixins.mobile {
    padding: vars.$spacing-xs vars.$spacing-sm;
    font-size: 0.8rem;
  }
}

.demoContent {
  background: vars.$color-white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  box-shadow: vars.$shadow-md;
  border: 1px solid vars.$color-border;

  @include mixins.mobile {
    padding: vars.$spacing-lg;
    border-radius: vars.$border-radius-md;
  }
}

.demoSection {
  h3 {
    color: vars.$color-text-primary;
    margin-bottom: vars.$spacing-lg;
    font-size: 1.25rem;
    border-bottom: 2px solid vars.$color-primary;
    padding-bottom: vars.$spacing-sm;

    @include mixins.mobile {
      font-size: 1.125rem;
    }
  }
}

// Spinner Grid
.spinnerGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-lg;

  @include mixins.mobile {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: vars.$spacing-md;
  }
}

.spinnerItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: vars.$spacing-md;
  padding: vars.$spacing-lg;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-md;
  background: vars.$color-background;

  span {
    font-size: 0.875rem;
    color: vars.$color-text-secondary;
    text-align: center;
  }

  @include mixins.mobile {
    padding: vars.$spacing-md;
    gap: vars.$spacing-sm;
  }
}

// Progress Grid
.progressGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: vars.$spacing-xl;

  @include mixins.mobile {
    grid-template-columns: 1fr;
    gap: vars.$spacing-lg;
  }
}

.progressItem {
  h4 {
    color: vars.$color-text-primary;
    margin-bottom: vars.$spacing-md;
    font-size: 1rem;
  }

  > * + * {
    margin-top: vars.$spacing-md;
  }
}

// Button Grid
.buttonGrid {
  display: flex;
  gap: vars.$spacing-lg;
  flex-wrap: wrap;
  align-items: center;

  @include mixins.mobile {
    flex-direction: column;
    align-items: stretch;
    gap: vars.$spacing-md;
  }
}

// Feedback Grid
.feedbackGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: vars.$spacing-xl;

  @include mixins.mobile {
    grid-template-columns: 1fr;
    gap: vars.$spacing-lg;
  }
}

.feedbackItem {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-md;
  padding: vars.$spacing-lg;
  border: 1px solid vars.$color-border;
  border-radius: vars.$border-radius-md;
  background: vars.$color-background;

  @include mixins.mobile {
    padding: vars.$spacing-md;
  }
}

// Form Demo
.formDemo {
  max-width: 400px;
  margin: 0 auto;
}

.formField {
  margin-bottom: vars.$spacing-md;

  label {
    display: block;
    margin-bottom: vars.$spacing-xs;
    font-weight: 500;
    color: vars.$color-text-primary;
  }

  input {
    width: 100%;
    padding: vars.$spacing-sm;
    border: 1px solid vars.$color-border;
    border-radius: vars.$border-radius-sm;
    font-size: 1rem;
    transition: border-color 0.2s ease;

    &:focus {
      outline: none;
      border-color: vars.$color-primary;
    }
  }
}

.submitButton {
  width: 100%;
  padding: vars.$spacing-sm vars.$spacing-md;
  background: vars.$color-primary;
  color: white;
  border: none;
  border-radius: vars.$border-radius-md;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover:not(:disabled) {
    background: darken(vars.$color-primary, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Skeleton Grid
.skeletonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: vars.$spacing-xl;

  @include mixins.mobile {
    grid-template-columns: 1fr;
    gap: vars.$spacing-lg;
  }
}

.skeletonItem {
  h4 {
    color: vars.$color-text-primary;
    margin-bottom: vars.$spacing-md;
    font-size: 1rem;
  }
}

// Upload Demo
.uploadDemo {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-lg;
}

.uploadStats {
  padding: vars.$spacing-md;
  background: vars.$color-background;
  border-radius: vars.$border-radius-md;
  border: 1px solid vars.$color-border;

  p {
    margin: vars.$spacing-xs 0;
    font-size: 0.875rem;
    color: vars.$color-text-secondary;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Demo Button
.demoButton {
  padding: vars.$spacing-sm vars.$spacing-md;
  background: vars.$color-primary;
  color: white;
  border: none;
  border-radius: vars.$border-radius-md;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;

  &:hover:not(:disabled) {
    background: darken(vars.$color-primary, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Responsive adjustments
@include mixins.tablet {
  .demoTabs {
    justify-content: center;
  }

  .spinnerGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .progressGrid {
    grid-template-columns: 1fr;
  }

  .buttonGrid {
    justify-content: center;
  }
}

@include mixins.mobile-large {
  .loadingDemo {
    padding: vars.$spacing-lg vars.$spacing-md;
  }

  .demoContent {
    padding: vars.$spacing-lg;
  }

  .skeletonGrid {
    grid-template-columns: 1fr;
  }
}