@use "../../../styles/variables" as vars;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: vars.$z-index-modal;
  padding: vars.$spacing-lg;
  backdrop-filter: blur(2px);
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: white;
  border-radius: vars.$border-radius-lg;
  box-shadow: vars.$shadow-lg;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

// Modal sizes
.modal.small {
  width: 400px;
}

.modal.medium {
  width: 600px;
}

.modal.large {
  width: 800px;
}

.modal.extraLarge {
  width: 1000px;
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: vars.$spacing-lg;
  border-bottom: 1px solid #eee;
  background: vars.$background-secondary;
}

.modalTitle {
  margin: 0;
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
}

.closeButton {
  background: none;
  border: none;
  color: vars.$text-muted;
  cursor: pointer;
  padding: vars.$spacing-xs;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 20px;

  &:hover {
    background-color: rgba(vars.$text-muted, 0.1);
    color: vars.$text-primary;
  }

  &:focus {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

.modalBody {
  flex: 1;
  padding: vars.$spacing-lg;
  overflow-y: auto;
}

.modalFooter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: vars.$spacing-md;
  padding: vars.$spacing-lg;
  border-top: 1px solid #eee;
  background: vars.$background-secondary;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .modalOverlay {
    padding: vars.$spacing-md;
    align-items: flex-end;
  }

  .modal {
    width: 100% !important;
    max-width: none;
    max-height: 80vh;
    border-radius: vars.$border-radius-lg vars.$border-radius-lg 0 0;
    animation: slideUpMobile 0.3s ease-out;
  }

  .modalHeader,
  .modalBody,
  .modalFooter {
    padding: vars.$spacing-md;
  }

  .modalTitle {
    font-size: vars.$font-size-md;
  }

  .modalFooter {
    flex-direction: column-reverse;
    gap: vars.$spacing-sm;

    button {
      width: 100%;
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideUpMobile {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalOverlay.exiting {
  animation: fadeOut 0.3s ease-in forwards;
}

.modal.exiting {
  animation: slideDown 0.3s ease-in forwards;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}