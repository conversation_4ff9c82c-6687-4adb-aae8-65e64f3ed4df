import React from 'react';
import { BaseComponentProps } from '../../../types';
import styles from './LoadingSpinner.module.scss';

export interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'small' | 'medium' | 'large' | 'extraLarge';
  variant?: 'primary' | 'secondary' | 'white' | 'dark';
  text?: string;
  overlay?: boolean;
  fullscreen?: boolean;
  inline?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  variant = 'primary',
  text,
  overlay = false,
  fullscreen = false,
  inline = false,
  className = '',
  testId,
}) => {
  const spinnerClasses = [
    styles.spinner,
    styles[size],
    styles[variant],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  const spinner = <div className={spinnerClasses} data-testid={testId} />;

  if (inline) {
    return (
      <div className={styles.inline}>
        {spinner}
        {text && <span className={styles.loadingText}>{text}</span>}
      </div>
    );
  }

  const content = (
    <div className={styles.loadingContainer}>
      {spinner}
      {text && <p className={styles.loadingText}>{text}</p>}
    </div>
  );

  if (fullscreen) {
    return <div className={styles.fullscreen}>{content}</div>;
  }

  if (overlay) {
    return <div className={styles.overlay}>{content}</div>;
  }

  return content;
};

// Skeleton loading components
export interface SkeletonProps extends BaseComponentProps {
  variant?: 'text' | 'title' | 'paragraph' | 'avatar' | 'button' | 'card';
  width?: string | number;
  height?: string | number;
  count?: number;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width,
  height,
  count = 1,
  className = '',
  testId,
}) => {
  const skeletonClasses = [
    styles.skeleton,
    styles[variant],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  if (count === 1) {
    return (
      <div
        className={skeletonClasses}
        style={style}
        data-testid={testId}
      />
    );
  }

  return (
    <div data-testid={testId}>
      {Array.from({ length: count }, (_, index) => (
        <div
          key={index}
          className={skeletonClasses}
          style={style}
        />
      ))}
    </div>
  );
};

// Loading overlay for containers
export interface LoadingOverlayProps extends BaseComponentProps {
  isLoading: boolean;
  text?: string;
  spinnerSize?: LoadingSpinnerProps['size'];
  spinnerVariant?: LoadingSpinnerProps['variant'];
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  children,
  isLoading,
  text = '加载中...',
  spinnerSize = 'large',
  spinnerVariant = 'primary',
  className = '',
  testId,
}) => {
  return (
    <div style={{ position: 'relative' }} className={className} data-testid={testId}>
      {children}
      {isLoading && (
        <LoadingSpinner
          size={spinnerSize}
          variant={spinnerVariant}
          text={text}
          overlay
        />
      )}
    </div>
  );
};

// Progress indicator component
export interface ProgressIndicatorProps extends BaseComponentProps {
  progress: number; // 0-100
  variant?: 'linear' | 'circular';
  size?: 'small' | 'medium' | 'large';
  showPercentage?: boolean;
  text?: string;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  animated?: boolean;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  variant = 'linear',
  size = 'medium',
  showPercentage = true,
  text,
  color = 'primary',
  animated = true,
  className = '',
  testId,
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  if (variant === 'circular') {
    const radius = size === 'small' ? 20 : size === 'large' ? 40 : 30;
    const strokeWidth = size === 'small' ? 3 : size === 'large' ? 6 : 4;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (clampedProgress / 100) * circumference;

    return (
      <div className={`${styles.progressContainer} ${styles.circular} ${styles[size]} ${className}`} data-testid={testId}>
        <svg
          className={styles.progressCircle}
          width={(radius + strokeWidth) * 2}
          height={(radius + strokeWidth) * 2}
        >
          <circle
            className={styles.progressTrack}
            cx={radius + strokeWidth}
            cy={radius + strokeWidth}
            r={radius}
            strokeWidth={strokeWidth}
          />
          <circle
            className={`${styles.progressFill} ${styles[color]} ${animated ? styles.animated : ''}`}
            cx={radius + strokeWidth}
            cy={radius + strokeWidth}
            r={radius}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
          />
        </svg>
        {showPercentage && (
          <div className={styles.progressText}>
            <span className={styles.percentage}>{Math.round(clampedProgress)}%</span>
            {text && <span className={styles.label}>{text}</span>}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${styles.progressContainer} ${styles.linear} ${styles[size]} ${className}`} data-testid={testId}>
      {(showPercentage || text) && (
        <div className={styles.progressHeader}>
          {text && <span className={styles.label}>{text}</span>}
          {showPercentage && <span className={styles.percentage}>{Math.round(clampedProgress)}%</span>}
        </div>
      )}
      <div className={styles.progressTrack}>
        <div
          className={`${styles.progressFill} ${styles[color]} ${animated ? styles.animated : ''}`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
};

// Success feedback component
export interface SuccessFeedbackProps extends BaseComponentProps {
  message?: string;
  duration?: number;
  onComplete?: () => void;
  variant?: 'checkmark' | 'celebration' | 'simple';
  size?: 'small' | 'medium' | 'large';
}

export const SuccessFeedback: React.FC<SuccessFeedbackProps> = ({
  message = '操作成功！',
  duration = 2000,
  onComplete,
  variant = 'checkmark',
  size = 'medium',
  className = '',
  testId,
}) => {
  const [isVisible, setIsVisible] = React.useState(true);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => {
        onComplete?.();
      }, 300); // Wait for fade out animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onComplete]);

  const getIcon = () => {
    switch (variant) {
      case 'checkmark':
        return '✅';
      case 'celebration':
        return '🎉';
      case 'simple':
        return '✓';
      default:
        return '✅';
    }
  };

  return (
    <div
      className={`${styles.successFeedback} ${styles[size]} ${isVisible ? styles.visible : styles.hidden} ${className}`}
      data-testid={testId}
    >
      <div className={styles.successIcon}>{getIcon()}</div>
      <div className={styles.successMessage}>{message}</div>
    </div>
  );
};

// Loading button component
export interface LoadingButtonProps extends BaseComponentProps {
  isLoading: boolean;
  loadingText?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  isLoading,
  loadingText = '处理中...',
  disabled,
  variant = 'primary',
  size = 'medium',
  onClick,
  className = '',
  testId,
}) => {
  return (
    <button
      className={`${styles.loadingButton} ${styles[variant]} ${styles[size]} ${isLoading ? styles.loading : ''} ${className}`}
      disabled={disabled || isLoading}
      onClick={onClick}
      data-testid={testId}
    >
      {isLoading && (
        <LoadingSpinner
          size="small"
          variant={variant === 'primary' ? 'white' : 'primary'}
          inline
        />
      )}
      <span className={styles.buttonText}>
        {isLoading ? loadingText : children}
      </span>
    </button>
  );
};