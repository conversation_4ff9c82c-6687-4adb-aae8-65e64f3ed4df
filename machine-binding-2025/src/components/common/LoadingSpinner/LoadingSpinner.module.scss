@use 'sass:color';
@import '../../../styles/variables.scss';

.spinner {
  display: inline-block;
  border-radius: 50%;
  border-style: solid;
  animation: spin 1s linear infinite;
}

// Spinner sizes
.spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.spinner.medium {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

.spinner.large {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

.spinner.extraLarge {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

// Spinner variants
.spinner.primary {
  border-color: rgba($primary-color, 0.2);
  border-top-color: $primary-color;
}

.spinner.secondary {
  border-color: rgba($secondary-color, 0.2);
  border-top-color: $secondary-color;
}

.spinner.white {
  border-color: rgba(white, 0.2);
  border-top-color: white;
}

.spinner.dark {
  border-color: rgba($dark-color, 0.2);
  border-top-color: $dark-color;
}

// Loading container
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $spacing-md;
  padding: $spacing-lg;
}

.loadingText {
  color: $text-muted;
  font-size: $font-size-sm;
  text-align: center;
  margin: 0;
}

// Overlay loading
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(white, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: $z-index-modal;
  backdrop-filter: blur(1px);
}

// Fullscreen loading
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(white, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: $z-index-modal;
  backdrop-filter: blur(2px);
}

// Inline loading
.inline {
  display: inline-flex;
  align-items: center;
  gap: $spacing-sm;
}

// Skeleton loading
.skeleton {
  background: linear-gradient(
    90deg,
    rgba($light-color, 0.8) 25%,
    rgba($light-color, 0.4) 50%,
    rgba($light-color, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: $border-radius-sm;
}

.skeleton.text {
  height: 1em;
  width: 100%;
}

.skeleton.title {
  height: 1.5em;
  width: 60%;
  margin-bottom: $spacing-sm;
}

.skeleton.paragraph {
  height: 1em;
  width: 100%;
  margin-bottom: $spacing-xs;

  &:last-child {
    width: 80%;
    margin-bottom: 0;
  }
}

.skeleton.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton.button {
  height: 40px;
  width: 120px;
  border-radius: $border-radius-md;
}

.skeleton.card {
  height: 200px;
  width: 100%;
  border-radius: $border-radius-lg;
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Pulse animation for loading states
.pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Progress indicator styles
.progressContainer {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  &.linear {
    width: 100%;
  }

  &.circular {
    align-items: center;
    position: relative;
  }

  &.small {
    font-size: 0.75rem;
  }

  &.medium {
    font-size: 0.875rem;
  }

  &.large {
    font-size: 1rem;
  }
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-xs;
}

.progressTrack {
  background-color: rgba($light-color, 0.3);
  border-radius: $border-radius-sm;
  overflow: hidden;
  height: 8px;

  .small & {
    height: 4px;
  }

  .large & {
    height: 12px;
  }

  // Circular progress track
  .circular & {
    fill: none;
    stroke: rgba($light-color, 0.3);
  }
}

.progressFill {
  height: 100%;
  border-radius: $border-radius-sm;
  transition: width 0.3s ease;

  &.animated {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // Colors
  &.primary {
    background-color: $primary-color;
  }

  &.secondary {
    background-color: $secondary-color;
  }

  &.success {
    background-color: $success-color;
  }

  &.warning {
    background-color: $warning-color;
  }

  &.error {
    background-color: $error-color;
  }

  // Circular progress fill
  .circular & {
    fill: none;
    stroke-linecap: round;
    transform-origin: center;
    transition: stroke-dashoffset 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    &.primary {
      stroke: $primary-color;
    }

    &.secondary {
      stroke: $secondary-color;
    }

    &.success {
      stroke: $success-color;
    }

    &.warning {
      stroke: $warning-color;
    }

    &.error {
      stroke: $error-color;
    }
  }
}

.progressCircle {
  transform: rotate(-90deg);
}

.progressText {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.percentage {
  font-weight: 600;
  color: $text-primary;
}

.label {
  color: $text-muted;
  font-size: 0.8em;
  margin-top: 2px;
}

// Success feedback styles
.successFeedback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  padding: $spacing-lg;
  background: rgba($success-color, 0.1);
  border: 1px solid rgba($success-color, 0.3);
  border-radius: $border-radius-lg;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    transform: scale(1);
  }

  &.hidden {
    opacity: 0;
    transform: scale(0.9);
  }

  &.small {
    padding: $spacing-sm;
    gap: $spacing-xs;
    
    .successIcon {
      font-size: 1.5rem;
    }
    
    .successMessage {
      font-size: 0.875rem;
    }
  }

  &.medium {
    .successIcon {
      font-size: 2rem;
    }
    
    .successMessage {
      font-size: 1rem;
    }
  }

  &.large {
    padding: $spacing-xl;
    
    .successIcon {
      font-size: 2.5rem;
    }
    
    .successMessage {
      font-size: 1.125rem;
    }
  }
}

.successIcon {
  animation: successBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.successMessage {
  color: $success-color;
  font-weight: 500;
  text-align: center;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

// Loading button styles
.loadingButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $border-radius-md;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  &.loading {
    pointer-events: none;
  }

  // Sizes
  &.small {
    padding: $spacing-xs $spacing-sm;
    font-size: 0.875rem;
    gap: $spacing-xs;
  }

  &.medium {
    padding: $spacing-sm $spacing-md;
    font-size: 1rem;
  }

  &.large {
    padding: $spacing-md $spacing-lg;
    font-size: 1.125rem;
  }

  // Variants
  &.primary {
    background-color: $primary-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust($primary-color, $lightness: -10%);
    }
  }

  &.secondary {
    background-color: $secondary-color;
    color: white;

    &:hover:not(:disabled) {
      background-color: color.adjust($secondary-color, $lightness: -10%);
    }
  }

  &.outline {
    background-color: transparent;
    color: $primary-color;
    border: 1px solid $primary-color;

    &:hover:not(:disabled) {
      background-color: rgba($primary-color, 0.1);
    }
  }
}

.buttonText {
  transition: opacity 0.2s ease;
  
  .loading & {
    opacity: 0.8;
  }
}