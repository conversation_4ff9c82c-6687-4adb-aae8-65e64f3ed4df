import React from 'react';
import { useDeviceInfo, useLayoutConfig } from '../../../utils/device';
import styles from './ResponsiveTest.module.scss';

const ResponsiveTest: React.FC = () => {
  const deviceInfo = useDeviceInfo();
  const layoutConfig = useLayoutConfig();

  return (
    <div className={styles.responsiveTest}>
      <div className={styles.deviceIndicator}>
        <span className={styles.deviceType}>
          {deviceInfo.isPhone ? '📱 Phone' : 
           deviceInfo.isTablet ? '📱 Tablet' : '💻 Desktop'}
        </span>
        <span className={styles.orientation}>
          {deviceInfo.isPortrait ? '📱 Portrait' : '📺 Landscape'}
        </span>
      </div>

      <div className={styles.adaptiveGrid}>
        {Array.from({ length: 8 }, (_, i) => (
          <div key={i} className={styles.gridItem}>
            Item {i + 1}
          </div>
        ))}
      </div>

      <div className={styles.orientationDemo}>
        <div className="portrait-only">
          <div className={styles.demoCard}>Portrait Only Content</div>
        </div>
        <div className="landscape-only">
          <div className={styles.demoCard}>Landscape Only Content</div>
        </div>
      </div>

      <div className={styles.deviceDemo}>
        <div className="phone-only">
          <div className={styles.demoCard}>Phone Only</div>
        </div>
        <div className="tablet-small-only">
          <div className={styles.demoCard}>Small Tablet Only</div>
        </div>
        <div className="tablet-large-up-only">
          <div className={styles.demoCard}>Large Tablet+ Only</div>
        </div>
      </div>

      <div className={styles.infoPanel}>
        <h3>Current Layout Config</h3>
        <p>Grid Columns: {layoutConfig.gridColumns}</p>
        <p>Touch Target: {layoutConfig.touchTargetSize}px</p>
        <p>Font Size: {layoutConfig.baseFontSize}px</p>
        <p>Max Width: {layoutConfig.maxContentWidth}</p>
      </div>
    </div>
  );
};

export default ResponsiveTest;