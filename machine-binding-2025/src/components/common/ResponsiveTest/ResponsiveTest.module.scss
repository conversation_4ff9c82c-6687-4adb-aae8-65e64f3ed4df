@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.responsiveTest {
  @include adaptive-content-area;
  padding: $spacing-lg;
  
  @include mobile-portrait {
    padding: $spacing-md;
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-md;
  }
}

.deviceIndicator {
  position: fixed;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  z-index: $z-index-tooltip;
}

.deviceType,
.orientation {
  white-space: nowrap;
}

.adaptiveGrid {
  @include orientation-adaptive(1, 2, 2, 3);
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
  
  @include mobile-portrait {
    gap: $spacing-sm;
  }
  
  @include mobile-landscape {
    gap: $spacing-xs;
  }
}

.gridItem {
  background: $primary-color;
  color: white;
  padding: $spacing-md;
  border-radius: $border-radius-md;
  text-align: center;
  font-weight: $font-weight-medium;
  
  @include touch-target;
  
  @include mobile-portrait {
    padding: $spacing-lg;
    min-height: $touch-target-comfortable;
  }
  
  @include mobile-landscape {
    padding: $spacing-sm;
    min-height: $touch-target-min;
    font-size: $font-size-sm;
  }
  
  @include tablet-portrait {
    padding: $spacing-lg;
  }
  
  @include tablet-landscape {
    padding: $spacing-md;
  }
}

.orientationDemo,
.deviceDemo {
  margin-bottom: $spacing-lg;
  display: flex;
  gap: $spacing-md;
  flex-wrap: wrap;
  
  @include mobile-portrait {
    flex-direction: column;
    gap: $spacing-sm;
  }
}

.demoCard {
  background: $background-secondary;
  border: 2px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  text-align: center;
  font-weight: $font-weight-medium;
  
  @include mobile-portrait {
    padding: $spacing-md;
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }
}

.infoPanel {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-sm;
  
  h3 {
    margin-bottom: $spacing-md;
    color: $primary-color;
  }
  
  p {
    margin-bottom: $spacing-xs;
    font-size: $font-size-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  @include mobile-portrait {
    padding: $spacing-md;
    
    h3 {
      font-size: $font-size-md;
    }
    
    p {
      font-size: $font-size-xs;
    }
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-md;
    
    h3 {
      font-size: $font-size-sm;
      margin-bottom: $spacing-sm;
    }
    
    p {
      font-size: $font-size-xs;
      margin-bottom: 2px;
    }
  }
}