@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.responsiveTest {
  @include mixins.adaptive-content-area;
  padding: vars.$spacing-lg;
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-md;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-md;
  }
}

.deviceIndicator {
  position: fixed;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
  z-index: vars.$z-index-tooltip;
}

.deviceType,
.orientation {
  white-space: nowrap;
}

.adaptiveGrid {
  @include mixins.orientation-adaptive(1, 2, 2, 3);
  gap: vars.$spacing-md;
  margin-bottom: vars.$spacing-lg;
  
  @include mixins.mobile-portrait {
    gap: vars.$spacing-sm;
  }
  
  @include mixins.mobile-landscape {
    gap: vars.$spacing-xs;
  }
}

.gridItem {
  background: vars.$primary-color;
  color: white;
  padding: vars.$spacing-md;
  border-radius: vars.$border-radius-md;
  text-align: center;
  font-weight: vars.$font-weight-medium;
  
  @include mixins.touch-target;
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-lg;
    min-height: vars.$touch-target-comfortable;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm;
    min-height: vars.$touch-target-min;
    font-size: vars.$font-size-sm;
  }
  
  @include mixins.tablet-portrait {
    padding: vars.$spacing-lg;
  }
  
  @include mixins.tablet-landscape {
    padding: vars.$spacing-md;
  }
}

.orientationDemo,
.deviceDemo {
  margin-bottom: vars.$spacing-lg;
  display: flex;
  gap: vars.$spacing-md;
  flex-wrap: wrap;
  
  @include mixins.mobile-portrait {
    flex-direction: column;
    gap: vars.$spacing-sm;
  }
}

.demoCard {
  background: vars.$background-secondary;
  border: 2px solid vars.$border-color;
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-lg;
  text-align: center;
  font-weight: vars.$font-weight-medium;
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-md;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-md;
    font-size: vars.$font-size-sm;
  }
}

.infoPanel {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
  
  h3 {
    margin-bottom: vars.$spacing-md;
    color: vars.$primary-color;
  }
  
  p {
    margin-bottom: vars.$spacing-xs;
    font-size: vars.$font-size-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-md;
    
    h3 {
      font-size: vars.$font-size-md;
    }
    
    p {
      font-size: vars.$font-size-xs;
    }
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-md;
    
    h3 {
      font-size: vars.$font-size-sm;
      margin-bottom: vars.$spacing-sm;
    }
    
    p {
      font-size: vars.$font-size-xs;
      margin-bottom: 2px;
    }
  }
}