import React, { useState, useEffect, useRef, useCallback } from 'react';
import { CarouselItem, BaseComponentProps } from '../../../types';
import { LoadingSpinner } from '../../common';
import styles from './ImageCarousel.module.scss';

export interface ImageCarouselProps extends BaseComponentProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  showPlayPause?: boolean;
  loading?: boolean;
  error?: string;
  onSlideClick?: (item: CarouselItem, index: number) => void;
  onSlideChange?: (index: number) => void;
}

export const ImageCarousel: React.FC<ImageCarouselProps> = ({
  items = [],
  autoPlay = true,
  autoPlayInterval = 5000,
  showDots = true,
  showArrows = true,
  showPlayPause = true,
  loading = false,
  error,
  onSlideClick,
  onSlideChange,
  className = '',
  testId,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();
  const carouselRef = useRef<HTMLDivElement>(null);

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && items.length > 1) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % items.length);
      }, autoPlayInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying, items.length, autoPlayInterval]);

  // Notify parent of slide changes
  useEffect(() => {
    onSlideChange?.(currentIndex);
  }, [currentIndex, onSlideChange]);

  // Navigation functions
  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  const goToPrevious = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + items.length) % items.length);
  }, [items.length]);

  const goToNext = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % items.length);
  }, [items.length]);

  const togglePlayPause = useCallback(() => {
    setIsPlaying(prev => !prev);
  }, []);

  // Touch gesture handling
  const handleTouchStart = (event: React.TouchEvent) => {
    setTouchStart(event.targetTouches[0].clientX);
  };

  const handleTouchMove = (event: React.TouchEvent) => {
    setTouchEnd(event.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && items.length > 1) {
      goToNext();
    }
    if (isRightSwipe && items.length > 1) {
      goToPrevious();
    }

    setTouchStart(0);
    setTouchEnd(0);
  };

  // Keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        goToPrevious();
        break;
      case 'ArrowRight':
        event.preventDefault();
        goToNext();
        break;
      case ' ':
        event.preventDefault();
        togglePlayPause();
        break;
    }
  };

  // Handle slide click
  const handleSlideClick = (item: CarouselItem, index: number) => {
    onSlideClick?.(item, index);
  };

  // Loading state
  if (loading) {
    return (
      <div className={`${styles.carousel} ${className}`} data-testid={testId}>
        <div className={styles.loading}>
          <LoadingSpinner size="large" text="加载图片中..." />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`${styles.carousel} ${className}`} data-testid={testId}>
        <div className={styles.error}>
          <div style={{ fontSize: '2rem', marginBottom: '16px' }}>⚠️</div>
          <div>图片加载失败</div>
          <div style={{ fontSize: '14px', marginTop: '8px' }}>{error}</div>
        </div>
      </div>
    );
  }

  // Empty state
  if (items.length === 0) {
    return (
      <div className={`${styles.carousel} ${className}`} data-testid={testId}>
        <div className={styles.error}>
          <div style={{ fontSize: '2rem', marginBottom: '16px' }}>📷</div>
          <div>暂无图片</div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={carouselRef}
      className={`${styles.carousel} ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="region"
      aria-label="图片轮播"
      data-testid={testId}
    >
      <div
        className={styles.carouselContainer}
        style={{
          transform: `translateX(-${currentIndex * 100}%)`,
        }}
      >
        {items.map((item, index) => (
          <div
            key={item.id || index}
            className={styles.carouselSlide}
            onClick={() => handleSlideClick(item, index)}
            role="button"
            tabIndex={0}
            aria-label={`幻灯片 ${index + 1}: ${item.title || '图片'}`}
          >
            <img
              src={item.image}
              alt={item.title || `幻灯片 ${index + 1}`}
              className={styles.slideImage}
              loading={index === 0 ? 'eager' : 'lazy'}
            />
            
            {(item.title || item.description) && (
              <div className={styles.slideContent}>
                {item.title && (
                  <h3 className={styles.slideTitle}>{item.title}</h3>
                )}
                {item.description && (
                  <p className={styles.slideDescription}>{item.description}</p>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      {showArrows && items.length > 1 && (
        <>
          <button
            className={`${styles.navButton} ${styles.prev}`}
            onClick={goToPrevious}
            aria-label="上一张"
            data-testid={`${testId}-prev`}
          >
            ←
          </button>
          <button
            className={`${styles.navButton} ${styles.next}`}
            onClick={goToNext}
            aria-label="下一张"
            data-testid={`${testId}-next`}
          >
            →
          </button>
        </>
      )}

      {/* Play/Pause Button */}
      {showPlayPause && items.length > 1 && (
        <button
          className={styles.playPauseButton}
          onClick={togglePlayPause}
          aria-label={isPlaying ? '暂停自动播放' : '开始自动播放'}
          data-testid={`${testId}-play-pause`}
        >
          {isPlaying ? '⏸️' : '▶️'}
        </button>
      )}

      {/* Dots Navigation */}
      {showDots && items.length > 1 && (
        <div className={styles.dots}>
          {items.map((_, index) => (
            <button
              key={index}
              className={`${styles.dot} ${index === currentIndex ? styles.active : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`转到第 ${index + 1} 张`}
              data-testid={`${testId}-dot-${index}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};