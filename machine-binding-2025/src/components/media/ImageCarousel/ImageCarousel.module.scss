@import '../../../styles/variables.scss';

.carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  background: $background-primary;
}

.carouselContainer {
  display: flex;
  transition: transform 0.3s ease-in-out;
  height: 200px;
}

.carouselSlide {
  flex: 0 0 100%;
  position: relative;
  overflow: hidden;
}

.slideImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.slideContent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: $spacing-lg;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.slideTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  margin: 0 0 $spacing-xs 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.slideDescription {
  font-size: $font-size-sm;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// Show content on hover/focus
.carouselSlide:hover .slideContent,
.carouselSlide:focus-within .slideContent {
  transform: translateY(0);
}

.carouselSlide:hover .slideImage {
  transform: scale(1.05);
}

// Navigation dots
.dots {
  display: flex;
  justify-content: center;
  gap: $spacing-sm;
  padding: $spacing-md;
  position: absolute;
  bottom: $spacing-md;
  left: 50%;
  transform: translateX(-50%);
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(white, 0.5);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: white;
    transform: scale(1.2);
  }

  &:hover {
    background: rgba(white, 0.8);
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }
}

// Navigation arrows
.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(white, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  color: $text-primary;
  transition: all 0.3s ease;
  opacity: 0;
  z-index: 2;

  &:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  &.prev {
    left: $spacing-md;
  }

  &.next {
    right: $spacing-md;
  }
}

.carousel:hover .navButton {
  opacity: 1;
}

// Auto-play indicator
.playPauseButton {
  position: absolute;
  top: $spacing-md;
  right: $spacing-md;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
  z-index: 2;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }
}

// Loading state
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: $background-secondary;
  color: $text-muted;
}

// Error state
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: $background-secondary;
  color: $text-muted;
  text-align: center;
  padding: $spacing-lg;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .carouselContainer {
    height: 160px;
  }

  .slideContent {
    padding: $spacing-md;
  }

  .slideTitle {
    font-size: $font-size-md;
  }

  .slideDescription {
    font-size: $font-size-xs;
  }

  .navButton {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .playPauseButton {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .dots {
    bottom: $spacing-sm;
  }

  .dot {
    width: 10px;
    height: 10px;
  }
}

// Tablet optimizations
@media (min-width: $tablet) {
  .carouselContainer {
    height: 240px;
  }

  .slideContent {
    padding: $spacing-xl;
  }

  .slideTitle {
    font-size: $font-size-xl;
  }
}

// Touch gestures support
.carousel {
  touch-action: pan-y;
  user-select: none;
}

// Smooth scrolling for touch devices
@media (hover: none) and (pointer: coarse) {
  .carouselContainer {
    scroll-behavior: smooth;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
  }

  .carouselSlide {
    scroll-snap-align: start;
  }

  .navButton {
    display: none;
  }
}

// Animation for slide transitions
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.carouselSlide {
  animation: slideIn 0.5s ease-out;
}