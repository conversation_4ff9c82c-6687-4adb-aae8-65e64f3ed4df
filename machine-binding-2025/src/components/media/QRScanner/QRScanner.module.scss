@import '../../../styles/variables.scss';

.qrScanner {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: $dark-color;
  border-radius: $border-radius-lg;
  overflow: hidden;
}

.videoContainer {
  position: relative;
  width: 100%;
  height: 300px;
  background: $dark-color;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.scanFrame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid $success-color;
  border-radius: $border-radius-md;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

.scanFrame::before,
.scanFrame::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid $success-color;
}

.scanFrame::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scanFrame::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.scanLine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, $success-color, transparent);
  animation: scanAnimation 2s linear infinite;
}

.controls {
  position: absolute;
  bottom: $spacing-lg;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: $spacing-md;
  z-index: 2;
}

.controlButton {
  background: rgba(white, 0.9);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: white;
    transform: scale(1.1);
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.status {
  position: absolute;
  top: $spacing-lg;
  left: $spacing-lg;
  right: $spacing-lg;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  text-align: center;
  backdrop-filter: blur(10px);
}

.status.success {
  background: rgba($success-color, 0.9);
}

.status.error {
  background: rgba($danger-color, 0.9);
}

.status.warning {
  background: rgba($warning-color, 0.9);
  color: $text-primary;
}

// Loading state
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: white;
  gap: $spacing-md;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(white, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

// Error state
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: white;
  text-align: center;
  padding: $spacing-lg;
  gap: $spacing-md;
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: $spacing-md;
}

.errorMessage {
  font-size: $font-size-md;
  margin-bottom: $spacing-lg;
}

// Fallback input
.fallbackInput {
  padding: $spacing-lg;
  background: white;
  border-top: 1px solid #eee;
}

.fallbackTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  margin: 0 0 $spacing-md 0;
  color: $text-primary;
  text-align: center;
}

.inputGroup {
  display: flex;
  gap: $spacing-sm;
}

.qrInput {
  flex: 1;
  padding: $spacing-sm $spacing-md;
  border: 2px solid #ddd;
  border-radius: $border-radius-md;
  font-size: $font-size-md;

  &:focus {
    outline: none;
    border-color: $primary-color;
  }
}

.submitButton {
  padding: $spacing-sm $spacing-lg;
  background: $primary-color;
  color: white;
  border: none;
  border-radius: $border-radius-md;
  cursor: pointer;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  transition: background-color 0.3s ease;

  &:hover {
    background: darken($primary-color, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .qrScanner {
    max-width: none;
    border-radius: 0;
  }

  .videoContainer {
    height: 250px;
  }

  .scanFrame {
    width: 150px;
    height: 150px;
  }

  .controls {
    bottom: $spacing-md;
  }

  .controlButton {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }

  .status {
    top: $spacing-md;
    left: $spacing-md;
    right: $spacing-md;
  }

  .fallbackInput {
    padding: $spacing-md;
  }
}

// Animations
@keyframes scanAnimation {
  0% {
    top: 0;
  }
  50% {
    top: calc(100% - 2px);
  }
  100% {
    top: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Success animation
@keyframes successPulse {
  0%, 100% {
    border-color: $success-color;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  }
  50% {
    border-color: lighten($success-color, 20%);
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3), 0 0 20px $success-color;
  }
}

.scanFrame.success {
  animation: successPulse 0.5s ease-in-out;
}