import React, { useRef, useEffect, useState, useCallback } from 'react';
import QrScanner from 'qr-scanner';
import { BaseComponentProps } from '../../../types';
import { Button, Input } from '../../common';
import styles from './QRScanner.module.scss';

export interface QRScannerProps extends BaseComponentProps {
  onScan?: (result: string) => void;
  onError?: (error: string) => void;
  showFallbackInput?: boolean;
  autoStart?: boolean;
  facingMode?: 'user' | 'environment';
  maxScanAttempts?: number;
}

export const QRScanner: React.FC<QRScannerProps> = ({
  onScan,
  onError,
  showFallbackInput = true,
  autoStart = true,
  facingMode = 'environment',
  maxScanAttempts = 10,
  className = '',
  testId,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const qrScannerRef = useRef<QrScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [hasCamera, setHasCamera] = useState(true);
  const [status, setStatus] = useState<{
    type: 'info' | 'success' | 'error' | 'warning';
    message: string;
  } | null>(null);
  const [fallbackValue, setFallbackValue] = useState('');
  const [scanAttempts, setScanAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize QR scanner
  const initializeScanner = useCallback(async () => {
    if (!videoRef.current) return;

    try {
      setIsLoading(true);
      setStatus({ type: 'info', message: '正在启动摄像头...' });

      // Check if camera is available
      const hasCamera = await QrScanner.hasCamera();
      setHasCamera(hasCamera);

      if (!hasCamera) {
        setStatus({ type: 'warning', message: '未检测到摄像头，请使用手动输入' });
        setIsLoading(false);
        return;
      }

      // Create QR scanner instance
      qrScannerRef.current = new QrScanner(
        videoRef.current,
        (result) => {
          setStatus({ type: 'success', message: '扫描成功！' });
          setScanAttempts(0);
          onScan?.(result.data);
          
          // Show success animation
          setTimeout(() => {
            setStatus(null);
          }, 2000);
        },
        {
          onDecodeError: (error) => {
            setScanAttempts(prev => prev + 1);
            
            if (scanAttempts >= maxScanAttempts) {
              setStatus({ 
                type: 'error', 
                message: '扫描失败次数过多，请检查二维码是否清晰' 
              });
              onError?.('扫描失败次数过多');
            } else {
              setStatus({ 
                type: 'info', 
                message: '正在扫描二维码，请对准扫描框...' 
              });
            }
          },
          preferredCamera: facingMode,
          highlightScanRegion: false,
          highlightCodeOutline: false,
        }
      );

      if (autoStart) {
        await startScanning();
      }

      setStatus({ type: 'info', message: '请将二维码对准扫描框' });
    } catch (error) {
      console.error('Failed to initialize QR scanner:', error);
      setStatus({ 
        type: 'error', 
        message: '摄像头启动失败，请检查权限设置' 
      });
      onError?.('摄像头启动失败');
      setHasCamera(false);
    } finally {
      setIsLoading(false);
    }
  }, [autoStart, facingMode, maxScanAttempts, onScan, onError, scanAttempts]);

  // Start scanning
  const startScanning = useCallback(async () => {
    if (!qrScannerRef.current) return;

    try {
      await qrScannerRef.current.start();
      setIsScanning(true);
      setScanAttempts(0);
      setStatus({ type: 'info', message: '请将二维码对准扫描框' });
    } catch (error) {
      console.error('Failed to start scanning:', error);
      setStatus({ type: 'error', message: '启动扫描失败' });
      onError?.('启动扫描失败');
    }
  }, [onError]);

  // Stop scanning
  const stopScanning = useCallback(() => {
    if (qrScannerRef.current) {
      qrScannerRef.current.stop();
      setIsScanning(false);
      setStatus({ type: 'info', message: '扫描已暂停' });
    }
  }, []);

  // Toggle flashlight (if available)
  const toggleFlash = useCallback(async () => {
    if (!qrScannerRef.current) return;

    try {
      const hasFlash = await qrScannerRef.current.hasFlash();
      if (hasFlash) {
        await qrScannerRef.current.toggleFlash();
      } else {
        setStatus({ type: 'warning', message: '设备不支持闪光灯' });
      }
    } catch (error) {
      console.error('Failed to toggle flash:', error);
      setStatus({ type: 'error', message: '闪光灯控制失败' });
    }
  }, []);

  // Switch camera (if multiple cameras available)
  const switchCamera = useCallback(async () => {
    if (!qrScannerRef.current) return;

    try {
      const cameras = await QrScanner.listCameras();
      if (cameras.length > 1) {
        // Switch to next camera
        const currentCamera = await qrScannerRef.current.getCamera();
        const currentIndex = cameras.findIndex(cam => cam.id === currentCamera?.id);
        const nextIndex = (currentIndex + 1) % cameras.length;
        await qrScannerRef.current.setCamera(cameras[nextIndex].id);
        setStatus({ type: 'info', message: '已切换摄像头' });
      } else {
        setStatus({ type: 'warning', message: '只有一个摄像头可用' });
      }
    } catch (error) {
      console.error('Failed to switch camera:', error);
      setStatus({ type: 'error', message: '切换摄像头失败' });
    }
  }, []);

  // Handle fallback input submission
  const handleFallbackSubmit = () => {
    if (fallbackValue.trim()) {
      onScan?.(fallbackValue.trim());
      setFallbackValue('');
    }
  };

  // Initialize scanner on mount
  useEffect(() => {
    initializeScanner();

    return () => {
      if (qrScannerRef.current) {
        qrScannerRef.current.destroy();
      }
    };
  }, [initializeScanner]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case ' ':
          event.preventDefault();
          if (isScanning) {
            stopScanning();
          } else {
            startScanning();
          }
          break;
        case 'f':
        case 'F':
          event.preventDefault();
          toggleFlash();
          break;
        case 'c':
        case 'C':
          event.preventDefault();
          switchCamera();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isScanning, startScanning, stopScanning, toggleFlash, switchCamera]);

  return (
    <div className={`${styles.qrScanner} ${className}`} data-testid={testId}>
      <div className={styles.videoContainer}>
        {isLoading ? (
          <div className={styles.loading}>
            <div className={styles.loadingSpinner} />
            <div>正在启动摄像头...</div>
          </div>
        ) : !hasCamera ? (
          <div className={styles.error}>
            <div className={styles.errorIcon}>📷</div>
            <div className={styles.errorMessage}>
              未检测到摄像头或权限被拒绝
            </div>
            <div style={{ fontSize: '14px', opacity: 0.8 }}>
              请使用下方的手动输入功能
            </div>
          </div>
        ) : (
          <>
            <video
              ref={videoRef}
              className={styles.video}
              playsInline
              muted
            />
            
            <div className={styles.overlay}>
              <div className={`${styles.scanFrame} ${status?.type === 'success' ? styles.success : ''}`}>
                <div className={styles.scanLine} />
              </div>
            </div>

            <div className={styles.controls}>
              <button
                className={styles.controlButton}
                onClick={isScanning ? stopScanning : startScanning}
                aria-label={isScanning ? '暂停扫描' : '开始扫描'}
                data-testid={`${testId}-toggle`}
              >
                {isScanning ? '⏸️' : '▶️'}
              </button>
              
              <button
                className={styles.controlButton}
                onClick={toggleFlash}
                aria-label="切换闪光灯"
                data-testid={`${testId}-flash`}
              >
                💡
              </button>
              
              <button
                className={styles.controlButton}
                onClick={switchCamera}
                aria-label="切换摄像头"
                data-testid={`${testId}-switch`}
              >
                🔄
              </button>
            </div>
          </>
        )}

        {status && (
          <div className={`${styles.status} ${styles[status.type]}`}>
            {status.message}
          </div>
        )}
      </div>

      {/* Fallback manual input */}
      {showFallbackInput && (
        <div className={styles.fallbackInput}>
          <h4 className={styles.fallbackTitle}>手动输入二维码</h4>
          <div className={styles.inputGroup}>
            <Input
              value={fallbackValue}
              onChange={(e) => setFallbackValue(e.target.value)}
              placeholder="请输入或粘贴二维码内容"
              className={styles.qrInput}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleFallbackSubmit();
                }
              }}
            />
            <Button
              onClick={handleFallbackSubmit}
              disabled={!fallbackValue.trim()}
              className={styles.submitButton}
            >
              确认
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};