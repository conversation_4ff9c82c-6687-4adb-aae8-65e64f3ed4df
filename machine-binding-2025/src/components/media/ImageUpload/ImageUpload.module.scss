@import '../../../styles/variables.scss';

.imageUpload {
  width: 100%;
}

.uploadArea {
  border: 2px dashed #ddd;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  text-align: center;
  background: $background-secondary;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: $primary-color;
    background: rgba($primary-color, 0.02);
  }

  &.dragOver {
    border-color: $primary-color;
    background: rgba($primary-color, 0.05);
    transform: scale(1.02);
  }

  &.error {
    border-color: $danger-color;
    background: rgba($danger-color, 0.02);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.uploadIcon {
  font-size: 3rem;
  color: $text-muted;
  margin-bottom: $spacing-md;
}

.uploadText {
  font-size: $font-size-md;
  color: $text-primary;
  margin: 0 0 $spacing-xs 0;
  font-weight: $font-weight-medium;
}

.uploadHint {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0;
}

.hiddenInput {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 1px;
  height: 1px;
}

.imagePreview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: $spacing-md;
  margin-top: $spacing-lg;
}

.previewItem {
  position: relative;
  aspect-ratio: 1;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: $background-secondary;
  box-shadow: $shadow-sm;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.previewItem:hover .previewImage {
  transform: scale(1.05);
}

.removeButton {
  position: absolute;
  top: $spacing-xs;
  right: $spacing-xs;
  background: rgba($danger-color, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: $danger-color;
    transform: scale(1.1);
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }
}

.imageInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: $spacing-xs;
  font-size: $font-size-xs;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.previewItem:hover .imageInfo {
  opacity: 1;
}

.uploadProgress {
  margin-top: $spacing-md;
  background: $background-secondary;
  border-radius: $border-radius-md;
  padding: $spacing-md;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: $spacing-sm;
}

.progressFill {
  height: 100%;
  background: $primary-color;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progressText {
  font-size: $font-size-sm;
  color: $text-muted;
  text-align: center;
  margin: 0;
}

.errorMessage {
  margin-top: $spacing-md;
  padding: $spacing-md;
  background: rgba($danger-color, 0.1);
  border: 1px solid rgba($danger-color, 0.3);
  border-radius: $border-radius-md;
  color: $danger-color;
  font-size: $font-size-sm;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.uploadStats {
  margin-top: $spacing-md;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: $font-size-sm;
  color: $text-muted;
}

.maxFiles {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.fileCount {
  font-weight: $font-weight-medium;
  color: $text-primary;
}

.fileCount.atLimit {
  color: $warning-color;
}

.fileCount.overLimit {
  color: $danger-color;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .uploadArea {
    padding: $spacing-lg;
    min-height: 100px;
  }

  .uploadIcon {
    font-size: 2rem;
    margin-bottom: $spacing-sm;
  }

  .uploadText {
    font-size: $font-size-sm;
  }

  .uploadHint {
    font-size: $font-size-xs;
  }

  .imagePreview {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: $spacing-sm;
  }

  .removeButton {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }
}

// Tablet optimizations
@media (min-width: $tablet) {
  .imagePreview {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

// Loading state
.imageUpload.loading {
  .uploadArea {
    pointer-events: none;
    opacity: 0.7;
  }
}

// Animation for new images
@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.previewItem {
  animation: slideIn 0.3s ease-out;
}

// Drag and drop visual feedback
.uploadArea.dragOver::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($primary-color, 0.1);
  border-radius: $border-radius-lg;
  pointer-events: none;
}