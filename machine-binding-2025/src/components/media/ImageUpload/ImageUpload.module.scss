@use "../../../styles/variables" as vars;

.imageUpload {
  width: 100%;
}

.uploadArea {
  border: 2px dashed #ddd;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  text-align: center;
  background: vars.$background-secondary;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: vars.$primary-color;
    background: rgba(vars.$primary-color, 0.02);
  }

  &.dragOver {
    border-color: vars.$primary-color;
    background: rgba(vars.$primary-color, 0.05);
    transform: scale(1.02);
  }

  &.error {
    border-color: vars.$danger-color;
    background: rgba(vars.$danger-color, 0.02);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.uploadIcon {
  font-size: 3rem;
  color: vars.$text-muted;
  margin-bottom: vars.$spacing-md;
}

.uploadText {
  font-size: vars.$font-size-md;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-xs 0;
  font-weight: vars.$font-weight-medium;
}

.uploadHint {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0;
}

.hiddenInput {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 1px;
  height: 1px;
}

.imagePreview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: vars.$spacing-md;
  margin-top: vars.$spacing-lg;
}

.previewItem {
  position: relative;
  aspect-ratio: 1;
  border-radius: vars.$border-radius-md;
  overflow: hidden;
  background: vars.$background-secondary;
  box-shadow: vars.$shadow-sm;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.previewItem:hover .previewImage {
  transform: scale(1.05);
}

.removeButton {
  position: absolute;
  top: vars.$spacing-xs;
  right: vars.$spacing-xs;
  background: rgba(vars.$danger-color, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: vars.$danger-color;
    transform: scale(1.1);
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }
}

.imageInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: vars.$spacing-xs;
  font-size: vars.$font-size-xs;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.previewItem:hover .imageInfo {
  opacity: 1;
}

.uploadProgress {
  margin-top: vars.$spacing-md;
  background: vars.$background-secondary;
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-md;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: vars.$spacing-sm;
}

.progressFill {
  height: 100%;
  background: vars.$primary-color;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progressText {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  text-align: center;
  margin: 0;
}

.errorMessage {
  margin-top: vars.$spacing-md;
  padding: vars.$spacing-md;
  background: rgba(vars.$danger-color, 0.1);
  border: 1px solid rgba(vars.$danger-color, 0.3);
  border-radius: vars.$border-radius-md;
  color: vars.$danger-color;
  font-size: vars.$font-size-sm;
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
}

.uploadStats {
  margin-top: vars.$spacing-md;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
}

.maxFiles {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
}

.fileCount {
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;
}

.fileCount.atLimit {
  color: vars.$warning-color;
}

.fileCount.overLimit {
  color: vars.$danger-color;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .uploadArea {
    padding: vars.$spacing-lg;
    min-height: 100px;
  }

  .uploadIcon {
    font-size: 2rem;
    margin-bottom: vars.$spacing-sm;
  }

  .uploadText {
    font-size: vars.$font-size-sm;
  }

  .uploadHint {
    font-size: vars.$font-size-xs;
  }

  .imagePreview {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: vars.$spacing-sm;
  }

  .removeButton {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) {
  .imagePreview {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

// Loading state
.imageUpload.loading {
  .uploadArea {
    pointer-events: none;
    opacity: 0.7;
  }
}

// Animation for new images
@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.previewItem {
  animation: slideIn 0.3s ease-out;
}

// Drag and drop visual feedback
.uploadArea.dragOver::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(vars.$primary-color, 0.1);
  border-radius: vars.$border-radius-lg;
  pointer-events: none;
}