import React, { useState, useRef, useCallback } from 'react';
import type { BaseComponentProps, UploadProgress } from '../../../types';
import { uploadService } from '../../../services';
import { useUploadProgress } from '../../../hooks';
import { UploadProgress as UploadProgressComponent, LoadingSpinner } from '../../common';
import styles from './ImageUpload.module.scss';

export interface ImageUploadProps extends BaseComponentProps {
  maxFiles?: number;
  maxSizeInMB?: number;
  acceptedTypes?: string[];
  onUpload?: (files: File[]) => void;
  onRemove?: (index: number) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  loading?: boolean;
  value?: File[];
  placeholder?: string;
  hint?: string;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  maxFiles = 3,
  maxSizeInMB = 5,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  onUpload,
  onRemove,
  onError,
  disabled = false,
  loading = false,
  value = [],
  placeholder = '点击或拖拽上传图片',
  hint = '支持 JPG、PNG、WebP 格式',
  className = '',
  testId,
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadProgress = useUploadProgress();

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    setError(null);
    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    const errors: string[] = [];

    // Validate each file
    for (const file of fileArray) {
      // Check file type
      if (!uploadService.validateFileType(file, acceptedTypes)) {
        errors.push(`${file.name}: 不支持的文件格式`);
        continue;
      }

      // Check file size
      if (!uploadService.validateFileSize(file, maxSizeInMB)) {
        errors.push(`${file.name}: 文件大小超过 ${maxSizeInMB}MB`);
        continue;
      }

      // Check total file count
      if (value.length + validFiles.length >= maxFiles) {
        errors.push(`最多只能上传 ${maxFiles} 张图片`);
        break;
      }

      validFiles.push(file);
    }

    // Show errors if any
    if (errors.length > 0) {
      const errorMessage = errors.join('; ');
      setError(errorMessage);
      onError?.(errorMessage);
    }

    // Process valid files
    if (validFiles.length > 0) {
      try {
        // Start upload progress tracking for each file
        const uploadPromises = validFiles.map(async (file, index) => {
          const uploadId = `${Date.now()}-${index}`;
          uploadProgress.startUpload(uploadId, file.name);

          try {
            // Simulate compression progress
            uploadProgress.updateProgress(uploadId, 20);
            const compressedFile = await uploadService.compressImage(file, 0.8);
            
            // Simulate upload progress
            uploadProgress.updateProgress(uploadId, 50);
            await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
            uploadProgress.updateProgress(uploadId, 80);
            await new Promise(resolve => setTimeout(resolve, 300));
            
            uploadProgress.completeUpload(uploadId);
            return compressedFile;
          } catch (error) {
            uploadProgress.failUpload(uploadId, '处理失败');
            throw error;
          }
        });

        const compressedFiles = await Promise.all(uploadPromises);
        onUpload?.(compressedFiles);
        
        // Clear completed uploads after a delay
        setTimeout(() => {
          uploadProgress.clearCompleted();
        }, 2000);
      } catch (error) {
        const errorMessage = '图片处理失败，请重试';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    }
  }, [acceptedTypes, maxSizeInMB, maxFiles, value.length, onUpload, onError]);

  // Handle drag and drop
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !loading) {
      setDragOver(true);
    }
  }, [disabled, loading]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    if (disabled || loading) return;

    const files = event.dataTransfer.files;
    handleFileSelect(files);
  }, [disabled, loading, handleFileSelect]);

  // Handle click to select files
  const handleClick = useCallback(() => {
    if (disabled || loading) return;
    fileInputRef.current?.click();
  }, [disabled, loading]);

  // Handle file input change
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(event.target.files);
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  }, [handleFileSelect]);

  // Handle remove image
  const handleRemove = useCallback((index: number) => {
    onRemove?.(index);
  }, [onRemove]);

  // Create preview URL for file
  const createPreviewUrl = useCallback((file: File): string => {
    return uploadService.createPreviewUrl(file);
  }, []);

  const uploadAreaClasses = [
    styles.uploadArea,
    dragOver && styles.dragOver,
    error && styles.error,
    disabled && styles.disabled,
  ]
    .filter(Boolean)
    .join(' ');

  const containerClasses = [
    styles.imageUpload,
    loading && styles.loading,
    className,
  ]
    .filter(Boolean)
    .join(' ');

  const canUploadMore = value.length < maxFiles;
  const fileCountClass = value.length >= maxFiles ? styles.overLimit : 
                        value.length >= maxFiles * 0.8 ? styles.atLimit : '';

  return (
    <div className={containerClasses} data-testid={testId}>
      {/* Upload Area */}
      {canUploadMore && (
        <div
          className={uploadAreaClasses}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
          role="button"
          tabIndex={0}
          aria-label="上传图片"
        >
          <div className={styles.uploadIcon}>📷</div>
          <div className={styles.uploadText}>{placeholder}</div>
          <div className={styles.uploadHint}>{hint}</div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleInputChange}
            className={styles.hiddenInput}
            disabled={disabled || loading}
          />
        </div>
      )}

      {/* Upload Progress */}
      {uploadProgress.uploads.length > 0 && (
        <UploadProgressComponent
          files={uploadProgress.uploads.map(upload => ({
            name: upload.name,
            progress: upload.progress,
            status: upload.status,
            error: upload.error,
          }))}
          onCancel={(fileName) => {
            const upload = uploadProgress.uploads.find(u => u.name === fileName);
            if (upload) {
              uploadProgress.removeUpload(upload.id);
            }
          }}
          onRetry={(fileName) => {
            // Implement retry logic here
            console.log('Retry upload for:', fileName);
          }}
        />
      )}

      {/* Error Message */}
      {error && (
        <div className={styles.errorMessage}>
          <span>⚠️</span>
          <span>{error}</span>
        </div>
      )}

      {/* Image Preview */}
      {value.length > 0 && (
        <div className={styles.imagePreview}>
          {value.map((file, index) => (
            <div key={index} className={styles.previewItem}>
              <img
                src={createPreviewUrl(file)}
                alt={`预览 ${index + 1}`}
                className={styles.previewImage}
              />
              
              <button
                className={styles.removeButton}
                onClick={() => handleRemove(index)}
                aria-label={`删除图片 ${index + 1}`}
                type="button"
              >
                ×
              </button>
              
              <div className={styles.imageInfo}>
                {uploadService.formatFileSize(file.size)}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Upload Stats */}
      <div className={styles.uploadStats}>
        <div className={styles.maxFiles}>
          <span>已上传:</span>
          <span className={`${styles.fileCount} ${fileCountClass}`}>
            {value.length}/{maxFiles}
          </span>
        </div>
        <div>
          最大 {maxSizeInMB}MB/张
        </div>
      </div>
    </div>
  );
};