@import '../../../styles/variables.scss';

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: $shadow-md;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  min-height: 60px;
}

.leftSection {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.backButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: $touch-target-min;
  height: $touch-target-min;
  background: rgba(white, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;

  &:hover {
    background: rgba(white, 0.2);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: 2px solid rgba(white, 0.5);
    outline-offset: 2px;
  }
}

.title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  margin: 0;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rightSection {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.qrButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: $touch-target-min;
  height: $touch-target-min;
  background: rgba(white, 0.1);
  border: none;
  border-radius: $border-radius-md;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 20px;

  &:hover {
    background: rgba(white, 0.2);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: 2px solid rgba(white, 0.5);
    outline-offset: 2px;
  }
}

.menuButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: $touch-target-min;
  height: $touch-target-min;
  background: rgba(white, 0.1);
  border: none;
  border-radius: $border-radius-md;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;

  &:hover {
    background: rgba(white, 0.2);
  }

  &:focus {
    outline: 2px solid rgba(white, 0.5);
    outline-offset: 2px;
  }
}

// Badge for notifications
.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: $danger-color;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: $font-weight-bold;
  border: 2px solid white;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .navbar {
    padding: $spacing-sm $spacing-md;
    min-height: 56px;
  }

  .title {
    font-size: $font-size-md;
  }

  .backButton,
  .qrButton,
  .menuButton {
    width: $touch-target-min;
    height: $touch-target-min;
  }
}

// Tablet optimizations
@media (min-width: $tablet) {
  .navbar {
    padding: $spacing-lg $spacing-xl;
    min-height: 64px;
  }

  .title {
    font-size: $font-size-xl;
  }
}

// Animation for navbar appearance
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.navbar {
  animation: slideDown 0.3s ease-out;
}