import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { BaseComponentProps } from '../../../types';
import styles from './NavigationBar.module.scss';

export interface NavigationBarProps extends BaseComponentProps {
  title?: string;
  showBackButton?: boolean;
  showQRButton?: boolean;
  showMenuButton?: boolean;
  onQRScan?: () => void;
  onMenuClick?: () => void;
  customBackAction?: () => void;
  rightContent?: React.ReactNode;
  badgeCount?: number;
}

export const NavigationBar: React.FC<NavigationBarProps> = ({
  title = '设备绑定系统',
  showBackButton = true,
  showQRButton = true,
  showMenuButton = false,
  onQRScan,
  onMenuClick,
  customBackAction,
  rightContent,
  badgeCount,
  className = '',
  testId,
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Determine if we should show back button based on current route
  const shouldShowBackButton = showBackButton && location.pathname !== '/home';

  const handleBackClick = () => {
    if (customBackAction) {
      customBackAction();
    } else {
      navigate(-1);
    }
  };

  const handleQRClick = () => {
    if (onQRScan) {
      onQRScan();
    } else {
      // Default QR scan behavior - navigate to device binding
      navigate('/device-binding');
    }
  };

  const handleMenuClick = () => {
    if (onMenuClick) {
      onMenuClick();
    } else {
      // Default menu behavior - navigate to profile
      navigate('/profile');
    }
  };

  return (
    <nav className={`${styles.navbar} ${className}`} data-testid={testId}>
      <div className={styles.leftSection}>
        {shouldShowBackButton && (
          <button
            className={styles.backButton}
            onClick={handleBackClick}
            aria-label="返回"
            data-testid="back-button"
          >
            ←
          </button>
        )}
        <h1 className={styles.title}>{title}</h1>
      </div>

      <div className={styles.rightSection}>
        {rightContent}
        
        {showQRButton && (
          <button
            className={styles.qrButton}
            onClick={handleQRClick}
            aria-label="扫描二维码"
            data-testid="qr-button"
          >
            📱
            {badgeCount && badgeCount > 0 && (
              <span className={styles.badge}>{badgeCount > 99 ? '99+' : badgeCount}</span>
            )}
          </button>
        )}

        {showMenuButton && (
          <button
            className={styles.menuButton}
            onClick={handleMenuClick}
            aria-label="菜单"
            data-testid="menu-button"
          >
            ☰
          </button>
        )}
      </div>
    </nav>
  );
};