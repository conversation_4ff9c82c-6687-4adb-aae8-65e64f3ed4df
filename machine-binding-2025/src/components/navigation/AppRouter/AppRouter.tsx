import React from 'react';
import { <PERSON>rowser<PERSON>outer, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../../../hooks';
import { ProtectedRoute } from '../ProtectedRoute/ProtectedRoute';
import { Login } from '../../../pages/Login';

// Lazy load pages for better performance
const Home = React.lazy(() => import('../../../pages/Home').then(m => ({ default: m.Home })));
const DeviceBinding = React.lazy(() => import('../../../pages/DeviceBinding').then(m => ({ default: m.DeviceBinding })));
const BindingRecords = React.lazy(() => import('../../../pages/BindingRecords').then(m => ({ default: m.BindingRecords })));
const BindingRecordDetail = React.lazy(() => import('../../../pages/BindingRecords').then(m => ({ default: m.BindingRecordDetail })));
const Profile = React.lazy(() => import('../../../pages/Profile').then(m => ({ default: m.Profile })));
const Admin = React.lazy(() => import('../../../pages/Admin').then(m => ({ default: m.Admin })));

// Loading component for lazy-loaded routes
const PageLoader: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    fontSize: '18px',
    color: '#666'
  }}>
    加载中...
  </div>
);

// Unauthorized page component
const UnauthorizedPage: React.FC = () => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    textAlign: 'center',
    padding: '20px'
  }}>
    <h1 style={{ color: '#dc3545', marginBottom: '16px' }}>访问被拒绝</h1>
    <p style={{ color: '#666', marginBottom: '24px' }}>您没有权限访问此页面</p>
    <button 
      onClick={() => window.history.back()}
      style={{
        padding: '12px 24px',
        backgroundColor: '#007bff',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer'
      }}
    >
      返回上一页
    </button>
  </div>
);

// Not found page component
const NotFoundPage: React.FC = () => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    textAlign: 'center',
    padding: '20px'
  }}>
    <h1 style={{ color: '#dc3545', marginBottom: '16px' }}>404 - 页面未找到</h1>
    <p style={{ color: '#666', marginBottom: '24px' }}>您访问的页面不存在</p>
    <button 
      onClick={() => window.location.href = '/home'}
      style={{
        padding: '12px 24px',
        backgroundColor: '#007bff',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer'
      }}
    >
      返回首页
    </button>
  </div>
);

export const AppRouter: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <BrowserRouter>
      <React.Suspense fallback={<PageLoader />}>
        <Routes>
          {/* Public routes */}
          <Route 
            path="/login" 
            element={
              isAuthenticated ? <Navigate to="/home" replace /> : <Login />
            } 
          />
          
          {/* Protected routes */}
          <Route 
            path="/home" 
            element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/device-binding" 
            element={
              <ProtectedRoute>
                <DeviceBinding />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/device-binding/:qrCode" 
            element={
              <ProtectedRoute>
                <DeviceBinding />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/binding-records" 
            element={
              <ProtectedRoute>
                <BindingRecords />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/binding-records/:id" 
            element={
              <ProtectedRoute>
                <BindingRecordDetail />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/profile" 
            element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } 
          />

          {/* Admin-only routes */}
          <Route 
            path="/admin" 
            element={
              <ProtectedRoute adminOnly>
                <Admin />
              </ProtectedRoute>
            } 
          />
          
          <Route 
            path="/admin/*" 
            element={
              <ProtectedRoute adminOnly>
                <Admin />
              </ProtectedRoute>
            } 
          />

          {/* Error pages */}
          <Route path="/unauthorized" element={<UnauthorizedPage />} />
          <Route path="/404" element={<NotFoundPage />} />

          {/* Default redirects */}
          <Route 
            path="/" 
            element={
              <Navigate 
                to={isAuthenticated ? "/home" : "/login"} 
                replace 
              />
            } 
          />

          {/* Catch all - redirect to 404 */}
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </React.Suspense>
    </BrowserRouter>
  );
};