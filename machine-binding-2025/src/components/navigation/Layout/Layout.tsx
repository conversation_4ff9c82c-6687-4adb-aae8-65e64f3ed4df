import React from 'react';
import { NavigationBar, NavigationBarProps } from '../NavigationBar/NavigationBar';
import { useLoading } from '../../../hooks';
import { BaseComponentProps } from '../../../types';
import styles from './Layout.module.scss';

export interface LayoutProps extends BaseComponentProps {
  // Navigation props
  navProps?: Partial<NavigationBarProps>;
  showNavigation?: boolean;
  
  // Content props
  contentPadding?: 'none' | 'small' | 'normal';
  
  // Loading props
  showGlobalLoading?: boolean;
  loadingMessage?: string;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  navProps = {},
  showNavigation = true,
  contentPadding = 'normal',
  showGlobalLoading = true,
  loadingMessage,
  className = '',
  testId,
}) => {
  const { loading } = useLoading();

  const mainClasses = [
    styles.main,
    contentPadding === 'normal' && styles.withPadding,
    contentPadding === 'small' && styles.withSmallPadding,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={`${styles.layout} ${className}`} data-testid={testId}>
      {showNavigation && <NavigationBar {...navProps} />}
      
      <div className={styles.content}>
        <main className={mainClasses}>
          {children}
        </main>
      </div>

      {/* Global loading overlay */}
      {showGlobalLoading && loading.isLoading && (
        <div className={styles.loadingOverlay}>
          <div className={styles.loadingContent}>
            <div className={styles.spinner} />
            <div className={styles.loadingText}>
              {loadingMessage || loading.message || '加载中...'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};