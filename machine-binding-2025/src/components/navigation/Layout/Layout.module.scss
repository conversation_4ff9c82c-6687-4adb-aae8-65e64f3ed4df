@import '../../../styles/variables.scss';
@import '../../../styles/mixins.scss';

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $background-gray;
  
  // Safe area support for devices with notches
  padding-top: env(safe-area-inset-top);
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.main {
  flex: 1;
  background-color: $background-gray;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

// Content padding variants with adaptive behavior
.main.withPadding {
  @include adaptive-content-area;
  
  // Additional responsive padding
  @include mobile-portrait {
    padding: $spacing-md;
  }
  
  @include mobile-landscape {
    padding: $spacing-sm $spacing-lg;
  }
  
  @include tablet-portrait {
    padding: $spacing-lg $spacing-xl;
    max-width: 600px;
    margin: 0 auto;
  }
  
  @include tablet-landscape {
    padding: $spacing-lg $spacing-xxl;
    max-width: 900px;
    margin: 0 auto;
  }
  
  @include desktop-up {
    padding: $spacing-xl;
    max-width: 1200px;
    margin: 0 auto;
  }
}

.main.withSmallPadding {
  @include mobile-only {
    padding: $spacing-sm;
  }
  
  @include tablet-up {
    padding: $spacing-md $spacing-lg;
    max-width: 800px;
    margin: 0 auto;
  }
}

// Orientation-specific layouts
@include mobile-landscape {
  .layout {
    // Adjust for landscape mobile navigation
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

// Device-specific optimizations
@include phone-only {
  .main {
    // Ensure content doesn't get too cramped on small phones
    min-width: 280px;
  }
}

@include tablet-large {
  .main.withPadding {
    // More generous padding on large tablets
    padding: $spacing-xxl;
    max-width: 1000px;
  }
}

// Loading overlay
.loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(white, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: $z-index-modal;
  backdrop-filter: blur(2px);
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-md;
  padding: $spacing-xl;
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid $light-color;
  border-top: 4px solid $primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loadingText {
  color: $text-primary;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}