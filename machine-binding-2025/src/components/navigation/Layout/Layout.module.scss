@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: vars.$background-gray;
  
  // Safe area support for devices with notches
  padding-top: env(safe-area-inset-top);
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.main {
  flex: 1;
  background-color: vars.$background-gray;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

// Content padding variants with adaptive behavior
.main.withPadding {
  @include mixins.adaptive-content-area;
  
  // Additional responsive padding
  @include mixins.mobile-portrait {
    padding: vars.$spacing-md;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-lg;
  }
  
  @include mixins.tablet-portrait {
    padding: vars.$spacing-lg vars.$spacing-xl;
    max-width: 600px;
    margin: 0 auto;
  }
  
  @include mixins.tablet-landscape {
    padding: vars.$spacing-lg vars.$spacing-xxl;
    max-width: 900px;
    margin: 0 auto;
  }
  
  @include mixins.desktop-up {
    padding: vars.$spacing-xl;
    max-width: 1200px;
    margin: 0 auto;
  }
}

.main.withSmallPadding {
  @include mixins.mobile-only {
    padding: vars.$spacing-sm;
  }
  
  @include mixins.tablet-up {
    padding: vars.$spacing-md vars.$spacing-lg;
    max-width: 800px;
    margin: 0 auto;
  }
}

// Orientation-specific layouts
@include mixins.mobile-landscape {
  .layout {
    // Adjust for landscape mobile navigation
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

// Device-specific optimizations
@include mixins.phone-only {
  .main {
    // Ensure content doesn't get too cramped on small phones
    min-width: 280px;
  }
}

@include mixins.tablet-large {
  .main.withPadding {
    // More generous padding on large tablets
    padding: vars.$spacing-xxl;
    max-width: 1000px;
  }
}

// Loading overlay
.loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(white, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: vars.$z-index-modal;
  backdrop-filter: blur(2px);
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: vars.$spacing-md;
  padding: vars.$spacing-xl;
  background: white;
  border-radius: vars.$border-radius-lg;
  box-shadow: vars.$shadow-lg;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid vars.$light-color;
  border-top: 4px solid vars.$primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loadingText {
  color: vars.$text-primary;
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}