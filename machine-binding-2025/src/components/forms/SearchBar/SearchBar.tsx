import React, { useState, useRef, useEffect } from 'react';
import { BaseComponentProps } from '../../../types';
import styles from './SearchBar.module.scss';

export interface SearchResult {
  id: string;
  title: string;
  subtitle?: string;
  data?: any;
}

export interface SearchBarProps extends BaseComponentProps {
  value?: string;
  placeholder?: string;
  loading?: boolean;
  disabled?: boolean;
  results?: SearchResult[];
  showResults?: boolean;
  debounceMs?: number;
  onSearch?: (query: string) => void;
  onResultSelect?: (result: SearchResult) => void;
  onClear?: () => void;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value = '',
  placeholder = '请输入搜索关键字',
  loading = false,
  disabled = false,
  results = [],
  showResults = false,
  debounceMs = 300,
  onSearch,
  onResultSelect,
  onClear,
  onChange,
  onFocus,
  onBlur,
  className = '',
  testId,
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Debounced search
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (onSearch && inputValue.trim()) {
        onSearch(inputValue.trim());
      }
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [inputValue, onSearch, debounceMs]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    setHighlightedIndex(-1);
    onChange?.(newValue);
  };

  const handleClear = () => {
    setInputValue('');
    setHighlightedIndex(-1);
    onClear?.();
    onChange?.('');
    inputRef.current?.focus();
  };

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    // Delay blur to allow result selection
    setTimeout(() => {
      setIsFocused(false);
      setHighlightedIndex(-1);
      onBlur?.();
    }, 200);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!showResults || results.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setHighlightedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : 0
        );
        break;

      case 'ArrowUp':
        event.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : results.length - 1
        );
        break;

      case 'Enter':
        event.preventDefault();
        if (highlightedIndex >= 0 && results[highlightedIndex]) {
          handleResultSelect(results[highlightedIndex]);
        } else if (onSearch && inputValue.trim()) {
          onSearch(inputValue.trim());
        }
        break;

      case 'Escape':
        setHighlightedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleResultSelect = (result: SearchResult) => {
    setInputValue(result.title);
    setHighlightedIndex(-1);
    onResultSelect?.(result);
    onChange?.(result.title);
  };

  const shouldShowResults = showResults && isFocused && results.length > 0;

  return (
    <div 
      className={`${styles.searchBar} ${loading ? styles.loading : ''} ${className}`}
      data-testid={testId}
    >
      <div className={styles.searchIcon}>
        🔍
      </div>

      <input
        ref={inputRef}
        type="text"
        className={styles.searchInput}
        value={inputValue}
        placeholder={placeholder}
        disabled={disabled}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        data-testid={`${testId}-input`}
      />

      {inputValue && (
        <button
          className={styles.clearButton}
          onClick={handleClear}
          aria-label="清除搜索"
          data-testid={`${testId}-clear`}
        >
          ×
        </button>
      )}

      {shouldShowResults && (
        <div className={styles.resultsDropdown}>
          {results.map((result, index) => (
            <div
              key={result.id}
              className={`${styles.resultItem} ${
                index === highlightedIndex ? styles.highlighted : ''
              }`}
              onClick={() => handleResultSelect(result)}
              data-testid={`${testId}-result-${index}`}
            >
              <div>{result.title}</div>
              {result.subtitle && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  {result.subtitle}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {showResults && isFocused && inputValue && results.length === 0 && !loading && (
        <div className={styles.resultsDropdown}>
          <div className={styles.noResults}>
            没有找到相关结果
          </div>
        </div>
      )}
    </div>
  );
};