@use "../../../styles/variables" as vars;

.searchBar {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.searchInput {
  width: 100%;
  min-height: vars.$touch-target-min;
  padding: vars.$spacing-sm vars.$spacing-md;
  padding-left: 48px; // Space for search icon
  padding-right: 48px; // Space for clear button
  border: 2px solid #ddd;
  border-radius: vars.$border-radius-lg;
  font-size: vars.$font-size-md;
  font-family: inherit;
  background-color: vars.$background-primary;
  transition: all 0.2s ease;
  box-shadow: vars.$shadow-sm;

  &::placeholder {
    color: vars.$text-muted;
  }

  &:focus {
    outline: none;
    border-color: vars.$primary-color;
    box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1), vars.$shadow-md;
  }

  &:disabled {
    background-color: vars.$background-secondary;
    color: vars.$text-muted;
    cursor: not-allowed;
  }
}

.searchIcon {
  position: absolute;
  left: vars.$spacing-md;
  top: 50%;
  transform: translateY(-50%);
  color: vars.$text-muted;
  font-size: 18px;
  pointer-events: none;
}

.clearButton {
  position: absolute;
  right: vars.$spacing-sm;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: vars.$text-muted;
  cursor: pointer;
  padding: vars.$spacing-xs;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 16px;

  &:hover {
    background-color: rgba(vars.$text-muted, 0.1);
    color: vars.$text-primary;
  }

  &:focus {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

// Loading state
.searchBar.loading {
  .searchInput {
    background-image: linear-gradient(
      90deg,
      transparent,
      rgba(vars.$primary-color, 0.1),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
}

// Results dropdown
.resultsDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 vars.$border-radius-md vars.$border-radius-md;
  box-shadow: vars.$shadow-lg;
  max-height: 300px;
  overflow-y: auto;
  z-index: vars.$z-index-dropdown;
}

.resultItem {
  padding: vars.$spacing-md;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: vars.$background-secondary;
  }

  &:last-child {
    border-bottom: none;
  }

  &.highlighted {
    background-color: rgba(vars.$primary-color, 0.1);
  }
}

.noResults {
  padding: vars.$spacing-lg;
  text-align: center;
  color: vars.$text-muted;
  font-size: vars.$font-size-sm;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .searchBar {
    max-width: none;
  }

  .searchInput {
    min-height: vars.$touch-target-min;
    padding: vars.$spacing-md;
    padding-left: 52px;
    padding-right: 52px;
    font-size: vars.$font-size-md;
  }

  .searchIcon {
    left: vars.$spacing-lg;
    font-size: 20px;
  }

  .clearButton {
    right: vars.$spacing-md;
    width: 36px;
    height: 36px;
  }
}

// Animations
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}