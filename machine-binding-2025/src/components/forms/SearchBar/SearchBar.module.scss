@import '../../../styles/variables.scss';

.searchBar {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.searchInput {
  width: 100%;
  min-height: $touch-target-min;
  padding: $spacing-sm $spacing-md;
  padding-left: 48px; // Space for search icon
  padding-right: 48px; // Space for clear button
  border: 2px solid #ddd;
  border-radius: $border-radius-lg;
  font-size: $font-size-md;
  font-family: inherit;
  background-color: $background-primary;
  transition: all 0.2s ease;
  box-shadow: $shadow-sm;

  &::placeholder {
    color: $text-muted;
  }

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1), $shadow-md;
  }

  &:disabled {
    background-color: $background-secondary;
    color: $text-muted;
    cursor: not-allowed;
  }
}

.searchIcon {
  position: absolute;
  left: $spacing-md;
  top: 50%;
  transform: translateY(-50%);
  color: $text-muted;
  font-size: 18px;
  pointer-events: none;
}

.clearButton {
  position: absolute;
  right: $spacing-sm;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: $text-muted;
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 16px;

  &:hover {
    background-color: rgba($text-muted, 0.1);
    color: $text-primary;
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// Loading state
.searchBar.loading {
  .searchInput {
    background-image: linear-gradient(
      90deg,
      transparent,
      rgba($primary-color, 0.1),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
}

// Results dropdown
.resultsDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 $border-radius-md $border-radius-md;
  box-shadow: $shadow-lg;
  max-height: 300px;
  overflow-y: auto;
  z-index: $z-index-dropdown;
}

.resultItem {
  padding: $spacing-md;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: $background-secondary;
  }

  &:last-child {
    border-bottom: none;
  }

  &.highlighted {
    background-color: rgba($primary-color, 0.1);
  }
}

.noResults {
  padding: $spacing-lg;
  text-align: center;
  color: $text-muted;
  font-size: $font-size-sm;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .searchBar {
    max-width: none;
  }

  .searchInput {
    min-height: $touch-target-min;
    padding: $spacing-md;
    padding-left: 52px;
    padding-right: 52px;
    font-size: $font-size-md;
  }

  .searchIcon {
    left: $spacing-lg;
    font-size: 20px;
  }

  .clearButton {
    right: $spacing-md;
    width: 36px;
    height: 36px;
  }
}

// Animations
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}