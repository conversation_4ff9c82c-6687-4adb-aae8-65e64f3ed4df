import React from 'react';
import { BaseComponentProps } from '../../../types';
import styles from './FormField.module.scss';

export interface FormFieldProps extends BaseComponentProps {
  label?: string;
  required?: boolean;
  error?: string;
  success?: string;
  helpText?: string;
  variant?: 'default' | 'compact' | 'inline';
  htmlFor?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  children,
  label,
  required = false,
  error,
  success,
  helpText,
  variant = 'default',
  htmlFor,
  className = '',
  testId,
}) => {
  const fieldClasses = [
    styles.formField,
    styles[variant],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={fieldClasses} data-testid={testId}>
      {label && (
        <label 
          htmlFor={htmlFor}
          className={`${styles.label} ${required ? styles.required : ''}`}
        >
          {label}
        </label>
      )}
      
      <div className={styles.inputWrapper}>
        {children}
      </div>

      {error && (
        <div className={styles.errorMessage} role="alert">
          ⚠️ {error}
        </div>
      )}

      {success && !error && (
        <div className={styles.successMessage}>
          ✅ {success}
        </div>
      )}

      {helpText && !error && !success && (
        <div className={styles.helpText}>
          {helpText}
        </div>
      )}
    </div>
  );
};