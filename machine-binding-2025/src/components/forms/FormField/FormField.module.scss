@use "../../../styles/variables" as vars;

.formField {
  margin-bottom: vars.$spacing-lg;
}

.label {
  display: block;
  margin-bottom: vars.$spacing-xs;
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;

  &.required::after {
    content: ' *';
    color: vars.$danger-color;
  }
}

.inputWrapper {
  position: relative;
}

.helpText {
  margin-top: vars.$spacing-xs;
  font-size: vars.$font-size-xs;
  color: vars.$text-muted;
  line-height: 1.4;
}

.errorMessage {
  margin-top: vars.$spacing-xs;
  font-size: vars.$font-size-xs;
  color: vars.$danger-color;
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  line-height: 1.4;
}

.successMessage {
  margin-top: vars.$spacing-xs;
  font-size: vars.$font-size-xs;
  color: vars.$success-color;
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  line-height: 1.4;
}

// Compact variant
.formField.compact {
  margin-bottom: vars.$spacing-md;
}

// Inline variant
.formField.inline {
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  margin-bottom: vars.$spacing-md;

  .label {
    margin-bottom: 0;
    min-width: 120px;
    flex-shrink: 0;
  }

  .inputWrapper {
    flex: 1;
  }

  .helpText,
  .errorMessage,
  .successMessage {
    margin-top: 0;
    margin-left: vars.$spacing-md;
  }
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .formField.inline {
    flex-direction: column;
    align-items: stretch;
    gap: vars.$spacing-xs;

    .label {
      min-width: auto;
    }

    .helpText,
    .errorMessage,
    .successMessage {
      margin-left: 0;
      margin-top: vars.$spacing-xs;
    }
  }
}