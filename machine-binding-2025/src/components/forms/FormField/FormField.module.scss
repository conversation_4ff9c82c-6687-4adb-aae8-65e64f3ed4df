@import '../../../styles/variables.scss';

.formField {
  margin-bottom: $spacing-lg;
}

.label {
  display: block;
  margin-bottom: $spacing-xs;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-primary;

  &.required::after {
    content: ' *';
    color: $danger-color;
  }
}

.inputWrapper {
  position: relative;
}

.helpText {
  margin-top: $spacing-xs;
  font-size: $font-size-xs;
  color: $text-muted;
  line-height: 1.4;
}

.errorMessage {
  margin-top: $spacing-xs;
  font-size: $font-size-xs;
  color: $danger-color;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  line-height: 1.4;
}

.successMessage {
  margin-top: $spacing-xs;
  font-size: $font-size-xs;
  color: $success-color;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  line-height: 1.4;
}

// Compact variant
.formField.compact {
  margin-bottom: $spacing-md;
}

// Inline variant
.formField.inline {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-md;

  .label {
    margin-bottom: 0;
    min-width: 120px;
    flex-shrink: 0;
  }

  .inputWrapper {
    flex: 1;
  }

  .helpText,
  .errorMessage,
  .successMessage {
    margin-top: 0;
    margin-left: $spacing-md;
  }
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .formField.inline {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-xs;

    .label {
      min-width: auto;
    }

    .helpText,
    .errorMessage,
    .successMessage {
      margin-left: 0;
      margin-top: $spacing-xs;
    }
  }
}