@import '../../styles/variables.scss';

.profile {
  max-width: 800px;
  margin: 0 auto;
  padding: $spacing-lg;
}

// User Header Section
.userHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.avatarContainer {
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);

    .avatarOverlay {
      opacity: 1;
    }
  }
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid rgba(white, 0.3);
  object-fit: cover;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: rgba(white, 0.6);
  }
}

.avatarOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatarText {
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  color: white;
  text-align: center;
}

.userInfo {
  flex: 1;
  z-index: 1;
}

.userName {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  margin: 0 0 $spacing-xs 0;
}

.userEmail {
  font-size: $font-size-md;
  opacity: 0.9;
  margin: 0 0 $spacing-sm 0;
}

.userMeta {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.department {
  background: rgba(white, 0.2);
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
}

.adminBadge {
  background: rgba($warning-color, 0.9);
  color: white;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Section Styles
.section {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
}

.sectionTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-lg 0;
  padding-bottom: $spacing-sm;
  border-bottom: 2px solid $background-secondary;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-lg;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.infoLabel {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: $font-size-md;
  color: $text-primary;
  font-weight: $font-weight-medium;
  word-break: break-word;
}

// Help Section
.helpGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-md;
}

// Admin Section
.adminGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-md;
}

.adminItem {
  background: linear-gradient(135deg, rgba($danger-color, 0.1) 0%, rgba($warning-color, 0.1) 100%);
  border: 2px solid rgba($danger-color, 0.2);
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    border-color: $danger-color;
    background: linear-gradient(135deg, rgba($danger-color, 0.15) 0%, rgba($warning-color, 0.15) 100%);
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  &:active {
    transform: translateY(0);
  }
}

.adminIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.adminContent {
  flex: 1;
}

.adminTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $danger-color;
  margin: 0 0 $spacing-xs 0;
}

.adminDescription {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0;
}

.helpItem {
  background: $background-light;
  border: 2px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    border-color: $primary-color;
    background: rgba($primary-color, 0.05);
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  &:active {
    transform: translateY(0);
  }
}

.helpIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.helpContent {
  flex: 1;
}

.helpTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-xs 0;
}

.helpDescription {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0;
}

// Actions Section
.actions {
  display: flex;
  gap: $spacing-md;
  justify-content: center;
  flex-wrap: wrap;
  padding: $spacing-lg 0;
}

.actionButton {
  min-width: 120px;
}

// Modal Styles
.avatarModal {
  text-align: center;
  padding: $spacing-lg;
}

.fullAvatar {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-lg;
}

.avatarInfo {
  h4 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: 0 0 $spacing-xs 0;
  }

  p {
    font-size: $font-size-md;
    color: $text-muted;
    margin: 0;
  }
}

.logoutModal {
  text-align: center;
  padding: $spacing-lg;
}

.logoutIcon {
  font-size: 3rem;
  margin-bottom: $spacing-lg;
}

.logoutMessage {
  font-size: $font-size-md;
  color: $text-primary;
  margin: 0 0 $spacing-xl 0;
  line-height: 1.6;
}

.logoutActions {
  display: flex;
  gap: $spacing-md;
  justify-content: center;
}

// Error State
.errorContainer {
  background: rgba($danger-color, 0.02);
  border: 1px solid rgba($danger-color, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-xxl;
  text-align: center;
  margin: $spacing-lg 0;
}

.errorIcon {
  font-size: 3rem;
  color: $danger-color;
  margin-bottom: $spacing-lg;
}

.errorTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $danger-color;
  margin: 0 0 $spacing-md 0;
}

.errorMessage {
  font-size: $font-size-md;
  color: $text-muted;
  margin: 0 0 $spacing-lg 0;
}

// Enhanced mobile optimizations with orientation support
@include mobile-portrait {
  .profile {
    @include adaptive-content-area;
    padding: $spacing-md;
  }

  .userHeader {
    padding: $spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: $spacing-md;
  }

  .avatar {
    width: 100px;
    height: 100px;
  }

  .userName {
    font-size: $font-size-lg;
  }

  .userMeta {
    justify-content: center;
    flex-wrap: wrap;
  }

  .section {
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .helpGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .helpItem {
    padding: $spacing-md;
    flex-direction: column;
    text-align: center;
    gap: $spacing-sm;
  }

  .adminGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .adminItem {
    padding: $spacing-md;
    flex-direction: column;
    text-align: center;
    gap: $spacing-sm;
  }

  .actions {
    flex-direction: column;
    padding: $spacing-md 0;
    gap: $spacing-sm;
  }

  .actionButton {
    width: 100%;
    min-height: $touch-target-comfortable;
  }

  .fullAvatar {
    width: 150px;
    height: 150px;
  }

  .logoutActions {
    flex-direction: column;
    gap: $spacing-sm;
  }
}

@include mobile-landscape {
  .profile {
    @include adaptive-content-area;
    padding: $spacing-sm $spacing-md;
  }

  .userHeader {
    padding: $spacing-md $spacing-lg;
    flex-direction: row;
    text-align: left;
    gap: $spacing-lg;
  }

  .avatar {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }

  .userName {
    font-size: $font-size-md;
  }

  .userMeta {
    justify-content: flex-start;
  }

  .section {
    padding: $spacing-md;
    margin-bottom: $spacing-sm;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }

  .helpGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }

  .helpItem {
    padding: $spacing-sm $spacing-md;
    flex-direction: row;
    text-align: left;
    gap: $spacing-sm;
  }

  .adminGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }

  .adminItem {
    padding: $spacing-sm $spacing-md;
    flex-direction: row;
    text-align: left;
    gap: $spacing-sm;
  }

  .actions {
    flex-direction: row;
    padding: $spacing-sm 0;
    gap: $spacing-sm;
    justify-content: center;
  }

  .actionButton {
    min-width: 120px;
  }

  .fullAvatar {
    width: 120px;
    height: 120px;
  }

  .logoutActions {
    flex-direction: row;
    gap: $spacing-md;
    justify-content: center;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .profile {
    padding: $spacing-xl;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .helpGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Animation for sections
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: slideUp 0.3s ease-out;
}

.userHeader {
  animation: slideUp 0.3s ease-out;
}

// Hover effects for interactive elements
.helpItem,
.avatarContainer {
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// Loading state for avatar
.avatar {
  &[src=""] {
    background: $background-secondary;
    
    &::before {
      content: '👤';
      font-size: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: $text-muted;
    }
  }
}