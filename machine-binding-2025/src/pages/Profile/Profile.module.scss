@use "../../../styles/variables" as vars;

.profile {
  max-width: 800px;
  margin: 0 auto;
  padding: vars.$spacing-lg;
}

// User Header Section
.userHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: vars.$spacing-xl;
  border-radius: vars.$border-radius-lg;
  margin-bottom: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-lg;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.avatarContainer {
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);

    .avatarOverlay {
      opacity: 1;
    }
  }
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid rgba(white, 0.3);
  object-fit: cover;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: rgba(white, 0.6);
  }
}

.avatarOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatarText {
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-medium;
  color: white;
  text-align: center;
}

.userInfo {
  flex: 1;
  z-index: 1;
}

.userName {
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-bold;
  margin: 0 0 vars.$spacing-xs 0;
}

.userEmail {
  font-size: vars.$font-size-md;
  opacity: 0.9;
  margin: 0 0 vars.$spacing-sm 0;
}

.userMeta {
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
  flex-wrap: wrap;
}

.department {
  background: rgba(white, 0.2);
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
}

.adminBadge {
  background: rgba(vars.$warning-color, 0.9);
  color: white;
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Section Styles
.section {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
}

.sectionTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-lg 0;
  padding-bottom: vars.$spacing-sm;
  border-bottom: 2px solid vars.$background-secondary;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: vars.$spacing-lg;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
}

.infoLabel {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: vars.$font-size-md;
  color: vars.$text-primary;
  font-weight: vars.$font-weight-medium;
  word-break: break-word;
}

// Help Section
.helpGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-md;
}

// Admin Section
.adminGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-md;
}

.adminItem {
  background: linear-gradient(135deg, rgba(vars.$danger-color, 0.1) 0%, rgba(vars.$warning-color, 0.1) 100%);
  border: 2px solid rgba(vars.$danger-color, 0.2);
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    border-color: vars.$danger-color;
    background: linear-gradient(135deg, rgba(vars.$danger-color, 0.15) 0%, rgba(vars.$warning-color, 0.15) 100%);
    transform: translateY(-2px);
    box-shadow: vars.$shadow-md;
  }

  &:active {
    transform: translateY(0);
  }
}

.adminIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.adminContent {
  flex: 1;
}

.adminTitle {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-semibold;
  color: vars.$danger-color;
  margin: 0 0 vars.$spacing-xs 0;
}

.adminDescription {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0;
}

.helpItem {
  background: vars.$background-light;
  border: 2px solid vars.$border-color;
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    border-color: vars.$primary-color;
    background: rgba(vars.$primary-color, 0.05);
    transform: translateY(-2px);
    box-shadow: vars.$shadow-md;
  }

  &:active {
    transform: translateY(0);
  }
}

.helpIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.helpContent {
  flex: 1;
}

.helpTitle {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-xs 0;
}

.helpDescription {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0;
}

// Actions Section
.actions {
  display: flex;
  gap: vars.$spacing-md;
  justify-content: center;
  flex-wrap: wrap;
  padding: vars.$spacing-lg 0;
}

.actionButton {
  min-width: 120px;
}

// Modal Styles
.avatarModal {
  text-align: center;
  padding: vars.$spacing-lg;
}

.fullAvatar {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-lg;
}

.avatarInfo {
  h4 {
    font-size: vars.$font-size-lg;
    font-weight: vars.$font-weight-semibold;
    color: vars.$text-primary;
    margin: 0 0 vars.$spacing-xs 0;
  }

  p {
    font-size: vars.$font-size-md;
    color: vars.$text-muted;
    margin: 0;
  }
}

.logoutModal {
  text-align: center;
  padding: vars.$spacing-lg;
}

.logoutIcon {
  font-size: 3rem;
  margin-bottom: vars.$spacing-lg;
}

.logoutMessage {
  font-size: vars.$font-size-md;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-xl 0;
  line-height: 1.6;
}

.logoutActions {
  display: flex;
  gap: vars.$spacing-md;
  justify-content: center;
}

// Error State
.errorContainer {
  background: rgba(vars.$danger-color, 0.02);
  border: 1px solid rgba(vars.$danger-color, 0.1);
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xxl;
  text-align: center;
  margin: vars.$spacing-lg 0;
}

.errorIcon {
  font-size: 3rem;
  color: vars.$danger-color;
  margin-bottom: vars.$spacing-lg;
}

.errorTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$danger-color;
  margin: 0 0 vars.$spacing-md 0;
}

.errorMessage {
  font-size: vars.$font-size-md;
  color: vars.$text-muted;
  margin: 0 0 vars.$spacing-lg 0;
}

// Enhanced mobile optimizations with orientation support
@include mixins.mobile-portrait {
  .profile {
    @include mixins.adaptive-content-area;
    padding: vars.$spacing-md;
  }

  .userHeader {
    padding: vars.$spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-md;
  }

  .avatar {
    width: 100px;
    height: 100px;
  }

  .userName {
    font-size: vars.$font-size-lg;
  }

  .userMeta {
    justify-content: center;
    flex-wrap: wrap;
  }

  .section {
    padding: vars.$spacing-lg;
    margin-bottom: vars.$spacing-md;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .helpGrid {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .helpItem {
    padding: vars.$spacing-md;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-sm;
  }

  .adminGrid {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .adminItem {
    padding: vars.$spacing-md;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-sm;
  }

  .actions {
    flex-direction: column;
    padding: vars.$spacing-md 0;
    gap: vars.$spacing-sm;
  }

  .actionButton {
    width: 100%;
    min-height: vars.$touch-target-comfortable;
  }

  .fullAvatar {
    width: 150px;
    height: 150px;
  }

  .logoutActions {
    flex-direction: column;
    gap: vars.$spacing-sm;
  }
}

@include mixins.mobile-landscape {
  .profile {
    @include mixins.adaptive-content-area;
    padding: vars.$spacing-sm vars.$spacing-md;
  }

  .userHeader {
    padding: vars.$spacing-md vars.$spacing-lg;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-lg;
  }

  .avatar {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }

  .userName {
    font-size: vars.$font-size-md;
  }

  .userMeta {
    justify-content: flex-start;
  }

  .section {
    padding: vars.$spacing-md;
    margin-bottom: vars.$spacing-sm;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
  }

  .helpGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
  }

  .helpItem {
    padding: vars.$spacing-sm vars.$spacing-md;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-sm;
  }

  .adminGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
  }

  .adminItem {
    padding: vars.$spacing-sm vars.$spacing-md;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-sm;
  }

  .actions {
    flex-direction: row;
    padding: vars.$spacing-sm 0;
    gap: vars.$spacing-sm;
    justify-content: center;
  }

  .actionButton {
    min-width: 120px;
  }

  .fullAvatar {
    width: 120px;
    height: 120px;
  }

  .logoutActions {
    flex-direction: row;
    gap: vars.$spacing-md;
    justify-content: center;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) and (max-width: vars.$tablet-large - 1px) {
  .profile {
    padding: vars.$spacing-xl;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .helpGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Animation for sections
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: slideUp 0.3s ease-out;
}

.userHeader {
  animation: slideUp 0.3s ease-out;
}

// Hover effects for interactive elements
.helpItem,
.avatarContainer {
  &:focus {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

// Loading state for avatar
.avatar {
  &[src=""] {
    background: vars.$background-secondary;
    
    &::before {
      content: '👤';
      font-size: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: vars.$text-muted;
    }
  }
}