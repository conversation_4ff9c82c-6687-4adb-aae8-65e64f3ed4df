import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout } from '../../components/navigation';
import { Button, Modal } from '../../components/common';
import { useAuth, useToast } from '../../hooks';
import styles from './Profile.module.scss';

export const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { showToast } = useToast();
  
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  // Handle logout confirmation
  const handleLogout = async () => {
    try {
      await logout();
      showToast({
        type: 'success',
        title: '退出成功',
        message: '您已成功退出登录',
      });
      navigate('/login');
    } catch (error) {
      showToast({
        type: 'error',
        title: '退出失败',
        message: '退出登录时发生错误，请重试',
      });
    } finally {
      setShowLogoutModal(false);
    }
  };

  // Handle avatar click
  const handleAvatarClick = () => {
    setShowAvatarModal(true);
  };

  // Get app version info
  const getAppInfo = () => {
    return {
      version: '1.0.0',
      buildDate: '2024-01-15',
      environment: process.env.NODE_ENV || 'development',
    };
  };

  const appInfo = getAppInfo();

  const navProps = {
    title: '个人中心',
    showBack: true,
    onBack: () => navigate('/home'),
  };

  if (!user) {
    return (
      <Layout navProps={navProps}>
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>⚠️</div>
          <h3 className={styles.errorTitle}>用户信息加载失败</h3>
          <p className={styles.errorMessage}>无法获取用户信息，请重新登录</p>
          <Button
            variant="primary"
            onClick={() => navigate('/login')}
          >
            重新登录
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout navProps={navProps}>
      <div className={styles.profile}>
        {/* User Header Section */}
        <div className={styles.userHeader}>
          <div 
            className={styles.avatarContainer}
            onClick={handleAvatarClick}
          >
            <img
              src={user.headerImg || '/default-avatar.png'}
              alt={`${user.name}的头像`}
              className={styles.avatar}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/default-avatar.png';
              }}
            />
            <div className={styles.avatarOverlay}>
              <span className={styles.avatarText}>点击查看</span>
            </div>
          </div>
          
          <div className={styles.userInfo}>
            <h2 className={styles.userName}>{user.name}</h2>
            <p className={styles.userEmail}>{user.email}</p>
            <div className={styles.userMeta}>
              <span className={styles.department}>{user.department}</span>
              {user.isAdmin && (
                <span className={styles.adminBadge}>管理员</span>
              )}
            </div>
          </div>
        </div>

        {/* User Details Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>基本信息</h3>
          <div className={styles.infoGrid}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>用户名</span>
              <span className={styles.infoValue}>{user.userName}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>姓名</span>
              <span className={styles.infoValue}>{user.name}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>邮箱</span>
              <span className={styles.infoValue}>{user.email}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>部门</span>
              <span className={styles.infoValue}>{user.department}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>用户ID</span>
              <span className={styles.infoValue}>{user.id}</span>
            </div>
            {user.created_at && (
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>注册时间</span>
                <span className={styles.infoValue}>
                  {new Date(user.created_at).toLocaleDateString('zh-CN')}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* App Information Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>应用信息</h3>
          <div className={styles.infoGrid}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>应用版本</span>
              <span className={styles.infoValue}>{appInfo.version}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>构建日期</span>
              <span className={styles.infoValue}>{appInfo.buildDate}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>运行环境</span>
              <span className={styles.infoValue}>
                {appInfo.environment === 'production' ? '生产环境' : '开发环境'}
              </span>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>帮助与支持</h3>
          <div className={styles.helpGrid}>
            <button 
              className={styles.helpItem}
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '使用帮助',
                  message: '帮助文档功能正在开发中',
                });
              }}
            >
              <div className={styles.helpIcon}>📖</div>
              <div className={styles.helpContent}>
                <h4 className={styles.helpTitle}>使用帮助</h4>
                <p className={styles.helpDescription}>查看应用使用说明</p>
              </div>
            </button>

            <button 
              className={styles.helpItem}
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '联系我们',
                  message: '客服功能正在开发中',
                });
              }}
            >
              <div className={styles.helpIcon}>📞</div>
              <div className={styles.helpContent}>
                <h4 className={styles.helpTitle}>联系我们</h4>
                <p className={styles.helpDescription}>获取技术支持</p>
              </div>
            </button>

            <button 
              className={styles.helpItem}
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '意见反馈',
                  message: '反馈功能正在开发中',
                });
              }}
            >
              <div className={styles.helpIcon}>💬</div>
              <div className={styles.helpContent}>
                <h4 className={styles.helpTitle}>意见反馈</h4>
                <p className={styles.helpDescription}>提交问题和建议</p>
              </div>
            </button>
          </div>
        </div>

        {/* Admin Section - Only show for admin users */}
        {user.isAdmin && (
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>管理功能</h3>
            <div className={styles.adminGrid}>
              <button 
                className={styles.adminItem}
                onClick={() => navigate('/admin')}
              >
                <div className={styles.adminIcon}>⚙️</div>
                <div className={styles.adminContent}>
                  <h4 className={styles.adminTitle}>管理中心</h4>
                  <p className={styles.adminDescription}>系统管理和配置</p>
                </div>
              </button>

              <button 
                className={styles.adminItem}
                onClick={() => {
                  showToast({
                    type: 'info',
                    title: '系统监控',
                    message: '系统监控功能正在开发中',
                  });
                }}
              >
                <div className={styles.adminIcon}>📊</div>
                <div className={styles.adminContent}>
                  <h4 className={styles.adminTitle}>系统监控</h4>
                  <p className={styles.adminDescription}>查看系统状态</p>
                </div>
              </button>
            </div>
          </div>
        )}

        {/* Actions Section */}
        <div className={styles.actions}>
          <Button
            variant="outline"
            onClick={() => navigate('/home')}
            className={styles.actionButton}
          >
            返回首页
          </Button>
          <Button
            variant="danger"
            onClick={() => setShowLogoutModal(true)}
            className={styles.actionButton}
          >
            退出登录
          </Button>
        </div>
      </div>

      {/* Avatar Modal */}
      {showAvatarModal && (
        <Modal
          isOpen={showAvatarModal}
          onClose={() => setShowAvatarModal(false)}
          title="用户头像"
          size="md"
        >
          <div className={styles.avatarModal}>
            <img
              src={user.headerImg || '/default-avatar.png'}
              alt={`${user.name}的头像`}
              className={styles.fullAvatar}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/default-avatar.png';
              }}
            />
            <div className={styles.avatarInfo}>
              <h4>{user.name}</h4>
              <p>{user.email}</p>
            </div>
          </div>
        </Modal>
      )}

      {/* Logout Confirmation Modal */}
      {showLogoutModal && (
        <Modal
          isOpen={showLogoutModal}
          onClose={() => setShowLogoutModal(false)}
          title="确认退出"
          size="sm"
        >
          <div className={styles.logoutModal}>
            <div className={styles.logoutIcon}>🚪</div>
            <p className={styles.logoutMessage}>
              您确定要退出登录吗？退出后需要重新输入用户名和密码。
            </p>
            <div className={styles.logoutActions}>
              <Button
                variant="outline"
                onClick={() => setShowLogoutModal(false)}
              >
                取消
              </Button>
              <Button
                variant="danger"
                onClick={handleLogout}
              >
                确认退出
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Layout>
  );
};