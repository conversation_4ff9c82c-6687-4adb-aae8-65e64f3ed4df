@use "../../../styles/variables" as vars;

.recordsList {
  max-width: 800px;
  margin: 0 auto;
  padding: vars.$spacing-lg;
}

.searchSection {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-lg;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
}

.searchTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-md 0;
}

.recordsContainer {
  background: white;
  border-radius: vars.$border-radius-lg;
  overflow: hidden;
  box-shadow: vars.$shadow-sm;
}

.recordsHeader {
  background: vars.$background-secondary;
  padding: vars.$spacing-lg;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recordsTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0;
}

.recordsCount {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  background: rgba(vars.$primary-color, 0.1);
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
}

.recordsList {
  .recordItem {
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
    cursor: pointer;

    &:hover {
      background-color: rgba(vars.$primary-color, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

.recordItem {
  padding: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
}

.recordIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(vars.$primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: vars.$primary-color;
  flex-shrink: 0;
}

.recordContent {
  flex: 1;
  min-width: 0;
}

.recordHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: vars.$spacing-xs;
}

.recordName {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;
  margin: 0;
  word-break: break-word;
}

.recordDate {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  flex-shrink: 0;
  margin-left: vars.$spacing-sm;
}

.recordDetails {
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
}

.recordCode {
  font-family: 'Courier New', monospace;
  background: vars.$background-secondary;
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
}

.recordStatus {
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-medium;
  text-transform: uppercase;
}

.recordStatus.pending {
  background: rgba(vars.$warning-color, 0.1);
  color: vars.$warning-color;
}

.recordStatus.confirmed {
  background: rgba(vars.$success-color, 0.1);
  color: vars.$success-color;
}

.recordStatus.rejected {
  background: rgba(vars.$danger-color, 0.1);
  color: vars.$danger-color;
}

.recordActions {
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
}

.actionButton {
  background: none;
  border: none;
  color: vars.$text-muted;
  cursor: pointer;
  padding: vars.$spacing-xs;
  border-radius: vars.$border-radius-sm;
  transition: all 0.2s ease;
  font-size: 16px;

  &:hover {
    background: rgba(vars.$primary-color, 0.1);
    color: vars.$primary-color;
  }

  &:focus {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

.loadingContainer {
  padding: vars.$spacing-xxl;
  text-align: center;
}

.emptyContainer {
  padding: vars.$spacing-xxl;
  text-align: center;
  color: vars.$text-muted;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: vars.$spacing-lg;
  opacity: 0.6;
}

.emptyTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-secondary;
  margin: 0 0 vars.$spacing-md 0;
}

.emptyDescription {
  font-size: vars.$font-size-md;
  color: vars.$text-muted;
  margin: 0 0 vars.$spacing-lg 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.refreshButton {
  margin-top: vars.$spacing-lg;
}

.errorContainer {
  padding: vars.$spacing-xxl;
  text-align: center;
  background: rgba(vars.$danger-color, 0.02);
  border: 1px solid rgba(vars.$danger-color, 0.1);
  border-radius: vars.$border-radius-lg;
  margin: vars.$spacing-lg 0;
}

.errorIcon {
  font-size: 3rem;
  color: vars.$danger-color;
  margin-bottom: vars.$spacing-lg;
}

.errorTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-medium;
  color: vars.$danger-color;
  margin: 0 0 vars.$spacing-md 0;
}

.errorMessage {
  font-size: vars.$font-size-md;
  color: vars.$text-muted;
  margin: 0 0 vars.$spacing-lg 0;
}

.retryButton {
  margin-top: vars.$spacing-md;
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: vars.$spacing-sm;
  padding: vars.$spacing-lg;
  background: white;
  border-top: 1px solid #eee;
}

.paginationButton {
  background: none;
  border: 1px solid #ddd;
  color: vars.$text-muted;
  cursor: pointer;
  padding: vars.$spacing-sm vars.$spacing-md;
  border-radius: vars.$border-radius-sm;
  transition: all 0.2s ease;
  font-size: vars.$font-size-sm;

  &:hover:not(:disabled) {
    background: vars.$primary-color;
    color: white;
    border-color: vars.$primary-color;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.active {
    background: vars.$primary-color;
    color: white;
    border-color: vars.$primary-color;
  }
}

.paginationInfo {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0 vars.$spacing-md;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .recordsList {
    padding: vars.$spacing-md;
  }

  .searchSection,
  .recordsContainer {
    margin-bottom: vars.$spacing-md;
  }

  .recordsHeader {
    padding: vars.$spacing-md;
    flex-direction: column;
    align-items: flex-start;
    gap: vars.$spacing-sm;
  }

  .recordItem {
    padding: vars.$spacing-md;
    flex-direction: column;
    align-items: flex-start;
    gap: vars.$spacing-sm;
  }

  .recordIcon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .recordHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: vars.$spacing-xs;
    width: 100%;
  }

  .recordDetails {
    flex-wrap: wrap;
    gap: vars.$spacing-sm;
  }

  .recordActions {
    width: 100%;
    justify-content: flex-end;
  }

  .pagination {
    padding: vars.$spacing-md;
    flex-wrap: wrap;
    gap: vars.$spacing-xs;
  }

  .paginationInfo {
    width: 100%;
    text-align: center;
    margin: vars.$spacing-sm 0;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) and (max-width: vars.$tablet-large - 1px) {
  .recordsList {
    padding: vars.$spacing-xl;
  }
}

// Loading animation
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.recordItem.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

// Slide in animation for new records
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recordItem {
  animation: slideIn 0.3s ease-out;
}