@import '../../styles/variables.scss';

.recordsList {
  max-width: 800px;
  margin: 0 auto;
  padding: $spacing-lg;
}

.searchSection {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
}

.searchTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-md 0;
}

.recordsContainer {
  background: white;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
}

.recordsHeader {
  background: $background-secondary;
  padding: $spacing-lg;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recordsTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0;
}

.recordsCount {
  font-size: $font-size-sm;
  color: $text-muted;
  background: rgba($primary-color, 0.1);
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
}

.recordsList {
  .recordItem {
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
    cursor: pointer;

    &:hover {
      background-color: rgba($primary-color, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

.recordItem {
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.recordIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: $primary-color;
  flex-shrink: 0;
}

.recordContent {
  flex: 1;
  min-width: 0;
}

.recordHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-xs;
}

.recordName {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin: 0;
  word-break: break-word;
}

.recordDate {
  font-size: $font-size-sm;
  color: $text-muted;
  flex-shrink: 0;
  margin-left: $spacing-sm;
}

.recordDetails {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  font-size: $font-size-sm;
  color: $text-muted;
}

.recordCode {
  font-family: 'Courier New', monospace;
  background: $background-secondary;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
}

.recordStatus {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  text-transform: uppercase;
}

.recordStatus.pending {
  background: rgba($warning-color, 0.1);
  color: $warning-color;
}

.recordStatus.confirmed {
  background: rgba($success-color, 0.1);
  color: $success-color;
}

.recordStatus.rejected {
  background: rgba($danger-color, 0.1);
  color: $danger-color;
}

.recordActions {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.actionButton {
  background: none;
  border: none;
  color: $text-muted;
  cursor: pointer;
  padding: $spacing-xs;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;
  font-size: 16px;

  &:hover {
    background: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

.loadingContainer {
  padding: $spacing-xxl;
  text-align: center;
}

.emptyContainer {
  padding: $spacing-xxl;
  text-align: center;
  color: $text-muted;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: $spacing-lg;
  opacity: 0.6;
}

.emptyTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $text-secondary;
  margin: 0 0 $spacing-md 0;
}

.emptyDescription {
  font-size: $font-size-md;
  color: $text-muted;
  margin: 0 0 $spacing-lg 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.refreshButton {
  margin-top: $spacing-lg;
}

.errorContainer {
  padding: $spacing-xxl;
  text-align: center;
  background: rgba($danger-color, 0.02);
  border: 1px solid rgba($danger-color, 0.1);
  border-radius: $border-radius-lg;
  margin: $spacing-lg 0;
}

.errorIcon {
  font-size: 3rem;
  color: $danger-color;
  margin-bottom: $spacing-lg;
}

.errorTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $danger-color;
  margin: 0 0 $spacing-md 0;
}

.errorMessage {
  font-size: $font-size-md;
  color: $text-muted;
  margin: 0 0 $spacing-lg 0;
}

.retryButton {
  margin-top: $spacing-md;
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-lg;
  background: white;
  border-top: 1px solid #eee;
}

.paginationButton {
  background: none;
  border: 1px solid #ddd;
  color: $text-muted;
  cursor: pointer;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;
  font-size: $font-size-sm;

  &:hover:not(:disabled) {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.active {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }
}

.paginationInfo {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0 $spacing-md;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .recordsList {
    padding: $spacing-md;
  }

  .searchSection,
  .recordsContainer {
    margin-bottom: $spacing-md;
  }

  .recordsHeader {
    padding: $spacing-md;
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }

  .recordItem {
    padding: $spacing-md;
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }

  .recordIcon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .recordHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
    width: 100%;
  }

  .recordDetails {
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  .recordActions {
    width: 100%;
    justify-content: flex-end;
  }

  .pagination {
    padding: $spacing-md;
    flex-wrap: wrap;
    gap: $spacing-xs;
  }

  .paginationInfo {
    width: 100%;
    text-align: center;
    margin: $spacing-sm 0;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .recordsList {
    padding: $spacing-xl;
  }
}

// Loading animation
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.recordItem.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

// Slide in animation for new records
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recordItem {
  animation: slideIn 0.3s ease-out;
}