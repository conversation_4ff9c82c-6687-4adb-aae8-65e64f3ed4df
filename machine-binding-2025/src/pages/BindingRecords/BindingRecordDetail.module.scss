@import '../../styles/variables.scss';

.recordDetail {
  max-width: 800px;
  margin: 0 auto;
  padding: $spacing-lg;
}

// Header Section
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-lg;
}

.deviceIcon {
  font-size: 4rem;
  flex-shrink: 0;
}

.headerContent {
  flex: 1;
}

.deviceName {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  margin: 0 0 $spacing-sm 0;
}

.headerMeta {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;
}

.recordId {
  font-family: 'Courier New', monospace;
  font-size: $font-size-sm;
  opacity: 0.9;
  background: rgba(white, 0.2);
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
}

.status {
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.pending {
  background: rgba($warning-color, 0.9);
  color: white;
}

.status.confirmed {
  background: rgba($success-color, 0.9);
  color: white;
}

.status.rejected {
  background: rgba($danger-color, 0.9);
  color: white;
}

// Section Styles
.section {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
}

.sectionTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-lg 0;
  padding-bottom: $spacing-sm;
  border-bottom: 2px solid $background-secondary;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-xl;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.infoLabel {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: $font-size-md;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

// Image Section Styles
.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
}

.imageCategory {
  text-align: center;
}

.imageCategoryTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin: 0 0 $spacing-md 0;
}

.imagePreview {
  position: relative;
  aspect-ratio: 1;
  border-radius: $border-radius-md;
  overflow: hidden;
  background: $background-secondary;
  cursor: pointer;
  transition: transform 0.3s ease;
  margin-bottom: $spacing-sm;

  &:hover {
    transform: scale(1.02);
  }
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageCount {
  position: absolute;
  top: $spacing-sm;
  right: $spacing-sm;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
}

.noImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $text-muted;
  font-size: $font-size-sm;
  background: $background-secondary;
}

.viewButton {
  width: 100%;
}

// Image Modal
.imageModal {
  max-width: 90vw;
  max-height: 80vh;
}

// Actions Section
.actions {
  display: flex;
  gap: $spacing-md;
  justify-content: center;
  flex-wrap: wrap;
  padding: $spacing-lg 0;
}

.actionButton {
  min-width: 120px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  text-align: center;
}

.errorContainer {
  background: rgba($danger-color, 0.02);
  border: 1px solid rgba($danger-color, 0.1);
  border-radius: $border-radius-lg;
  padding: $spacing-xxl;
  text-align: center;
  margin: $spacing-lg 0;
}

.errorIcon {
  font-size: 3rem;
  color: $danger-color;
  margin-bottom: $spacing-lg;
}

.errorTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $danger-color;
  margin: 0 0 $spacing-md 0;
}

.errorMessage {
  font-size: $font-size-md;
  color: $text-muted;
  margin: 0 0 $spacing-lg 0;
}

.timeline {
  position: relative;
  padding-left: $spacing-xl;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: $background-secondary;
}

.timelineItem {
  position: relative;
  padding-bottom: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.timelineItem::before {
  content: '';
  position: absolute;
  left: -23px;
  top: 8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: $primary-color;
  border: 3px solid white;
  box-shadow: 0 0 0 2px $primary-color;
}

.timelineContent {
  background: white;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  box-shadow: $shadow-sm;
}

.timelineTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin: 0 0 $spacing-xs 0;
}

.timelineTime {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .recordDetail {
    padding: $spacing-md;
  }

  .header {
    padding: $spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: $spacing-md;
  }

  .deviceIcon {
    font-size: 3rem;
  }

  .deviceName {
    font-size: $font-size-lg;
  }

  .headerMeta {
    justify-content: center;
  }

  .section {
    padding: $spacing-lg;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .imageGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .actions {
    flex-direction: column;
    padding: $spacing-md 0;
  }

  .actionButton {
    width: 100%;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .recordDetail {
    padding: $spacing-xl;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .imageGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Animation for sections
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: slideUp 0.3s ease-out;
}

.header {
  animation: slideUp 0.3s ease-out;
}