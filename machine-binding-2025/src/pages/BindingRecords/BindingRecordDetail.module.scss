@use "../../../styles/variables" as vars;

.recordDetail {
  max-width: 800px;
  margin: 0 auto;
  padding: vars.$spacing-lg;
}

// Header Section
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: vars.$spacing-xl;
  border-radius: vars.$border-radius-lg;
  margin-bottom: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-lg;
}

.deviceIcon {
  font-size: 4rem;
  flex-shrink: 0;
}

.headerContent {
  flex: 1;
}

.deviceName {
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-bold;
  margin: 0 0 vars.$spacing-sm 0;
}

.headerMeta {
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  flex-wrap: wrap;
}

.recordId {
  font-family: 'Courier New', monospace;
  font-size: vars.$font-size-sm;
  opacity: 0.9;
  background: rgba(white, 0.2);
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
}

.status {
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.pending {
  background: rgba(vars.$warning-color, 0.9);
  color: white;
}

.status.confirmed {
  background: rgba(vars.$success-color, 0.9);
  color: white;
}

.status.rejected {
  background: rgba(vars.$danger-color, 0.9);
  color: white;
}

// Section Styles
.section {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
}

.sectionTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-lg 0;
  padding-bottom: vars.$spacing-sm;
  border-bottom: 2px solid vars.$background-secondary;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: vars.$spacing-lg;
  margin-bottom: vars.$spacing-xl;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
}

.infoLabel {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: vars.$font-size-md;
  color: vars.$text-primary;
  font-weight: vars.$font-weight-medium;
}

// Image Section Styles
.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-lg;
}

.imageCategory {
  text-align: center;
}

.imageCategoryTitle {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-md 0;
}

.imagePreview {
  position: relative;
  aspect-ratio: 1;
  border-radius: vars.$border-radius-md;
  overflow: hidden;
  background: vars.$background-secondary;
  cursor: pointer;
  transition: transform 0.3s ease;
  margin-bottom: vars.$spacing-sm;

  &:hover {
    transform: scale(1.02);
  }
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageCount {
  position: absolute;
  top: vars.$spacing-sm;
  right: vars.$spacing-sm;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-medium;
}

.noImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: vars.$text-muted;
  font-size: vars.$font-size-sm;
  background: vars.$background-secondary;
}

.viewButton {
  width: 100%;
}

// Image Modal
.imageModal {
  max-width: 90vw;
  max-height: 80vh;
}

// Actions Section
.actions {
  display: flex;
  gap: vars.$spacing-md;
  justify-content: center;
  flex-wrap: wrap;
  padding: vars.$spacing-lg 0;
}

.actionButton {
  min-width: 120px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: vars.$spacing-xxl;
  text-align: center;
}

.errorContainer {
  background: rgba(vars.$danger-color, 0.02);
  border: 1px solid rgba(vars.$danger-color, 0.1);
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xxl;
  text-align: center;
  margin: vars.$spacing-lg 0;
}

.errorIcon {
  font-size: 3rem;
  color: vars.$danger-color;
  margin-bottom: vars.$spacing-lg;
}

.errorTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$danger-color;
  margin: 0 0 vars.$spacing-md 0;
}

.errorMessage {
  font-size: vars.$font-size-md;
  color: vars.$text-muted;
  margin: 0 0 vars.$spacing-lg 0;
}

.timeline {
  position: relative;
  padding-left: vars.$spacing-xl;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: vars.$background-secondary;
}

.timelineItem {
  position: relative;
  padding-bottom: vars.$spacing-lg;
  margin-bottom: vars.$spacing-lg;
}

.timelineItem::before {
  content: '';
  position: absolute;
  left: -23px;
  top: 8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: vars.$primary-color;
  border: 3px solid white;
  box-shadow: 0 0 0 2px vars.$primary-color;
}

.timelineContent {
  background: white;
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-md;
  box-shadow: vars.$shadow-sm;
}

.timelineTitle {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-xs 0;
}

.timelineTime {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .recordDetail {
    padding: vars.$spacing-md;
  }

  .header {
    padding: vars.$spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-md;
  }

  .deviceIcon {
    font-size: 3rem;
  }

  .deviceName {
    font-size: vars.$font-size-lg;
  }

  .headerMeta {
    justify-content: center;
  }

  .section {
    padding: vars.$spacing-lg;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .imageGrid {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .actions {
    flex-direction: column;
    padding: vars.$spacing-md 0;
  }

  .actionButton {
    width: 100%;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) and (max-width: vars.$tablet-large - 1px) {
  .recordDetail {
    padding: vars.$spacing-xl;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .imageGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Animation for sections
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: slideUp 0.3s ease-out;
}

.header {
  animation: slideUp 0.3s ease-out;
}