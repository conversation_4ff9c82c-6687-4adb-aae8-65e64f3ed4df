import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout } from '../../components/navigation';
import { <PERSON><PERSON>, LoadingSpinner, Modal } from '../../components/common';
import { ImageCarousel } from '../../components/media';
import { useAuth, useToast } from '../../hooks';
import { deviceService } from '../../services';
import { BindingRecord, DeviceBinding } from '../../types';
import styles from './BindingRecordDetail.module.scss';

export const BindingRecordDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();

  const [record, setRecord] = useState<DeviceBinding | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageType, setSelectedImageType] = useState<'nameplate' | 'whole' | 'gps'>('nameplate');

  // Load record details
  useEffect(() => {
    const loadRecordDetail = async () => {
      if (!id) {
        setError('记录ID不存在');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // For now, we'll simulate getting detailed record data
        // In a real implementation, this would be an API call to get full record details
        const response = await deviceService.getBindingRecordDetail(id);
        setRecord(response.data);
      } catch (error) {
        console.error('Failed to load record detail:', error);
        setError('加载记录详情失败，请重试');
        showToast({
          type: 'error',
          title: '加载失败',
          message: '无法加载记录详情，请检查网络连接',
        });
      } finally {
        setLoading(false);
      }
    };

    loadRecordDetail();
  }, [id, showToast]);

  // Handle image view
  const handleViewImages = (type: 'nameplate' | 'whole' | 'gps') => {
    setSelectedImageType(type);
    setShowImageModal(true);
  };

  // Get status display
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'confirmed':
        return { text: '已确认', className: styles.confirmed };
      case 'rejected':
        return { text: '已拒绝', className: styles.rejected };
      case 'pending':
      default:
        return { text: '待审核', className: styles.pending };
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  const navProps = {
    title: '绑定详情',
    showBack: true,
    onBack: () => navigate('/binding-records'),
  };

  if (loading) {
    return (
      <Layout navProps={navProps}>
        <div className={styles.loadingContainer}>
          <LoadingSpinner size="large" text="正在加载记录详情..." />
        </div>
      </Layout>
    );
  }

  if (error || !record) {
    return (
      <Layout navProps={navProps}>
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>⚠️</div>
          <h3 className={styles.errorTitle}>加载失败</h3>
          <p className={styles.errorMessage}>{error || '记录不存在'}</p>
          <div className={styles.errorActions}>
            <Button
              variant="outline"
              onClick={() => navigate('/binding-records')}
            >
              返回列表
            </Button>
            <Button
              variant="primary"
              onClick={() => window.location.reload()}
            >
              重新加载
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  const statusInfo = getStatusDisplay(record.status);
  const currentImages = record[`${selectedImageType}Photos`] || [];

  return (
    <Layout navProps={navProps}>
      <div className={styles.recordDetail}>
        {/* Header Section */}
        <div className={styles.header}>
          <div className={styles.deviceIcon}>📱</div>
          <div className={styles.headerContent}>
            <h2 className={styles.deviceName}>{record.customerName || '设备'}</h2>
            <div className={styles.headerMeta}>
              <span className={styles.recordId}>ID: {record.id}</span>
              <span className={`${styles.status} ${statusInfo.className}`}>
                {statusInfo.text}
              </span>
            </div>
          </div>
        </div>

        {/* Basic Information */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>基本信息</h3>
          <div className={styles.infoGrid}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>QR码</span>
              <span className={styles.infoValue}>{record.qrCode}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>品牌</span>
              <span className={styles.infoValue}>{record.brand}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>序列号</span>
              <span className={styles.infoValue}>{record.serialNumber}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>绑定人员</span>
              <span className={styles.infoValue}>{record.engineer}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>绑定时间</span>
              <span className={styles.infoValue}>{formatDate(record.createdAt)}</span>
            </div>
            {record.customerName && (
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>客户名称</span>
                <span className={styles.infoValue}>{record.customerName}</span>
              </div>
            )}
          </div>
        </div>

        {/* Images Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>设备照片</h3>
          <div className={styles.imageGrid}>
            {/* Nameplate Photos */}
            <div className={styles.imageCategory}>
              <h4 className={styles.imageCategoryTitle}>铭牌照片</h4>
              <div className={styles.imagePreview}>
                {record.nameplatePhotos.length > 0 ? (
                  <>
                    <img
                      src={record.nameplatePhotos[0]}
                      alt="铭牌照片"
                      className={styles.previewImage}
                      onClick={() => handleViewImages('nameplate')}
                    />
                    {record.nameplatePhotos.length > 1 && (
                      <div className={styles.imageCount}>
                        +{record.nameplatePhotos.length - 1}
                      </div>
                    )}
                  </>
                ) : (
                  <div className={styles.noImage}>暂无照片</div>
                )}
              </div>
              {record.nameplatePhotos.length > 0 && (
                <Button
                  variant="outline"
                  size="small"
                  onClick={() => handleViewImages('nameplate')}
                  className={styles.viewButton}
                >
                  查看 ({record.nameplatePhotos.length})
                </Button>
              )}
            </div>

            {/* Whole Machine Photos */}
            <div className={styles.imageCategory}>
              <h4 className={styles.imageCategoryTitle}>整机照片</h4>
              <div className={styles.imagePreview}>
                {record.wholePhotos.length > 0 ? (
                  <>
                    <img
                      src={record.wholePhotos[0]}
                      alt="整机照片"
                      className={styles.previewImage}
                      onClick={() => handleViewImages('whole')}
                    />
                    {record.wholePhotos.length > 1 && (
                      <div className={styles.imageCount}>
                        +{record.wholePhotos.length - 1}
                      </div>
                    )}
                  </>
                ) : (
                  <div className={styles.noImage}>暂无照片</div>
                )}
              </div>
              {record.wholePhotos.length > 0 && (
                <Button
                  variant="outline"
                  size="small"
                  onClick={() => handleViewImages('whole')}
                  className={styles.viewButton}
                >
                  查看 ({record.wholePhotos.length})
                </Button>
              )}
            </div>

            {/* GPS Photos */}
            <div className={styles.imageCategory}>
              <h4 className={styles.imageCategoryTitle}>GPS照片</h4>
              <div className={styles.imagePreview}>
                {record.gpsPhotos.length > 0 ? (
                  <>
                    <img
                      src={record.gpsPhotos[0]}
                      alt="GPS照片"
                      className={styles.previewImage}
                      onClick={() => handleViewImages('gps')}
                    />
                    {record.gpsPhotos.length > 1 && (
                      <div className={styles.imageCount}>
                        +{record.gpsPhotos.length - 1}
                      </div>
                    )}
                  </>
                ) : (
                  <div className={styles.noImage}>暂无照片</div>
                )}
              </div>
              {record.gpsPhotos.length > 0 && (
                <Button
                  variant="outline"
                  size="small"
                  onClick={() => handleViewImages('gps')}
                  className={styles.viewButton}
                >
                  查看 ({record.gpsPhotos.length})
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Actions Section */}
        <div className={styles.actions}>
          <Button
            variant="outline"
            onClick={() => navigate('/binding-records')}
            className={styles.actionButton}
          >
            返回列表
          </Button>
          {record.status === 'pending' && (
            <Button
              variant="primary"
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '功能开发中',
                  message: '编辑功能正在开发中',
                });
              }}
              className={styles.actionButton}
            >
              编辑记录
            </Button>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && currentImages.length > 0 && (
        <Modal
          isOpen={showImageModal}
          onClose={() => setShowImageModal(false)}
          title={`${selectedImageType === 'nameplate' ? '铭牌' : selectedImageType === 'whole' ? '整机' : 'GPS'}照片`}
          size="lg"
        >
          <div className={styles.imageModal}>
            <ImageCarousel
              items={currentImages.map(url => ({ url, alt: '设备照片' }))}
              showDots={true}
              autoPlay={false}
            />
          </div>
        </Modal>
      )}
    </Layout>
  );
};