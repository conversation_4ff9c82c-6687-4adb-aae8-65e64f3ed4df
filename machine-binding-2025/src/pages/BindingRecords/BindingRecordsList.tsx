import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout } from '../../components/navigation';
import { SearchBar, FormField } from '../../components/forms';
import { <PERSON><PERSON>, LoadingSpinner, EmptyState, ErrorEmptyState } from '../../components/common';
import { useAuth, useToast, useBindingRecords } from '../../hooks';
import { deviceService } from '../../services';
import { BindingRecord } from '../../types';
import styles from './BindingRecordsList.module.scss';

export const BindingRecordsList: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();

  // Use the custom hook for records management
  const {
    records: currentRecords,
    loading,
    error,
    searchFilters,
    paginationConfig,
    statistics,
    handleSearch,
    updateSearchFilters,
    refreshRecords,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    clearFilters,
  } = useBindingRecords(10);

  // Handle filter changes
  const handleFilterChange = useCallback((key: string, value: string) => {
    updateSearchFilters({ [key]: value });
  }, [updateSearchFilters]);

  // Handle record click
  const handleRecordClick = useCallback((record: BindingRecord) => {
    // Navigate to record detail page (to be implemented)
    navigate(`/binding-records/${record.id}`);
  }, [navigate]);

  // Get status display
  const getStatusDisplay = (record: BindingRecord) => {
    // Since we don't have status in the current data structure,
    // we'll show a default status
    return {
      status: 'pending' as const,
      text: '待审核',
    };
  };

  const navProps = {
    title: '绑定记录',
    showQRButton: true,
    onQRScan: () => navigate('/device-binding'),
  };

  return (
    <Layout navProps={navProps} contentPadding="none">
      <div className={styles.recordsList}>
        {/* Search Section */}
        <div className={styles.searchSection}>
          <h3 className={styles.searchTitle}>搜索记录</h3>
          
          <SearchBar
            value={searchFilters.query}
            placeholder="搜索设备名称或编号..."
            onSearch={handleSearch}
            onChange={handleSearch}
            loading={loading}
          />

          {/* Filters */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
            gap: '16px', 
            marginTop: '16px' 
          }}>
            <FormField label="时间范围">
              <select
                value={searchFilters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '2px solid #ddd',
                  borderRadius: '8px',
                  fontSize: '14px',
                }}
              >
                <option value="all">全部时间</option>
                <option value="today">今天</option>
                <option value="week">最近一周</option>
                <option value="month">最近一月</option>
              </select>
            </FormField>
          </div>
        </div>

        {/* Records Container */}
        <div className={styles.recordsContainer}>
          <div className={styles.recordsHeader}>
            <h3 className={styles.recordsTitle}>绑定记录</h3>
            <div className={styles.recordsCount}>
              共 {statistics.filtered} 条记录
              {statistics.hasFilters && statistics.total !== statistics.filtered && (
                <span style={{ marginLeft: '8px', opacity: 0.7 }}>
                  (总共 {statistics.total} 条)
                </span>
              )}
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className={styles.loadingContainer}>
              <LoadingSpinner size="large" text="正在加载绑定记录..." />
            </div>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className={styles.errorContainer}>
              <div className={styles.errorIcon}>⚠️</div>
              <h4 className={styles.errorTitle}>加载失败</h4>
              <p className={styles.errorMessage}>{error}</p>
              <Button
                variant="primary"
                onClick={refreshRecords}
                className={styles.retryButton}
              >
                重新加载
              </Button>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && statistics.filtered === 0 && (
            <div className={styles.emptyContainer}>
              {searchFilters.query ? (
                <>
                  <div className={styles.emptyIcon}>🔍</div>
                  <h4 className={styles.emptyTitle}>未找到匹配记录</h4>
                  <p className={styles.emptyDescription}>
                    没有找到与 "{searchFilters.query}" 相关的绑定记录
                  </p>
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                  >
                    清除搜索
                  </Button>
                </>
              ) : (
                <>
                  <div className={styles.emptyIcon}>📋</div>
                  <h4 className={styles.emptyTitle}>暂无绑定记录</h4>
                  <p className={styles.emptyDescription}>
                    您还没有任何设备绑定记录，快去绑定您的第一台设备吧！
                  </p>
                  <Button
                    variant="primary"
                    onClick={() => navigate('/device-binding')}
                  >
                    绑定设备
                  </Button>
                </>
              )}
            </div>
          )}

          {/* Records List */}
          {!loading && !error && currentRecords.length > 0 && (
            <>
              <div className={styles.recordsList}>
                {currentRecords.map((record) => {
                  const statusInfo = getStatusDisplay(record);
                  
                  return (
                    <div
                      key={record.id}
                      className={styles.recordItem}
                      onClick={() => handleRecordClick(record)}
                    >
                      <div className={styles.recordIcon}>
                        📱
                      </div>

                      <div className={styles.recordContent}>
                        <div className={styles.recordHeader}>
                          <h4 className={styles.recordName}>{record.name}</h4>
                          <span className={styles.recordDate}>{record.time}</span>
                        </div>

                        <div className={styles.recordDetails}>
                          <span className={styles.recordCode}>{record.code}</span>
                          <span className={`${styles.recordStatus} ${styles[statusInfo.status]}`}>
                            {statusInfo.text}
                          </span>
                          {record.customer_name && (
                            <span>客户: {record.customer_name}</span>
                          )}
                        </div>
                      </div>

                      <div className={styles.recordActions}>
                        <button
                          className={styles.actionButton}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRecordClick(record);
                          }}
                          aria-label="查看详情"
                        >
                          👁️
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Pagination */}
              {paginationConfig.totalPages > 1 && (
                <div className={styles.pagination}>
                  <button
                    className={styles.paginationButton}
                    onClick={goToPreviousPage}
                    disabled={paginationConfig.currentPage === 1}
                  >
                    上一页
                  </button>

                  {Array.from({ length: paginationConfig.totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      className={`${styles.paginationButton} ${
                        page === paginationConfig.currentPage ? styles.active : ''
                      }`}
                      onClick={() => goToPage(page)}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    className={styles.paginationButton}
                    onClick={goToNextPage}
                    disabled={paginationConfig.currentPage === paginationConfig.totalPages}
                  >
                    下一页
                  </button>

                  <div className={styles.paginationInfo}>
                    第 {paginationConfig.currentPage} 页，共 {paginationConfig.totalPages} 页
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Layout>
  );
};