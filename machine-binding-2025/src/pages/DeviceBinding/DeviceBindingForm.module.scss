@import '../../styles/variables.scss';

.bindingForm {
  max-width: 600px;
  margin: 0 auto;
  padding: $spacing-lg;
}

.formSection {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
}

.sectionTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-lg 0;
  padding-bottom: $spacing-sm;
  border-bottom: 2px solid $background-secondary;
}

.formGrid {
  display: grid;
  gap: $spacing-lg;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
}

.formRow.twoColumns {
  grid-template-columns: 1fr 1fr;
}

.qrCodeDisplay {
  background: $background-secondary;
  border: 2px dashed #ddd;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  text-align: center;
  margin-bottom: $spacing-lg;
}

.qrCodeText {
  font-family: 'Courier New', monospace;
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0 0 $spacing-sm 0;
  word-break: break-all;
}

.qrCodeLabel {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0;
}

.scanButton {
  margin-top: $spacing-md;
  width: 100%;
}

.brandHint {
  background: rgba($warning-color, 0.1);
  border: 1px solid rgba($warning-color, 0.3);
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-top: $spacing-sm;
  font-size: $font-size-sm;
  color: $text-primary;
}

.hintIcon {
  color: $warning-color;
  margin-right: $spacing-xs;
}

.submitSection {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-sm;
  position: sticky;
  bottom: $spacing-lg;
  z-index: $z-index-sticky;
}

.submitButton {
  width: 100%;
  min-height: 52px;
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
}

.formProgress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  padding: $spacing-md;
  background: $background-secondary;
  border-radius: $border-radius-md;
}

.progressStep {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-sm;
  color: $text-muted;
}

.progressStep.active {
  color: $primary-color;
  font-weight: $font-weight-medium;
}

.progressStep.completed {
  color: $success-color;
}

.stepIcon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: $font-weight-bold;
  background: $background-primary;
  border: 2px solid currentColor;
}

.progressStep.active .stepIcon {
  background: $primary-color;
  color: white;
}

.progressStep.completed .stepIcon {
  background: $success-color;
  color: white;
}

.validationSummary {
  background: rgba($danger-color, 0.1);
  border: 1px solid rgba($danger-color, 0.3);
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
}

.validationTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $danger-color;
  margin: 0 0 $spacing-sm 0;
}

.validationList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validationItem {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-sm;
  color: $danger-color;
  margin-bottom: $spacing-xs;
}

.validationItem:last-child {
  margin-bottom: 0;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .bindingForm {
    padding: $spacing-md;
  }

  .formSection {
    padding: $spacing-md;
    margin-bottom: $spacing-md;
  }

  .sectionTitle {
    font-size: $font-size-md;
  }

  .formRow.twoColumns {
    grid-template-columns: 1fr;
  }

  .qrCodeText {
    font-size: $font-size-md;
  }

  .submitSection {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0;
    padding: $spacing-md;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  .submitButton {
    min-height: $touch-target-min;
  }

  // Add bottom padding to prevent content from being hidden behind fixed submit button
  .bindingForm {
    padding-bottom: 100px;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .bindingForm {
    padding: $spacing-xl;
  }

  .formSection {
    padding: $spacing-xl;
  }
}

// Loading state
.bindingForm.loading {
  opacity: 0.7;
  pointer-events: none;
}

// Success state
.bindingForm.success {
  .formSection {
    border: 2px solid $success-color;
    background: rgba($success-color, 0.02);
  }
}

// Error state
.bindingForm.error {
  .formSection {
    border: 2px solid $danger-color;
    background: rgba($danger-color, 0.02);
  }
}