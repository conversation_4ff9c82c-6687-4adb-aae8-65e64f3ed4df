@use "../../../styles/variables" as vars;

.bindingForm {
  max-width: 600px;
  margin: 0 auto;
  padding: vars.$spacing-lg;
}

.formSection {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-lg;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
}

.sectionTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-lg 0;
  padding-bottom: vars.$spacing-sm;
  border-bottom: 2px solid vars.$background-secondary;
}

.formGrid {
  display: grid;
  gap: vars.$spacing-lg;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr;
  gap: vars.$spacing-md;
}

.formRow.twoColumns {
  grid-template-columns: 1fr 1fr;
}

.qrCodeDisplay {
  background: vars.$background-secondary;
  border: 2px dashed #ddd;
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-lg;
  text-align: center;
  margin-bottom: vars.$spacing-lg;
}

.qrCodeText {
  font-family: 'Courier New', monospace;
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-bold;
  color: vars.$primary-color;
  margin: 0 0 vars.$spacing-sm 0;
  word-break: break-all;
}

.qrCodeLabel {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0;
}

.scanButton {
  margin-top: vars.$spacing-md;
  width: 100%;
}

.brandHint {
  background: rgba(vars.$warning-color, 0.1);
  border: 1px solid rgba(vars.$warning-color, 0.3);
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-md;
  margin-top: vars.$spacing-sm;
  font-size: vars.$font-size-sm;
  color: vars.$text-primary;
}

.hintIcon {
  color: vars.$warning-color;
  margin-right: vars.$spacing-xs;
}

.submitSection {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
  position: sticky;
  bottom: vars.$spacing-lg;
  z-index: vars.$z-index-sticky;
}

.submitButton {
  width: 100%;
  min-height: 52px;
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
}

.formProgress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: vars.$spacing-lg;
  padding: vars.$spacing-md;
  background: vars.$background-secondary;
  border-radius: vars.$border-radius-md;
}

.progressStep {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
}

.progressStep.active {
  color: vars.$primary-color;
  font-weight: vars.$font-weight-medium;
}

.progressStep.completed {
  color: vars.$success-color;
}

.stepIcon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: vars.$font-weight-bold;
  background: vars.$background-primary;
  border: 2px solid currentColor;
}

.progressStep.active .stepIcon {
  background: vars.$primary-color;
  color: white;
}

.progressStep.completed .stepIcon {
  background: vars.$success-color;
  color: white;
}

.validationSummary {
  background: rgba(vars.$danger-color, 0.1);
  border: 1px solid rgba(vars.$danger-color, 0.3);
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-md;
  margin-bottom: vars.$spacing-lg;
}

.validationTitle {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
  color: vars.$danger-color;
  margin: 0 0 vars.$spacing-sm 0;
}

.validationList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validationItem {
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
  font-size: vars.$font-size-sm;
  color: vars.$danger-color;
  margin-bottom: vars.$spacing-xs;
}

.validationItem:last-child {
  margin-bottom: 0;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .bindingForm {
    padding: vars.$spacing-md;
  }

  .formSection {
    padding: vars.$spacing-md;
    margin-bottom: vars.$spacing-md;
  }

  .sectionTitle {
    font-size: vars.$font-size-md;
  }

  .formRow.twoColumns {
    grid-template-columns: 1fr;
  }

  .qrCodeText {
    font-size: vars.$font-size-md;
  }

  .submitSection {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0;
    padding: vars.$spacing-md;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  .submitButton {
    min-height: vars.$touch-target-min;
  }

  // Add bottom padding to prevent content from being hidden behind fixed submit button
  .bindingForm {
    padding-bottom: 100px;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) and (max-width: vars.$tablet-large - 1px) {
  .bindingForm {
    padding: vars.$spacing-xl;
  }

  .formSection {
    padding: vars.$spacing-xl;
  }
}

// Loading state
.bindingForm.loading {
  opacity: 0.7;
  pointer-events: none;
}

// Success state
.bindingForm.success {
  .formSection {
    border: 2px solid vars.$success-color;
    background: rgba(vars.$success-color, 0.02);
  }
}

// Error state
.bindingForm.error {
  .formSection {
    border: 2px solid vars.$danger-color;
    background: rgba(vars.$danger-color, 0.02);
  }
}