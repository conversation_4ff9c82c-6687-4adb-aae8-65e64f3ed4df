import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout } from '../../components/navigation';
import { Button, Input, LoadingSpinner } from '../../components/common';
import { FormField } from '../../components/forms';
import { QRScanner, ImageUpload } from '../../components/media';
import { useAuth, useToast, useQRScanner, useDeviceBinding } from '../../hooks';
import { deviceService } from '../../services';
import { DeviceBindingFormData } from '../../types';
import { qrCodeUtils } from '../../utils';
import styles from './DeviceBindingForm.module.scss';

interface FormErrors {
  qrCode?: string;
  brand?: string;
  serialNumber?: string;
  general?: string;
}

interface FormProgress {
  qrCode: boolean;
  deviceInfo: boolean;
  photos: boolean;
}

export const DeviceBindingForm: React.FC = () => {
  const { qrCode: urlQrCode } = useParams<{ qrCode: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const { handleQRScan, handleQRError, formatQRCodeForDisplay } = useQRScanner();
  const { 
    isSubmitting: isBindingSubmitting, 
    isUploading, 
    uploadProgress, 
    processDeviceBinding, 
    validateFormForSubmission 
  } = useDeviceBinding();

  // Form state
  const [formData, setFormData] = useState<DeviceBindingFormData>({
    qrCode: '',
    brand: '',
    serialNumber: '',
    nameplatePhotos: [],
    wholePhotos: [],
    gpsPhotos: [],
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const isSubmitting = isBindingSubmitting;
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [progress, setProgress] = useState<FormProgress>({
    qrCode: false,
    deviceInfo: false,
    photos: false,
  });

  // Initialize form with QR code from URL
  useEffect(() => {
    if (urlQrCode) {
      const decodedQrCode = decodeURIComponent(urlQrCode);
      setFormData(prev => ({ ...prev, qrCode: decodedQrCode }));
      setProgress(prev => ({ ...prev, qrCode: true }));
      validateQRCode(decodedQrCode);
    }
  }, [urlQrCode]);

  // Validate QR code
  const validateQRCode = useCallback(async (qrCode: string) => {
    if (!qrCode) return;

    const validation = qrCodeUtils.validateBusinessRules(qrCode);
    if (!validation.isValid) {
      setErrors(prev => ({ ...prev, qrCode: validation.errorMessage }));
      return;
    }

    try {
      const response = await deviceService.validateQRCode(qrCode);
      if (response.data.detail) {
        setErrors(prev => ({ ...prev, qrCode: '该二维码不存在或已失效' }));
      } else {
        setErrors(prev => ({ ...prev, qrCode: undefined }));
        setProgress(prev => ({ ...prev, qrCode: true }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, qrCode: '二维码验证失败，请重试' }));
    }
  }, []);

  // Handle form field changes
  const handleFieldChange = (field: keyof DeviceBindingFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }

    // Update progress
    updateProgress(field, value);
  };

  // Update form progress
  const updateProgress = (field: keyof DeviceBindingFormData, value: any) => {
    switch (field) {
      case 'qrCode':
        setProgress(prev => ({ ...prev, qrCode: Boolean(value) }));
        break;
      case 'brand':
      case 'serialNumber':
        setProgress(prev => ({ 
          ...prev, 
          deviceInfo: Boolean(formData.brand && formData.serialNumber) 
        }));
        break;
      case 'nameplatePhotos':
      case 'wholePhotos':
      case 'gpsPhotos':
        // Use updated formData for progress calculation
        const updatedFormData = { ...formData, [field]: value };
        setProgress(prev => ({ 
          ...prev, 
          photos: Boolean(
            updatedFormData.nameplatePhotos.length > 0 && 
            updatedFormData.wholePhotos.length > 0 && 
            updatedFormData.gpsPhotos.length > 0
          )
        }));
        break;
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // QR Code validation
    if (!formData.qrCode) {
      newErrors.qrCode = '请扫描或输入二维码';
    } else {
      const qrValidation = qrCodeUtils.validateBusinessRules(formData.qrCode);
      if (!qrValidation.isValid) {
        newErrors.qrCode = qrValidation.errorMessage;
      }
    }

    // Brand validation
    if (!formData.brand) {
      newErrors.brand = '请输入设备品牌';
    } else {
      const brandValidation = deviceService.validateBrandName(formData.brand);
      if (!brandValidation.isValid) {
        newErrors.brand = brandValidation.error;
      }
    }

    // Serial number validation
    if (!formData.serialNumber) {
      newErrors.serialNumber = '请输入机身编号';
    } else {
      const serialValidation = deviceService.validateSerialNumber(formData.serialNumber);
      if (!serialValidation.isValid) {
        newErrors.serialNumber = serialValidation.error;
      }
    }

    // Photo validation
    if (formData.nameplatePhotos.length === 0) {
      newErrors.general = '请上传机台铁牌照片';
    } else if (formData.wholePhotos.length === 0) {
      newErrors.general = '请上传机台全身照片';
    } else if (formData.gpsPhotos.length === 0) {
      newErrors.general = '请上传GPS照片';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle QR scan success
  const handleQRScanSuccess = (scannedCode: string) => {
    setFormData(prev => ({ ...prev, qrCode: scannedCode }));
    setShowQRScanner(false);
    validateQRCode(scannedCode);
    showToast({
      type: 'success',
      title: '扫描成功',
      message: '二维码已识别',
    });
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    // Validate form using the hook
    const validationErrors = validateFormForSubmission(formData);
    if (validationErrors.length > 0) {
      showToast({
        type: 'error',
        title: '表单验证失败',
        message: validationErrors[0],
      });
      return;
    }

    // Process device binding using the hook
    await processDeviceBinding(formData);
  };

  // Calculate form completion percentage
  const completionPercentage = Math.round(
    (Object.values(progress).filter(Boolean).length / Object.keys(progress).length) * 100
  );

  const navProps = {
    title: '绑定设备',
    showQRButton: true,
    onQRScan: () => setShowQRScanner(true),
  };

  return (
    <Layout navProps={navProps} contentPadding="none">
      <div className={`${styles.bindingForm} ${isSubmitting ? styles.loading : ''}`}>
        {/* Form Progress */}
        <div className={styles.formProgress}>
          <div className={`${styles.progressStep} ${progress.qrCode ? styles.completed : styles.active}`}>
            <div className={styles.stepIcon}>
              {progress.qrCode ? '✓' : '1'}
            </div>
            <span>扫描二维码</span>
          </div>
          <div className={`${styles.progressStep} ${progress.deviceInfo ? styles.completed : progress.qrCode ? styles.active : ''}`}>
            <div className={styles.stepIcon}>
              {progress.deviceInfo ? '✓' : '2'}
            </div>
            <span>设备信息</span>
          </div>
          <div className={`${styles.progressStep} ${progress.photos ? styles.completed : progress.deviceInfo ? styles.active : ''}`}>
            <div className={styles.stepIcon}>
              {progress.photos ? '✓' : '3'}
            </div>
            <span>上传照片</span>
          </div>
        </div>

        {/* Validation Summary */}
        {Object.keys(errors).length > 0 && (
          <div className={styles.validationSummary}>
            <h4 className={styles.validationTitle}>请完善以下信息：</h4>
            <ul className={styles.validationList}>
              {Object.entries(errors).map(([field, error]) => (
                <li key={field} className={styles.validationItem}>
                  <span>⚠️</span>
                  <span>{error}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          {/* QR Code Section */}
          <div className={styles.formSection}>
            <h3 className={styles.sectionTitle}>1. 二维码信息</h3>
            
            {formData.qrCode ? (
              <div className={styles.qrCodeDisplay}>
                <div className={styles.qrCodeText}>
                  {formatQRCodeForDisplay(formData.qrCode)}
                </div>
                <div className={styles.qrCodeLabel}>
                  二维码后20位（完整码已保存）
                </div>
                <Button
                  variant="outline"
                  onClick={() => setShowQRScanner(true)}
                  className={styles.scanButton}
                >
                  重新扫描
                </Button>
              </div>
            ) : (
              <div className={styles.qrCodeDisplay}>
                <div style={{ marginBottom: '16px', color: '#666' }}>
                  请扫描设备上的二维码
                </div>
                <Button
                  variant="primary"
                  onClick={() => setShowQRScanner(true)}
                  className={styles.scanButton}
                >
                  📱 扫描二维码
                </Button>
              </div>
            )}

            {errors.qrCode && (
              <div style={{ color: '#dc3545', fontSize: '14px', marginTop: '8px' }}>
                ⚠️ {errors.qrCode}
              </div>
            )}
          </div>

          {/* Device Information Section */}
          <div className={styles.formSection}>
            <h3 className={styles.sectionTitle}>2. 设备信息</h3>
            
            <div className={styles.formGrid}>
              <div className={styles.formRow}>
                <FormField
                  label="设备品牌"
                  required
                  error={errors.brand}
                  helpText="请输入英文大写字母，例如：BROTHER"
                >
                  <Input
                    name="brand"
                    value={formData.brand}
                    placeholder="请输入设备品牌"
                    onChange={(e) => handleFieldChange('brand', e.target.value.toUpperCase())}
                    disabled={isSubmitting}
                    style={{ textTransform: 'uppercase' }}
                  />
                </FormField>
                
                {formData.brand && (
                  <div className={styles.brandHint}>
                    <span className={styles.hintIcon}>💡</span>
                    品牌输入提示：必须英文大写，例如：BROTHER
                  </div>
                )}
              </div>

              <div className={styles.formRow}>
                <FormField
                  label="机身编号"
                  required
                  error={errors.serialNumber}
                >
                  <Input
                    name="serialNumber"
                    value={formData.serialNumber}
                    placeholder="请输入机身编号"
                    onChange={(e) => handleFieldChange('serialNumber', e.target.value)}
                    disabled={isSubmitting}
                  />
                </FormField>
              </div>

              <div className={styles.formRow}>
                <FormField label="绑定人">
                  <Input
                    name="engineer"
                    value={user?.name || ''}
                    disabled
                    style={{ backgroundColor: '#f8f9fa' }}
                  />
                </FormField>
              </div>
            </div>
          </div>

          {/* Photo Upload Section */}
          <div className={styles.formSection}>
            <h3 className={styles.sectionTitle}>3. 设备照片</h3>
            
            <div className={styles.formGrid}>
              <FormField
                label="机台铁牌照片"
                required
                helpText="请拍摄清晰的设备铭牌照片，确保文字可读"
              >
                <ImageUpload
                  maxFiles={3}
                  value={formData.nameplatePhotos}
                  onUpload={(files) => {
                    const newFiles = [...formData.nameplatePhotos, ...files];
                    handleFieldChange('nameplatePhotos', newFiles);
                  }}
                  onRemove={(index) => {
                    const newFiles = formData.nameplatePhotos.filter((_, i) => i !== index);
                    handleFieldChange('nameplatePhotos', newFiles);
                  }}
                  placeholder="上传机台铁牌照片"
                  hint="支持 JPG、PNG 格式，最多3张"
                  disabled={isSubmitting}
                />
              </FormField>

              <FormField
                label="机台全身照片"
                required
                helpText="请拍摄设备全貌，包含设备整体外观"
              >
                <ImageUpload
                  maxFiles={3}
                  value={formData.wholePhotos}
                  onUpload={(files) => {
                    const newFiles = [...formData.wholePhotos, ...files];
                    handleFieldChange('wholePhotos', newFiles);
                  }}
                  onRemove={(index) => {
                    const newFiles = formData.wholePhotos.filter((_, i) => i !== index);
                    handleFieldChange('wholePhotos', newFiles);
                  }}
                  placeholder="上传机台全身照片"
                  hint="支持 JPG、PNG 格式，最多3张"
                  disabled={isSubmitting}
                />
              </FormField>

              <FormField
                label="GPS定位照片"
                required
                helpText="请拍摄包含GPS定位信息的照片，用于确认设备位置"
              >
                <ImageUpload
                  maxFiles={3}
                  value={formData.gpsPhotos}
                  onUpload={(files) => {
                    const newFiles = [...formData.gpsPhotos, ...files];
                    handleFieldChange('gpsPhotos', newFiles);
                  }}
                  onRemove={(index) => {
                    const newFiles = formData.gpsPhotos.filter((_, i) => i !== index);
                    handleFieldChange('gpsPhotos', newFiles);
                  }}
                  placeholder="上传GPS定位照片"
                  hint="支持 JPG、PNG 格式，最多3张"
                  disabled={isSubmitting}
                />
              </FormField>
            </div>
          </div>
        </form>

        {/* Submit Section */}
        <div className={styles.submitSection}>
          {/* Upload Progress */}
          {isUploading && (
            <div style={{ marginBottom: '16px' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '8px',
                fontSize: '14px',
                color: '#666'
              }}>
                <span>正在上传照片...</span>
                <span>{uploadProgress}%</span>
              </div>
              <div style={{
                width: '100%',
                height: '8px',
                background: '#eee',
                borderRadius: '4px',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${uploadProgress}%`,
                  height: '100%',
                  background: '#007bff',
                  transition: 'width 0.3s ease',
                  borderRadius: '4px'
                }} />
              </div>
            </div>
          )}

          <div style={{ marginBottom: '12px', fontSize: '14px', color: '#666' }}>
            表单完成度: {completionPercentage}%
          </div>
          <Button
            type="submit"
            variant="primary"
            size="large"
            loading={isSubmitting}
            disabled={isSubmitting || completionPercentage < 100}
            onClick={handleSubmit}
            className={styles.submitButton}
          >
            {isSubmitting ? (isUploading ? '上传中...' : '提交中...') : '提交绑定申请'}
          </Button>
        </div>
      </div>

      {/* QR Scanner Modal */}
      {showQRScanner && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.9)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px',
        }}>
          <div style={{ width: '100%', maxWidth: '400px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '20px',
              color: 'white'
            }}>
              <h3 style={{ margin: 0 }}>扫描二维码</h3>
              <Button
                variant="ghost"
                onClick={() => setShowQRScanner(false)}
                style={{ color: 'white' }}
              >
                ✕
              </Button>
            </div>
            
            <QRScanner
              onScan={handleQRScanSuccess}
              onError={handleQRError}
              autoStart={true}
            />
          </div>
        </div>
      )}
    </Layout>
  );
};