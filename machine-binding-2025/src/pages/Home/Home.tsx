import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout } from '../../components/navigation';
import { ImageCarousel } from '../../components/media';
import { HomeActionGrid, LoadingSpinner, ErrorEmptyState } from '../../components/common';
import { useAuth, useToast } from '../../hooks';
import { deviceService } from '../../services';
import { CarouselItem } from '../../types';

export const Home: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const [carouselItems, setCarouselItems] = useState<CarouselItem[]>([]);
  const [carouselLoading, setCarouselLoading] = useState(true);
  const [carouselError, setCarouselError] = useState<string | null>(null);
  const [bindingRecordsCount, setBindingRecordsCount] = useState<number>(0);

  // Load banner images for carousel
  useEffect(() => {
    const loadBannerImages = async () => {
      try {
        setCarouselLoading(true);
        setCarouselError(null);
        
        const response = await deviceService.getBannerImages();
        const bannerData = response.data;
        
        // Transform banner data to carousel items
        const items: CarouselItem[] = bannerData.map((banner, index) => ({
          id: `banner-${index}`,
          image: `${import.meta.env.VITE_UPLOAD_BASE_URL}/${banner.photo_url}`,
          title: '设备绑定系统',
          description: '高效管理您的工业设备',
        }));

        // If no banners from API, use default images
        if (items.length === 0) {
          const defaultItems: CarouselItem[] = [
            {
              id: 'default-1',
              image: 'https://via.placeholder.com/800x300/667eea/ffffff?text=设备绑定系统',
              title: '欢迎使用设备绑定系统',
              description: '快速、安全、高效的设备管理解决方案',
            },
            {
              id: 'default-2',
              image: 'https://via.placeholder.com/800x300/764ba2/ffffff?text=扫码绑定',
              title: '扫码即可绑定设备',
              description: '支持二维码扫描，操作简单便捷',
            },
            {
              id: 'default-3',
              image: 'https://via.placeholder.com/800x300/f093fb/ffffff?text=数据管理',
              title: '完整的数据记录',
              description: '详细记录设备信息，便于追踪管理',
            },
          ];
          setCarouselItems(defaultItems);
        } else {
          setCarouselItems(items);
        }
      } catch (error) {
        console.error('Failed to load banner images:', error);
        setCarouselError('轮播图加载失败');
        
        // Use fallback images on error
        const fallbackItems: CarouselItem[] = [
          {
            id: 'fallback-1',
            image: 'https://via.placeholder.com/800x300/667eea/ffffff?text=设备绑定系统',
            title: '设备绑定系统',
            description: '工业设备管理平台',
          },
        ];
        setCarouselItems(fallbackItems);
      } finally {
        setCarouselLoading(false);
      }
    };

    loadBannerImages();
  }, []);

  // Load binding records count for badge
  useEffect(() => {
    const loadBindingRecordsCount = async () => {
      try {
        const response = await deviceService.getBindingRecords();
        setBindingRecordsCount(response.data.length);
      } catch (error) {
        console.error('Failed to load binding records count:', error);
        // Don't show error for this, just keep count at 0
      }
    };

    loadBindingRecordsCount();
  }, []);

  const handleDeviceBinding = () => {
    navigate('/device-binding');
  };

  const handleBindingRecords = () => {
    navigate('/binding-records');
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  const handleQRScan = () => {
    navigate('/device-binding');
    showToast({
      type: 'info',
      title: '扫码功能',
      message: '请使用设备绑定页面的扫码功能',
    });
  };

  const handleCarouselSlideClick = (item: CarouselItem, index: number) => {
    console.log('Carousel slide clicked:', item, index);
    // Could navigate to specific pages based on slide
  };

  const navProps = {
    title: '设备绑定系统',
    showBackButton: false,
    showQRButton: true,
    onQRScan: handleQRScan,
  };

  return (
    <Layout navProps={navProps} contentPadding="none">
      <div style={{ padding: '0' }}>
        {/* Welcome Section */}
        <div style={{ 
          padding: '20px', 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          textAlign: 'center'
        }}>
          <h2 style={{ margin: '0 0 8px 0', fontSize: '20px' }}>
            欢迎回来，{user?.name || '用户'}
          </h2>
          <p style={{ margin: '0', fontSize: '14px', opacity: 0.9 }}>
            {user?.department && `${user.department} | `}
            今天也要高效工作哦！
          </p>
        </div>

        {/* Image Carousel */}
        <div style={{ padding: '20px' }}>
          <ImageCarousel
            items={carouselItems}
            loading={carouselLoading}
            error={carouselError}
            autoPlay={true}
            autoPlayInterval={5000}
            onSlideClick={handleCarouselSlideClick}
            testId="home-carousel"
          />
        </div>

        {/* Action Grid */}
        <div style={{ padding: '0 20px 20px' }}>
          <h3 style={{ 
            margin: '0 0 20px 0', 
            fontSize: '18px', 
            color: '#333',
            textAlign: 'center'
          }}>
            功能菜单
          </h3>
          
          <HomeActionGrid
            onDeviceBinding={handleDeviceBinding}
            onBindingRecords={handleBindingRecords}
            onProfile={handleProfile}
            bindingRecordsCount={bindingRecordsCount}
          />
        </div>

        {/* Quick Stats */}
        <div style={{ 
          padding: '20px',
          background: '#f8f9fa',
          margin: '20px',
          borderRadius: '12px',
          textAlign: 'center'
        }}>
          <h4 style={{ margin: '0 0 12px 0', color: '#666', fontSize: '14px' }}>
            今日统计
          </h4>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(3, 1fr)', 
            gap: '16px',
            fontSize: '12px',
            color: '#888'
          }}>
            <div>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#007bff' }}>
                {bindingRecordsCount}
              </div>
              <div>绑定记录</div>
            </div>
            <div>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#28a745' }}>
                0
              </div>
              <div>待处理</div>
            </div>
            <div>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#ffc107' }}>
                0
              </div>
              <div>已完成</div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};