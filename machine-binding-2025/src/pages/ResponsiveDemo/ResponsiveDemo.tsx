import React from 'react';
import ResponsiveTest from '../../components/common/ResponsiveTest/ResponsiveTest';
import { ActionGrid } from '../../components/common';
import styles from './ResponsiveDemo.module.scss';

const ResponsiveDemo: React.FC = () => {
  const demoActions = [
    { id: '1', title: 'Portrait Test', icon: '📱', onClick: () => console.log('Portrait test') },
    { id: '2', title: 'Landscape Test', icon: '📺', onClick: () => console.log('Landscape test') },
    { id: '3', title: 'Phone Test', icon: '📞', onClick: () => console.log('Phone test') },
    { id: '4', title: 'Tablet Test', icon: '📱', onClick: () => console.log('Tablet test') },
    { id: '5', title: 'Touch Test', icon: '👆', onClick: () => console.log('Touch test') },
    { id: '6', title: 'Grid Test', icon: '⚏', onClick: () => console.log('Grid test') },
  ];

  return (
    <div className={styles.responsiveDemo}>
      <div className={styles.header}>
        <h1>Responsive Design Demo</h1>
        <p>This page demonstrates orientation and device-specific adaptations</p>
      </div>

      <div className={styles.section}>
        <h2>Adaptive Action Grid</h2>
        <ActionGrid items={demoActions} columns={3} />
      </div>

      <div className={styles.section}>
        <h2>Device & Orientation Tests</h2>
        <ResponsiveTest />
      </div>

      <div className={styles.section}>
        <h2>Adaptive Content Layout</h2>
        <div className={styles.contentDemo}>
          <div className="adaptive-container">
            <div className="adaptive-grid">
              <div className={styles.contentCard}>
                <h3>Card 1</h3>
                <p>This card adapts to different screen sizes and orientations.</p>
              </div>
              <div className={styles.contentCard}>
                <h3>Card 2</h3>
                <p>Notice how the layout changes between portrait and landscape.</p>
              </div>
              <div className={styles.contentCard}>
                <h3>Card 3</h3>
                <p>The grid system automatically adjusts column count.</p>
              </div>
              <div className={styles.contentCard}>
                <h3>Card 4</h3>
                <p>Touch targets are optimized for each device type.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h2>Form Layout Demo</h2>
        <div className="adaptive-form">
          <div className="form-row">
            <div className="form-group">
              <label>First Name</label>
              <input type="text" placeholder="Enter first name" />
            </div>
            <div className="form-group">
              <label>Last Name</label>
              <input type="text" placeholder="Enter last name" />
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Email</label>
              <input type="email" placeholder="Enter email" />
            </div>
            <div className="form-group">
              <label>Phone</label>
              <input type="tel" placeholder="Enter phone" />
            </div>
          </div>
          <div className="form-actions">
            <button type="button">Cancel</button>
            <button type="submit">Submit</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveDemo;