@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.responsiveDemo {
  @include adaptive-content-area;
  
  @include adaptive-typography;
}

.header {
  text-align: center;
  margin-bottom: $spacing-xl;
  
  h1 {
    color: $primary-color;
    margin-bottom: $spacing-md;
  }
  
  p {
    color: $text-muted;
    font-size: $font-size-lg;
    
    @include mobile-portrait {
      font-size: $font-size-md;
    }
    
    @include mobile-landscape {
      font-size: $font-size-sm;
    }
  }
}

.section {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
  
  @include mobile-portrait {
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    border-radius: $border-radius-md;
  }
  
  @include mobile-landscape {
    padding: $spacing-md;
    margin-bottom: $spacing-sm;
    border-radius: $border-radius-sm;
  }
  
  h2 {
    color: $primary-color;
    margin-bottom: $spacing-lg;
    border-bottom: 2px solid $border-color-light;
    padding-bottom: $spacing-sm;
    
    @include mobile-portrait {
      margin-bottom: $spacing-md;
      font-size: $font-size-lg;
    }
    
    @include mobile-landscape {
      margin-bottom: $spacing-sm;
      font-size: $font-size-md;
    }
  }
}

.contentDemo {
  .contentCard {
    @include adaptive-card-layout;
    background: $background-secondary;
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    padding: $spacing-lg;
    
    h3 {
      color: $text-primary;
      margin-bottom: $spacing-sm;
      font-size: $font-size-md;
      
      @include mobile-landscape {
        font-size: $font-size-sm;
        margin-bottom: $spacing-xs;
      }
    }
    
    p {
      color: $text-muted;
      font-size: $font-size-sm;
      line-height: 1.5;
      margin-bottom: 0;
      
      @include mobile-landscape {
        font-size: $font-size-xs;
        line-height: 1.4;
      }
    }
    
    @include mobile-portrait {
      padding: $spacing-md;
    }
    
    @include mobile-landscape {
      padding: $spacing-sm;
    }
  }
}

// Form styling
.section {
  .form-row {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-lg;
    
    @include mobile-portrait {
      flex-direction: column;
      gap: $spacing-md;
      margin-bottom: $spacing-md;
    }
    
    @include mobile-landscape {
      gap: $spacing-md;
      margin-bottom: $spacing-sm;
    }
  }
  
  .form-group {
    flex: 1;
    
    label {
      display: block;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-xs;
      font-size: $font-size-sm;
      
      @include mobile-landscape {
        font-size: $font-size-xs;
      }
    }
    
    input {
      width: 100%;
      padding: $spacing-md;
      border: 1px solid $border-color;
      border-radius: $border-radius-sm;
      font-size: $font-size-md;
      
      @include mobile-portrait {
        padding: $spacing-lg;
        font-size: 16px; // Prevent zoom on iOS
        min-height: $touch-target-comfortable;
      }
      
      @include mobile-landscape {
        padding: $spacing-sm;
        font-size: 16px; // Prevent zoom on iOS
        min-height: $touch-target-min;
      }
      
      &:focus {
        outline: 2px solid $primary-color;
        outline-offset: 2px;
        border-color: $primary-color;
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: $spacing-md;
    justify-content: flex-end;
    
    @include mobile-portrait {
      flex-direction: column;
      gap: $spacing-sm;
    }
    
    @include mobile-landscape {
      gap: $spacing-sm;
    }
    
    button {
      padding: $spacing-md $spacing-lg;
      border: 1px solid $border-color;
      border-radius: $border-radius-sm;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      cursor: pointer;
      transition: all 0.3s ease;
      
      @include mobile-portrait {
        width: 100%;
        padding: $spacing-lg;
        min-height: $touch-target-comfortable;
      }
      
      @include mobile-landscape {
        padding: $spacing-sm $spacing-md;
        min-height: $touch-target-min;
        font-size: $font-size-sm;
      }
      
      &[type="submit"] {
        background: $primary-color;
        color: white;
        border-color: $primary-color;
        
        &:hover {
          background: darken($primary-color, 10%);
        }
      }
      
      &[type="button"] {
        background: white;
        color: $text-primary;
        
        &:hover {
          background: $background-secondary;
        }
      }
      
      &:focus {
        outline: 2px solid $primary-color;
        outline-offset: 2px;
      }
      
      @include touch-device {
        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}

// Responsive indicators for testing
.responsiveDemo {
  position: relative;
  
  &::before {
    content: 'Responsive Demo Active';
    position: fixed;
    bottom: 10px;
    left: 10px;
    background: rgba($primary-color, 0.9);
    color: white;
    padding: $spacing-xs $spacing-sm;
    border-radius: $border-radius-sm;
    font-size: $font-size-xs;
    z-index: $z-index-tooltip;
    
    @include mobile-portrait {
      bottom: calc(80px + env(safe-area-inset-bottom) + 10px);
    }
    
    @include mobile-landscape {
      bottom: 10px;
      left: 10px;
    }
  }
}

// Animation for layout changes
.contentCard,
.section {
  transition: all 0.3s ease;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .contentCard,
  .section,
  button {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .contentCard,
  .section {
    border-width: 2px;
  }
  
  input,
  button {
    border-width: 2px;
  }
}