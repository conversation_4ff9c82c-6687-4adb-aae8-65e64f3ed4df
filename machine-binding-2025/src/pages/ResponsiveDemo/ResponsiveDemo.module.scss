@use "../../../styles/variables" as vars;
@use "../../../styles/mixins" as mixins;

.responsiveDemo {
  @include mixins.adaptive-content-area;
  
  @include mixins.adaptive-typography;
}

.header {
  text-align: center;
  margin-bottom: vars.$spacing-xl;
  
  h1 {
    color: vars.$primary-color;
    margin-bottom: vars.$spacing-md;
  }
  
  p {
    color: vars.$text-muted;
    font-size: vars.$font-size-lg;
    
    @include mixins.mobile-portrait {
      font-size: vars.$font-size-md;
    }
    
    @include mixins.mobile-landscape {
      font-size: vars.$font-size-sm;
    }
  }
}

.section {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
  
  @include mixins.mobile-portrait {
    padding: vars.$spacing-lg;
    margin-bottom: vars.$spacing-md;
    border-radius: vars.$border-radius-md;
  }
  
  @include mixins.mobile-landscape {
    padding: vars.$spacing-md;
    margin-bottom: vars.$spacing-sm;
    border-radius: vars.$border-radius-sm;
  }
  
  h2 {
    color: vars.$primary-color;
    margin-bottom: vars.$spacing-lg;
    border-bottom: 2px solid vars.$border-color-light;
    padding-bottom: vars.$spacing-sm;
    
    @include mixins.mobile-portrait {
      margin-bottom: vars.$spacing-md;
      font-size: vars.$font-size-lg;
    }
    
    @include mixins.mobile-landscape {
      margin-bottom: vars.$spacing-sm;
      font-size: vars.$font-size-md;
    }
  }
}

.contentDemo {
  .contentCard {
    @include mixins.adaptive-card-layout;
    background: vars.$background-secondary;
    border: 1px solid vars.$border-color;
    border-radius: vars.$border-radius-md;
    padding: vars.$spacing-lg;
    
    h3 {
      color: vars.$text-primary;
      margin-bottom: vars.$spacing-sm;
      font-size: vars.$font-size-md;
      
      @include mixins.mobile-landscape {
        font-size: vars.$font-size-sm;
        margin-bottom: vars.$spacing-xs;
      }
    }
    
    p {
      color: vars.$text-muted;
      font-size: vars.$font-size-sm;
      line-height: 1.5;
      margin-bottom: 0;
      
      @include mixins.mobile-landscape {
        font-size: vars.$font-size-xs;
        line-height: 1.4;
      }
    }
    
    @include mixins.mobile-portrait {
      padding: vars.$spacing-md;
    }
    
    @include mixins.mobile-landscape {
      padding: vars.$spacing-sm;
    }
  }
}

// Form styling
.section {
  .form-row {
    display: flex;
    gap: vars.$spacing-lg;
    margin-bottom: vars.$spacing-lg;
    
    @include mixins.mobile-portrait {
      flex-direction: column;
      gap: vars.$spacing-md;
      margin-bottom: vars.$spacing-md;
    }
    
    @include mixins.mobile-landscape {
      gap: vars.$spacing-md;
      margin-bottom: vars.$spacing-sm;
    }
  }
  
  .form-group {
    flex: 1;
    
    label {
      display: block;
      font-weight: vars.$font-weight-medium;
      color: vars.$text-primary;
      margin-bottom: vars.$spacing-xs;
      font-size: vars.$font-size-sm;
      
      @include mixins.mobile-landscape {
        font-size: vars.$font-size-xs;
      }
    }
    
    input {
      width: 100%;
      padding: vars.$spacing-md;
      border: 1px solid vars.$border-color;
      border-radius: vars.$border-radius-sm;
      font-size: vars.$font-size-md;
      
      @include mixins.mobile-portrait {
        padding: vars.$spacing-lg;
        font-size: 16px; // Prevent zoom on iOS
        min-height: vars.$touch-target-comfortable;
      }
      
      @include mixins.mobile-landscape {
        padding: vars.$spacing-sm;
        font-size: 16px; // Prevent zoom on iOS
        min-height: vars.$touch-target-min;
      }
      
      &:focus {
        outline: 2px solid vars.$primary-color;
        outline-offset: 2px;
        border-color: vars.$primary-color;
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: vars.$spacing-md;
    justify-content: flex-end;
    
    @include mixins.mobile-portrait {
      flex-direction: column;
      gap: vars.$spacing-sm;
    }
    
    @include mixins.mobile-landscape {
      gap: vars.$spacing-sm;
    }
    
    button {
      padding: vars.$spacing-md vars.$spacing-lg;
      border: 1px solid vars.$border-color;
      border-radius: vars.$border-radius-sm;
      font-size: vars.$font-size-md;
      font-weight: vars.$font-weight-medium;
      cursor: pointer;
      transition: all 0.3s ease;
      
      @include mixins.mobile-portrait {
        width: 100%;
        padding: vars.$spacing-lg;
        min-height: vars.$touch-target-comfortable;
      }
      
      @include mixins.mobile-landscape {
        padding: vars.$spacing-sm vars.$spacing-md;
        min-height: vars.$touch-target-min;
        font-size: vars.$font-size-sm;
      }
      
      &[type="submit"] {
        background: vars.$primary-color;
        color: white;
        border-color: vars.$primary-color;
        
        &:hover {
          background: darken(vars.$primary-color, 10%);
        }
      }
      
      &[type="button"] {
        background: white;
        color: vars.$text-primary;
        
        &:hover {
          background: vars.$background-secondary;
        }
      }
      
      &:focus {
        outline: 2px solid vars.$primary-color;
        outline-offset: 2px;
      }
      
      @include mixins.touch-device {
        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}

// Responsive indicators for testing
.responsiveDemo {
  position: relative;
  
  &::before {
    content: 'Responsive Demo Active';
    position: fixed;
    bottom: 10px;
    left: 10px;
    background: rgba(vars.$primary-color, 0.9);
    color: white;
    padding: vars.$spacing-xs vars.$spacing-sm;
    border-radius: vars.$border-radius-sm;
    font-size: vars.$font-size-xs;
    z-index: vars.$z-index-tooltip;
    
    @include mixins.mobile-portrait {
      bottom: calc(80px + env(safe-area-inset-bottom) + 10px);
    }
    
    @include mixins.mobile-landscape {
      bottom: 10px;
      left: 10px;
    }
  }
}

// Animation for layout changes
.contentCard,
.section {
  transition: all 0.3s ease;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .contentCard,
  .section,
  button {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .contentCard,
  .section {
    border-width: 2px;
  }
  
  input,
  button {
    border-width: 2px;
  }
}