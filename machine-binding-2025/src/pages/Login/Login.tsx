import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks';
import { Button, Input } from '../../components/common';
import { LoginCredentials } from '../../types';
import styles from './Login.module.scss';

export const Login: React.FC = () => {
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [formData, setFormData] = useState<LoginCredentials>({
    username: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState<Partial<LoginCredentials>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Clear errors when form data changes
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [formData, error, clearError]);

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/home" replace />;
  }

  // Form validation
  const validateForm = (): boolean => {
    const errors: Partial<LoginCredentials> = {};

    if (!formData.username.trim()) {
      errors.username = '请输入用户名';
    }

    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少需要6位字符';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name as keyof LoginCredentials]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await login(formData);
      // Navigation will be handled by the redirect above
    } catch (error) {
      console.error('Login failed:', error);
      // Error is handled by the auth context
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle Enter key press
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !isSubmitting) {
      handleSubmit(event as React.FormEvent);
    }
  };

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginContent}>
        {/* Logo Section */}
        <div className={styles.logo}>
          <div className={styles.logoImage}>
            🏭
          </div>
          <h1 className={styles.logoText}>设备绑定系统</h1>
        </div>

        {/* Login Form */}
        <div className={`${styles.loginCard} ${isLoading ? styles.loading : ''}`}>
          <h2 className={styles.loginTitle}>用户登录</h2>

          {/* Error Message */}
          {error && (
            <div className={styles.errorMessage} role="alert">
              ⚠️ {error}
            </div>
          )}

          <form className={styles.loginForm} onSubmit={handleSubmit}>
            <div className={styles.formGroup}>
              <Input
                name="username"
                label="用户名"
                type="text"
                value={formData.username}
                placeholder="请输入用户名"
                error={formErrors.username}
                required
                autoComplete="username"
                autoFocus
                disabled={isSubmitting}
                leftIcon="👤"
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                testId="username-input"
              />
            </div>

            <div className={styles.formGroup}>
              <Input
                name="password"
                label="密码"
                type="password"
                value={formData.password}
                placeholder="请输入密码"
                error={formErrors.password}
                required
                autoComplete="current-password"
                disabled={isSubmitting}
                leftIcon="🔒"
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                testId="password-input"
              />
            </div>

            <Button
              type="submit"
              variant="primary"
              size="large"
              fullWidth
              loading={isSubmitting}
              disabled={isSubmitting || isLoading}
              className={styles.submitButton}
              testId="login-button"
            >
              {isSubmitting ? '登录中...' : '登录'}
            </Button>
          </form>

          {/* Forgot Password Link */}
          <div className={styles.forgotPassword}>
            <a href="#" className={styles.forgotPasswordLink}>
              忘记密码？
            </a>
          </div>
        </div>

        {/* Footer */}
        <div className={styles.footer}>
          <p>© 2025 设备绑定系统. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};