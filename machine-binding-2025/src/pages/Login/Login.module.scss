@import '../../styles/variables.scss';

.loginContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: $spacing-lg;
}

.loginContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.logo {
  margin-bottom: $spacing-xxl;
  text-align: center;
}

.logoImage {
  width: 80px;
  height: 80px;
  margin-bottom: $spacing-md;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto $spacing-md;
}

.logoText {
  color: white;
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  margin: 0;
}

.loginCard {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-lg;
  width: 100%;
  max-width: 400px;
}

.loginTitle {
  text-align: center;
  margin-bottom: $spacing-xl;
  color: $text-primary;
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.submitButton {
  margin-top: $spacing-md;
}

.errorMessage {
  background-color: rgba($danger-color, 0.1);
  border: 1px solid rgba($danger-color, 0.3);
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
  color: $danger-color;
  font-size: $font-size-sm;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.forgotPassword {
  text-align: center;
  margin-top: $spacing-lg;
}

.forgotPasswordLink {
  color: $primary-color;
  text-decoration: none;
  font-size: $font-size-sm;

  &:hover {
    text-decoration: underline;
  }
}

.footer {
  margin-top: auto;
  padding-top: $spacing-xl;
  text-align: center;
  color: rgba(white, 0.8);
  font-size: $font-size-sm;
}

// Mobile optimizations
@media (max-width: $tablet - 1px) {
  .loginContainer {
    padding: $spacing-md;
  }

  .loginCard {
    padding: $spacing-lg;
    margin: $spacing-md 0;
  }

  .loginTitle {
    font-size: $font-size-lg;
  }

  .logoImage {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .logoText {
    font-size: $font-size-lg;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .loginContainer {
    padding: $spacing-xl;
  }

  .loginContent {
    max-width: 500px;
  }

  .loginCard {
    padding: $spacing-xxl;
  }
}

// Loading state
.loading {
  .loginForm {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Animation for form appearance
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loginCard {
  animation: slideUp 0.3s ease-out;
}