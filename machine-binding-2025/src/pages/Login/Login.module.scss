@use "../../../styles/variables" as vars;

.loginContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: vars.$spacing-lg;
}

.loginContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.logo {
  margin-bottom: vars.$spacing-xxl;
  text-align: center;
}

.logoImage {
  width: 80px;
  height: 80px;
  margin-bottom: vars.$spacing-md;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto vars.$spacing-md;
}

.logoText {
  color: white;
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-bold;
  margin: 0;
}

.loginCard {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  box-shadow: vars.$shadow-lg;
  width: 100%;
  max-width: 400px;
}

.loginTitle {
  text-align: center;
  margin-bottom: vars.$spacing-xl;
  color: vars.$text-primary;
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-semibold;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-lg;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.submitButton {
  margin-top: vars.$spacing-md;
}

.errorMessage {
  background-color: rgba(vars.$danger-color, 0.1);
  border: 1px solid rgba(vars.$danger-color, 0.3);
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-md;
  margin-bottom: vars.$spacing-lg;
  color: vars.$danger-color;
  font-size: vars.$font-size-sm;
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
}

.forgotPassword {
  text-align: center;
  margin-top: vars.$spacing-lg;
}

.forgotPasswordLink {
  color: vars.$primary-color;
  text-decoration: none;
  font-size: vars.$font-size-sm;

  &:hover {
    text-decoration: underline;
  }
}

.footer {
  margin-top: auto;
  padding-top: vars.$spacing-xl;
  text-align: center;
  color: rgba(white, 0.8);
  font-size: vars.$font-size-sm;
}

// Mobile optimizations
@media (max-width: vars.$tablet - 1px) {
  .loginContainer {
    padding: vars.$spacing-md;
  }

  .loginCard {
    padding: vars.$spacing-lg;
    margin: vars.$spacing-md 0;
  }

  .loginTitle {
    font-size: vars.$font-size-lg;
  }

  .logoImage {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .logoText {
    font-size: vars.$font-size-lg;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) and (max-width: vars.$tablet-large - 1px) {
  .loginContainer {
    padding: vars.$spacing-xl;
  }

  .loginContent {
    max-width: 500px;
  }

  .loginCard {
    padding: vars.$spacing-xxl;
  }
}

// Loading state
.loading {
  .loginForm {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Animation for form appearance
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loginCard {
  animation: slideUp 0.3s ease-out;
}