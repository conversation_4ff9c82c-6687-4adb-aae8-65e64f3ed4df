@import '../../styles/variables.scss';

.admin {
  max-width: 1000px;
  margin: 0 auto;
  padding: $spacing-lg;
}

// Admin Header Section
.adminHeader {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  color: white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.adminIcon {
  font-size: 4rem;
  flex-shrink: 0;
  z-index: 1;
}

.adminInfo {
  flex: 1;
  z-index: 1;
}

.adminTitle {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  margin: 0 0 $spacing-xs 0;
}

.adminDescription {
  font-size: $font-size-md;
  opacity: 0.9;
  margin: 0 0 $spacing-sm 0;
}

.adminMeta {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.adminBadge {
  background: rgba(white, 0.2);
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.adminUser {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  opacity: 0.9;
}

// Section Styles
.section {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-sm;
}

.sectionTitle {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  margin: 0 0 $spacing-sm 0;
}

.sectionDescription {
  font-size: $font-size-md;
  color: $text-muted;
  margin: 0 0 $spacing-lg 0;
}

// Action Grid Container
.actionGridContainer {
  margin-top: $spacing-lg;
}

// System Information
.systemInfo {
  margin-top: $spacing-lg;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
}

.infoItem {
  background: $background-light;
  padding: $spacing-lg;
  border-radius: $border-radius-md;
  border: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.infoLabel {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-primary;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.statusIndicator {
  font-size: $font-size-sm;
}

// Quick Actions
.quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-md;
  margin-top: $spacing-lg;
}

.quickAction {
  background: $background-light;
  border: 2px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    border-color: $primary-color;
    background: rgba($primary-color, 0.05);
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

.quickActionIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.quickActionText {
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

// Warning Section
.warningSection {
  background: rgba($warning-color, 0.1);
  border: 1px solid rgba($warning-color, 0.3);
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  display: flex;
  align-items: flex-start;
  gap: $spacing-lg;
  margin-top: $spacing-lg;
}

.warningIcon {
  font-size: 2rem;
  flex-shrink: 0;
  color: $warning-color;
}

.warningContent {
  flex: 1;
}

.warningTitle {
  font-size: $font-size-md;
  font-weight: $font-weight-semibold;
  color: $warning-color;
  margin: 0 0 $spacing-xs 0;
}

.warningText {
  font-size: $font-size-sm;
  color: $text-muted;
  margin: 0;
  line-height: 1.6;
}

// Enhanced mobile optimizations with orientation support
@include mobile-portrait {
  .admin {
    @include adaptive-content-area;
    padding: $spacing-md;
  }

  .adminHeader {
    padding: $spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: $spacing-md;
  }

  .adminIcon {
    font-size: 3rem;
  }

  .adminTitle {
    font-size: $font-size-lg;
  }

  .adminMeta {
    justify-content: center;
    flex-wrap: wrap;
  }

  .section {
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .quickActions {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .quickAction {
    padding: $spacing-md;
    flex-direction: column;
    text-align: center;
    gap: $spacing-sm;
    min-height: $touch-target-comfortable;
  }

  .warningSection {
    padding: $spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: $spacing-md;
  }
}

@include mobile-landscape {
  .admin {
    @include adaptive-content-area;
    padding: $spacing-sm $spacing-md;
  }

  .adminHeader {
    padding: $spacing-md $spacing-lg;
    flex-direction: row;
    text-align: left;
    gap: $spacing-lg;
  }

  .adminIcon {
    font-size: 2.5rem;
    flex-shrink: 0;
  }

  .adminTitle {
    font-size: $font-size-md;
  }

  .adminMeta {
    justify-content: flex-start;
  }

  .section {
    padding: $spacing-md;
    margin-bottom: $spacing-sm;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }

  .quickActions {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
  }

  .quickAction {
    padding: $spacing-sm $spacing-md;
    flex-direction: row;
    text-align: left;
    gap: $spacing-sm;
  }

  .warningSection {
    padding: $spacing-md;
    flex-direction: row;
    text-align: left;
    gap: $spacing-md;
  }
}

// Tablet optimizations
@media (min-width: $tablet) and (max-width: $tablet-large - 1px) {
  .admin {
    padding: $spacing-xl;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quickActions {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Large screen optimizations
@media (min-width: $desktop) {
  .infoGrid {
    grid-template-columns: repeat(4, 1fr);
  }

  .quickActions {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Animation for sections
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: slideUp 0.3s ease-out;
}

.adminHeader {
  animation: slideUp 0.3s ease-out;
}

.warningSection {
  animation: slideUp 0.3s ease-out;
}

// Hover effects for interactive elements
.quickAction {
  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// Admin-specific color scheme
.admin {
  --admin-primary: #{$danger-color};
  --admin-secondary: #{$warning-color};
  --admin-accent: #{$info-color};
}

// Special styling for admin elements
.adminHeader {
  border-left: 4px solid rgba(white, 0.3);
}

.section {
  border-left: 4px solid transparent;
  transition: border-color 0.3s ease;

  &:hover {
    border-left-color: var(--admin-primary);
  }
}

// Loading states for admin actions
.quickAction {
  &.loading {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      border-color: $border-color;
      background: $background-light;
    }
  }
}

// Admin badge styling
.adminBadge {
  position: relative;
  
  &::before {
    content: '👑';
    margin-right: $spacing-xs;
  }
}