@use "../../../styles/variables" as vars;

.admin {
  max-width: 1000px;
  margin: 0 auto;
  padding: vars.$spacing-lg;
}

// Admin Header Section
.adminHeader {
  background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  color: white;
  padding: vars.$spacing-xl;
  border-radius: vars.$border-radius-lg;
  margin-bottom: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-lg;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.adminIcon {
  font-size: 4rem;
  flex-shrink: 0;
  z-index: 1;
}

.adminInfo {
  flex: 1;
  z-index: 1;
}

.adminTitle {
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-bold;
  margin: 0 0 vars.$spacing-xs 0;
}

.adminDescription {
  font-size: vars.$font-size-md;
  opacity: 0.9;
  margin: 0 0 vars.$spacing-sm 0;
}

.adminMeta {
  display: flex;
  align-items: center;
  gap: vars.$spacing-sm;
  flex-wrap: wrap;
}

.adminBadge {
  background: rgba(white, 0.2);
  padding: vars.$spacing-xs vars.$spacing-sm;
  border-radius: vars.$border-radius-sm;
  font-size: vars.$font-size-xs;
  font-weight: vars.$font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.adminUser {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  opacity: 0.9;
}

// Section Styles
.section {
  background: white;
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  margin-bottom: vars.$spacing-lg;
  box-shadow: vars.$shadow-sm;
}

.sectionTitle {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  margin: 0 0 vars.$spacing-sm 0;
}

.sectionDescription {
  font-size: vars.$font-size-md;
  color: vars.$text-muted;
  margin: 0 0 vars.$spacing-lg 0;
}

// Action Grid Container
.actionGridContainer {
  margin-top: vars.$spacing-lg;
}

// System Information
.systemInfo {
  margin-top: vars.$spacing-lg;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-lg;
}

.infoItem {
  background: vars.$background-light;
  padding: vars.$spacing-lg;
  border-radius: vars.$border-radius-md;
  border: 1px solid vars.$border-color;
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-xs;
}

.infoLabel {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-muted;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.infoValue {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-semibold;
  color: vars.$text-primary;
  display: flex;
  align-items: center;
  gap: vars.$spacing-xs;
}

.statusIndicator {
  font-size: vars.$font-size-sm;
}

// Quick Actions
.quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: vars.$spacing-md;
  margin-top: vars.$spacing-lg;
}

.quickAction {
  background: vars.$background-light;
  border: 2px solid vars.$border-color;
  border-radius: vars.$border-radius-md;
  padding: vars.$spacing-lg;
  display: flex;
  align-items: center;
  gap: vars.$spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    border-color: vars.$primary-color;
    background: rgba(vars.$primary-color, 0.05);
    transform: translateY(-2px);
    box-shadow: vars.$shadow-md;
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

.quickActionIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.quickActionText {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-medium;
  color: vars.$text-primary;
}

// Warning Section
.warningSection {
  background: rgba(vars.$warning-color, 0.1);
  border: 1px solid rgba(vars.$warning-color, 0.3);
  border-radius: vars.$border-radius-lg;
  padding: vars.$spacing-xl;
  display: flex;
  align-items: flex-start;
  gap: vars.$spacing-lg;
  margin-top: vars.$spacing-lg;
}

.warningIcon {
  font-size: 2rem;
  flex-shrink: 0;
  color: vars.$warning-color;
}

.warningContent {
  flex: 1;
}

.warningTitle {
  font-size: vars.$font-size-md;
  font-weight: vars.$font-weight-semibold;
  color: vars.$warning-color;
  margin: 0 0 vars.$spacing-xs 0;
}

.warningText {
  font-size: vars.$font-size-sm;
  color: vars.$text-muted;
  margin: 0;
  line-height: 1.6;
}

// Enhanced mobile optimizations with orientation support
@include mixins.mobile-portrait {
  .admin {
    @include mixins.adaptive-content-area;
    padding: vars.$spacing-md;
  }

  .adminHeader {
    padding: vars.$spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-md;
  }

  .adminIcon {
    font-size: 3rem;
  }

  .adminTitle {
    font-size: vars.$font-size-lg;
  }

  .adminMeta {
    justify-content: center;
    flex-wrap: wrap;
  }

  .section {
    padding: vars.$spacing-lg;
    margin-bottom: vars.$spacing-md;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .quickActions {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  .quickAction {
    padding: vars.$spacing-md;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-sm;
    min-height: vars.$touch-target-comfortable;
  }

  .warningSection {
    padding: vars.$spacing-lg;
    flex-direction: column;
    text-align: center;
    gap: vars.$spacing-md;
  }
}

@include mixins.mobile-landscape {
  .admin {
    @include mixins.adaptive-content-area;
    padding: vars.$spacing-sm vars.$spacing-md;
  }

  .adminHeader {
    padding: vars.$spacing-md vars.$spacing-lg;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-lg;
  }

  .adminIcon {
    font-size: 2.5rem;
    flex-shrink: 0;
  }

  .adminTitle {
    font-size: vars.$font-size-md;
  }

  .adminMeta {
    justify-content: flex-start;
  }

  .section {
    padding: vars.$spacing-md;
    margin-bottom: vars.$spacing-sm;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
  }

  .quickActions {
    grid-template-columns: repeat(2, 1fr);
    gap: vars.$spacing-sm;
  }

  .quickAction {
    padding: vars.$spacing-sm vars.$spacing-md;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-sm;
  }

  .warningSection {
    padding: vars.$spacing-md;
    flex-direction: row;
    text-align: left;
    gap: vars.$spacing-md;
  }
}

// Tablet optimizations
@media (min-width: vars.$tablet) and (max-width: vars.$tablet-large - 1px) {
  .admin {
    padding: vars.$spacing-xl;
  }

  .infoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quickActions {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Large screen optimizations
@media (min-width: vars.$desktop) {
  .infoGrid {
    grid-template-columns: repeat(4, 1fr);
  }

  .quickActions {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Animation for sections
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: slideUp 0.3s ease-out;
}

.adminHeader {
  animation: slideUp 0.3s ease-out;
}

.warningSection {
  animation: slideUp 0.3s ease-out;
}

// Hover effects for interactive elements
.quickAction {
  &:focus-visible {
    outline: 2px solid vars.$primary-color;
    outline-offset: 2px;
  }
}

// Admin-specific color scheme
.admin {
  --admin-primary: #{vars.$danger-color};
  --admin-secondary: #{vars.$warning-color};
  --admin-accent: #{vars.$info-color};
}

// Special styling for admin elements
.adminHeader {
  border-left: 4px solid rgba(white, 0.3);
}

.section {
  border-left: 4px solid transparent;
  transition: border-color 0.3s ease;

  &:hover {
    border-left-color: var(--admin-primary);
  }
}

// Loading states for admin actions
.quickAction {
  &.loading {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      border-color: vars.$border-color;
      background: vars.$background-light;
    }
  }
}

// Admin badge styling
.adminBadge {
  position: relative;
  
  &::before {
    content: '👑';
    margin-right: vars.$spacing-xs;
  }
}