import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Layout } from '../../components/navigation';
import { AdminActionGrid } from '../../components/common';
import { useAuth, useToast } from '../../hooks';
import type { ActionGridItem } from '../../types';
import styles from './Admin.module.scss';

export const Admin: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { showToast } = useToast();

  // Check admin permissions on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!user?.isAdmin) {
      showToast({
        type: 'error',
        title: '访问被拒绝',
        message: '您没有管理员权限，无法访问此页面',
      });
      navigate('/home');
      return;
    }
  }, [isAuthenticated, user, navigate, showToast]);

  // Handle admin action clicks
  const handleAdminAction = (item: ActionGridItem) => {
    // For now, show placeholder messages for admin functions
    // In a real implementation, these would navigate to actual admin pages
    switch (item.id) {
      case 'account-management':
        showToast({
          type: 'info',
          title: '账号管理',
          message: '账号管理功能正在开发中',
        });
        break;
      case 'department-management':
        showToast({
          type: 'info',
          title: '部门管理',
          message: '部门管理功能正在开发中',
        });
        break;
      case 'device-management':
        showToast({
          type: 'info',
          title: '设备管理',
          message: '设备管理功能正在开发中',
        });
        break;
      case 'task-management':
        showToast({
          type: 'info',
          title: '任务管理',
          message: '任务管理功能正在开发中',
        });
        break;
      case 'maintenance-management':
        showToast({
          type: 'info',
          title: '保养管理',
          message: '保养管理功能正在开发中',
        });
        break;
      case 'fault-categories':
        showToast({
          type: 'info',
          title: '故障类别',
          message: '故障类别管理功能正在开发中',
        });
        break;
      case 'status-management':
        showToast({
          type: 'info',
          title: '状态管理',
          message: '状态管理功能正在开发中',
        });
        break;
      default:
        showToast({
          type: 'info',
          title: '功能开发中',
          message: '该功能正在开发中，敬请期待',
        });
    }
  };

  const navProps = {
    title: '管理中心',
    showBack: true,
    onBack: () => navigate('/home'),
  };

  // Don't render anything if user is not admin (will redirect)
  if (!user?.isAdmin) {
    return null;
  }

  return (
    <Layout navProps={navProps}>
      <div className={styles.admin}>
        {/* Admin Header */}
        <div className={styles.adminHeader}>
          <div className={styles.adminIcon}>⚙️</div>
          <div className={styles.adminInfo}>
            <h2 className={styles.adminTitle}>管理中心</h2>
            <p className={styles.adminDescription}>
              系统管理和配置功能
            </p>
            <div className={styles.adminMeta}>
              <span className={styles.adminBadge}>管理员</span>
              <span className={styles.adminUser}>{user.name}</span>
            </div>
          </div>
        </div>

        {/* Admin Functions Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>管理功能</h3>
          <p className={styles.sectionDescription}>
            选择下方功能进行系统管理和配置
          </p>
          
          <div className={styles.actionGridContainer}>
            <AdminActionGrid onItemClick={handleAdminAction} />
          </div>
        </div>

        {/* System Information Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>系统信息</h3>
          <div className={styles.systemInfo}>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>系统版本</span>
                <span className={styles.infoValue}>v1.0.0</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>数据库状态</span>
                <span className={styles.infoValue}>
                  <span className={styles.statusIndicator}>🟢</span>
                  正常
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>在线用户</span>
                <span className={styles.infoValue}>1</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>系统负载</span>
                <span className={styles.infoValue}>
                  <span className={styles.statusIndicator}>🟢</span>
                  低
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Section */}
        <div className={styles.section}>
          <h3 className={styles.sectionTitle}>快速操作</h3>
          <div className={styles.quickActions}>
            <button 
              className={styles.quickAction}
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '系统日志',
                  message: '系统日志查看功能正在开发中',
                });
              }}
            >
              <div className={styles.quickActionIcon}>📋</div>
              <div className={styles.quickActionText}>查看系统日志</div>
            </button>

            <button 
              className={styles.quickAction}
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '数据备份',
                  message: '数据备份功能正在开发中',
                });
              }}
            >
              <div className={styles.quickActionIcon}>💾</div>
              <div className={styles.quickActionText}>数据备份</div>
            </button>

            <button 
              className={styles.quickAction}
              onClick={() => {
                showToast({
                  type: 'info',
                  title: '系统设置',
                  message: '系统设置功能正在开发中',
                });
              }}
            >
              <div className={styles.quickActionIcon}>⚙️</div>
              <div className={styles.quickActionText}>系统设置</div>
            </button>
          </div>
        </div>

        {/* Warning Section */}
        <div className={styles.warningSection}>
          <div className={styles.warningIcon}>⚠️</div>
          <div className={styles.warningContent}>
            <h4 className={styles.warningTitle}>管理员提醒</h4>
            <p className={styles.warningText}>
              请谨慎使用管理功能，所有操作都会被记录在系统日志中。
              如有疑问，请联系系统管理员。
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};