import React, { createContext, useReducer, ReactNode, useEffect } from 'react';
import type { ApiError } from '../types';
import { errorHandler } from '../utils/errorHandler';
import { networkStatusDetector } from '../utils/networkStatus';

// API Context State
interface ApiState {
  isOnline: boolean;
  lastError: ApiError | null;
  requestCount: number;
  cache: Record<string, any>;
}

// API Context Type
interface ApiContextType extends ApiState {
  setOnlineStatus: (isOnline: boolean) => void;
  setError: (error: ApiError | null) => void;
  clearError: () => void;
  incrementRequestCount: () => void;
  decrementRequestCount: () => void;
  setCacheData: (key: string, data: any) => void;
  getCacheData: (key: string) => any;
  clearCache: () => void;
  clearCacheKey: (key: string) => void;
}

// API Actions
type ApiAction =
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'SET_ERROR'; payload: ApiError | null }
  | { type: 'CLEAR_ERROR' }
  | { type: 'INCREMENT_REQUEST_COUNT' }
  | { type: 'DECREMENT_REQUEST_COUNT' }
  | { type: 'SET_CACHE_DATA'; payload: { key: string; data: any } }
  | { type: 'CLEAR_CACHE' }
  | { type: 'CLEAR_CACHE_KEY'; payload: string };

// Initial state
const initialState: ApiState = {
  isOnline: navigator.onLine,
  lastError: null,
  requestCount: 0,
  cache: {},
};

// API reducer
const apiReducer = (state: ApiState, action: ApiAction): ApiState => {
  switch (action.type) {
    case 'SET_ONLINE_STATUS':
      return {
        ...state,
        isOnline: action.payload,
      };

    case 'SET_ERROR':
      return {
        ...state,
        lastError: action.payload,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        lastError: null,
      };

    case 'INCREMENT_REQUEST_COUNT':
      return {
        ...state,
        requestCount: state.requestCount + 1,
      };

    case 'DECREMENT_REQUEST_COUNT':
      return {
        ...state,
        requestCount: Math.max(0, state.requestCount - 1),
      };

    case 'SET_CACHE_DATA':
      return {
        ...state,
        cache: {
          ...state.cache,
          [action.payload.key]: {
            data: action.payload.data,
            timestamp: Date.now(),
          },
        },
      };

    case 'CLEAR_CACHE':
      return {
        ...state,
        cache: {},
      };

    case 'CLEAR_CACHE_KEY': {
      const newCache = { ...state.cache };
      delete newCache[action.payload];
      return {
        ...state,
        cache: newCache,
      };
    }

    default:
      return state;
  }
};

// Create context
const ApiContext = createContext<ApiContextType | undefined>(undefined);

// API Provider Props
interface ApiProviderProps {
  children: ReactNode;
}

// API Provider Component
export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(apiReducer, initialState);

  // Set online status
  const setOnlineStatus = (isOnline: boolean): void => {
    dispatch({ type: 'SET_ONLINE_STATUS', payload: isOnline });
  };

  // Set error
  const setError = (error: ApiError | null): void => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  // Clear error
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Request count management
  const incrementRequestCount = (): void => {
    dispatch({ type: 'INCREMENT_REQUEST_COUNT' });
  };

  const decrementRequestCount = (): void => {
    dispatch({ type: 'DECREMENT_REQUEST_COUNT' });
  };

  // Cache management
  const setCacheData = (key: string, data: any): void => {
    dispatch({ type: 'SET_CACHE_DATA', payload: { key, data } });
  };

  const getCacheData = (key: string): any => {
    const cached = state.cache[key];
    if (!cached) return null;

    // Check if cache is expired (5 minutes default)
    const maxAge = 5 * 60 * 1000; // 5 minutes
    if (Date.now() - cached.timestamp > maxAge) {
      clearCacheKey(key);
      return null;
    }

    return cached.data;
  };

  const clearCache = (): void => {
    dispatch({ type: 'CLEAR_CACHE' });
  };

  const clearCacheKey = (key: string): void => {
    dispatch({ type: 'CLEAR_CACHE_KEY', payload: key });
  };

  // Listen for network status changes
  useEffect(() => {
    const unsubscribe = networkStatusDetector.addListener((status) => {
      setOnlineStatus(status.isOnline);
    });

    return unsubscribe;
  }, []);

  // Set up error handler integration
  useEffect(() => {
    // This will be set up by the error handler hook in components
    return () => {
      // Cleanup if needed
    };
  }, []);

  const contextValue: ApiContextType = {
    ...state,
    setOnlineStatus,
    setError,
    clearError,
    incrementRequestCount,
    decrementRequestCount,
    setCacheData,
    getCacheData,
    clearCache,
    clearCacheKey,
  };

  return (
    <ApiContext.Provider value={contextValue}>
      {children}
    </ApiContext.Provider>
  );
};

// Export context for hooks
export { ApiContext };