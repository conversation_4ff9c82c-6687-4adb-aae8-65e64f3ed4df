import React, { createContext, useReducer, ReactNode } from 'react';
import { ToastMessage, LoadingState, UIState } from '../types';

// UI Context Type
interface UIContextType extends UIState {
  // Toast methods
  showToast: (toast: Omit<ToastMessage, 'id'>) => void;
  hideToast: (id: string) => void;
  clearToasts: () => void;
  
  // Modal methods
  openModal: (modalId: string) => void;
  closeModal: (modalId: string) => void;
  isModalOpen: (modalId: string) => boolean;
  
  // Loading methods
  setLoading: (loading: boolean, message?: string) => void;
  clearLoading: () => void;
}

// UI Actions
type UIAction =
  | { type: 'SHOW_TOAST'; payload: ToastMessage }
  | { type: 'HIDE_TOAST'; payload: string }
  | { type: 'CLEAR_TOASTS' }
  | { type: 'OPEN_MODAL'; payload: string }
  | { type: 'CLOSE_MODAL'; payload: string }
  | { type: 'SET_LOADING'; payload: LoadingState }
  | { type: 'CLEAR_LOADING' };

// Initial state
const initialState: UIState = {
  toasts: [],
  modals: {},
  loading: {
    isLoading: false,
    message: undefined,
  },
};

// UI reducer
const uiReducer = (state: UIState, action: UIAction): UIState => {
  switch (action.type) {
    case 'SHOW_TOAST':
      return {
        ...state,
        toasts: [...state.toasts, action.payload],
      };

    case 'HIDE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter(toast => toast.id !== action.payload),
      };

    case 'CLEAR_TOASTS':
      return {
        ...state,
        toasts: [],
      };

    case 'OPEN_MODAL':
      return {
        ...state,
        modals: {
          ...state.modals,
          [action.payload]: true,
        },
      };

    case 'CLOSE_MODAL':
      return {
        ...state,
        modals: {
          ...state.modals,
          [action.payload]: false,
        },
      };

    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      };

    case 'CLEAR_LOADING':
      return {
        ...state,
        loading: {
          isLoading: false,
          message: undefined,
        },
      };

    default:
      return state;
  }
};

// Create context
const UIContext = createContext<UIContextType | undefined>(undefined);

// UI Provider Props
interface UIProviderProps {
  children: ReactNode;
}

// Generate unique ID for toasts
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// UI Provider Component
export const UIProvider: React.FC<UIProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(uiReducer, initialState);

  // Toast methods
  const showToast = (toast: Omit<ToastMessage, 'id'>): void => {
    const id = generateId();
    const newToast: ToastMessage = {
      id,
      duration: 3000,
      autoClose: true,
      ...toast,
    };

    dispatch({ type: 'SHOW_TOAST', payload: newToast });

    // Auto-hide toast if autoClose is enabled
    if (newToast.autoClose && newToast.duration) {
      setTimeout(() => {
        dispatch({ type: 'HIDE_TOAST', payload: id });
      }, newToast.duration);
    }
  };

  const hideToast = (id: string): void => {
    dispatch({ type: 'HIDE_TOAST', payload: id });
  };

  const clearToasts = (): void => {
    dispatch({ type: 'CLEAR_TOASTS' });
  };

  // Modal methods
  const openModal = (modalId: string): void => {
    dispatch({ type: 'OPEN_MODAL', payload: modalId });
  };

  const closeModal = (modalId: string): void => {
    dispatch({ type: 'CLOSE_MODAL', payload: modalId });
  };

  const isModalOpen = (modalId: string): boolean => {
    return state.modals[modalId] || false;
  };

  // Loading methods
  const setLoading = (loading: boolean, message?: string): void => {
    dispatch({
      type: 'SET_LOADING',
      payload: { isLoading: loading, message },
    });
  };

  const clearLoading = (): void => {
    dispatch({ type: 'CLEAR_LOADING' });
  };

  const contextValue: UIContextType = {
    ...state,
    showToast,
    hideToast,
    clearToasts,
    openModal,
    closeModal,
    isModalOpen,
    setLoading,
    clearLoading,
  };

  return (
    <UIContext.Provider value={contextValue}>
      {children}
    </UIContext.Provider>
  );
};

// Export context for hooks
export { UIContext };