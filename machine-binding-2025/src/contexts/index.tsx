// Re-export all contexts and hooks
export * from './AuthContext';
export * from './UIContext';
export * from './ApiContext';

// Combined provider component
import React from 'react';
import { AuthProvider } from './AuthContext';
import { UIProvider } from './UIContext';
import { ApiProvider } from './ApiContext';

interface AppProvidersProps {
  children: React.ReactNode;
}

export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ApiProvider>
      <UIProvider>
        <AuthProvider>
          {children}
        </AuthProvider>
      </UIProvider>
    </ApiProvider>
  );
};