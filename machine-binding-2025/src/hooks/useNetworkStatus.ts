import { useState, useEffect, useCallback } from 'react';
import { networkStatusDetector, type NetworkStatus } from '../utils/networkStatus';
import { useToast } from './useUI';

export interface UseNetworkStatusOptions {
  showOfflineToast?: boolean;
  showOnlineToast?: boolean;
  enablePeriodicCheck?: boolean;
  checkInterval?: number;
}

export const useNetworkStatus = (options: UseNetworkStatusOptions = {}) => {
  const {
    showOfflineToast = true,
    showOnlineToast = true,
    enablePeriodicCheck = false,
    checkInterval = 30000,
  } = options;

  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(
    networkStatusDetector.getStatus()
  );
  const [wasOffline, setWasOffline] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    const unsubscribe = networkStatusDetector.addListener((status) => {
      const previousStatus = networkStatus;
      setNetworkStatus(status);

      // Show toast notifications for status changes
      if (previousStatus.isOnline && !status.isOnline && showOfflineToast) {
        setWasOffline(true);
        showToast({
          type: 'warning',
          title: '网络连接已断开',
          message: '请检查您的网络连接',
          duration: 5000,
        });
      } else if (!previousStatus.isOnline && status.isOnline && showOnlineToast && wasOffline) {
        setWasOffline(false);
        showToast({
          type: 'success',
          title: '网络连接已恢复',
          message: '您现在可以继续使用应用',
          duration: 3000,
        });
      }
    });

    // Enable periodic connectivity checks if requested
    if (enablePeriodicCheck) {
      networkStatusDetector.startPeriodicCheck(checkInterval);
    }

    return () => {
      unsubscribe();
      if (enablePeriodicCheck) {
        networkStatusDetector.stopPeriodicCheck();
      }
    };
  }, [networkStatus.isOnline, showOfflineToast, showOnlineToast, wasOffline, showToast, enablePeriodicCheck, checkInterval]);

  const testConnectivity = useCallback(async (url?: string, timeout?: number): Promise<boolean> => {
    return networkStatusDetector.testConnectivity(url, timeout);
  }, []);

  const getConnectionQuality = useCallback(() => {
    return networkStatusDetector.getConnectionQuality();
  }, []);

  const refreshStatus = useCallback(() => {
    setNetworkStatus(networkStatusDetector.getStatus());
  }, []);

  return {
    ...networkStatus,
    connectionQuality: getConnectionQuality(),
    testConnectivity,
    refreshStatus,
  };
};

// Hook for components that need to handle offline scenarios
export const useOfflineHandler = () => {
  const { isOnline } = useNetworkStatus();
  const { showToast } = useToast();

  const handleOfflineAction = useCallback((
    action: () => void | Promise<void>,
    offlineMessage: string = '此操作需要网络连接，请检查您的网络后重试'
  ) => {
    if (!isOnline) {
      showToast({
        type: 'warning',
        title: '网络连接不可用',
        message: offlineMessage,
      });
      return;
    }

    if (typeof action === 'function') {
      const result = action();
      if (result instanceof Promise) {
        return result;
      }
    }
  }, [isOnline, showToast]);

  const isActionAllowed = useCallback((requiresNetwork: boolean = true): boolean => {
    return !requiresNetwork || isOnline;
  }, [isOnline]);

  return {
    isOnline,
    handleOfflineAction,
    isActionAllowed,
  };
};

// Hook for managing offline data synchronization
export const useOfflineSync = <T = any>() => {
  const { isOnline } = useNetworkStatus();
  const [pendingOperations, setPendingOperations] = useState<Array<{
    id: string;
    operation: () => Promise<T>;
    data: any;
    timestamp: number;
  }>>([]);

  const addPendingOperation = useCallback((
    operation: () => Promise<T>,
    data: any
  ) => {
    const id = Date.now().toString(36) + Math.random().toString(36).substr(2);
    setPendingOperations(prev => [...prev, {
      id,
      operation,
      data,
      timestamp: Date.now(),
    }]);
    return id;
  }, []);

  const removePendingOperation = useCallback((id: string) => {
    setPendingOperations(prev => prev.filter(op => op.id !== id));
  }, []);

  const syncPendingOperations = useCallback(async () => {
    if (!isOnline || pendingOperations.length === 0) {
      return;
    }

    const results = await Promise.allSettled(
      pendingOperations.map(async (op) => {
        try {
          const result = await op.operation();
          return { id: op.id, result, success: true };
        } catch (error) {
          return { id: op.id, error, success: false };
        }
      })
    );

    // Remove successful operations
    const successfulIds = results
      .filter((result): result is PromiseFulfilledResult<{ id: string; result: T; success: true }> => 
        result.status === 'fulfilled' && result.value.success
      )
      .map(result => result.value.id);

    setPendingOperations(prev => 
      prev.filter(op => !successfulIds.includes(op.id))
    );

    return results;
  }, [isOnline, pendingOperations]);

  // Auto-sync when coming back online
  useEffect(() => {
    if (isOnline && pendingOperations.length > 0) {
      syncPendingOperations();
    }
  }, [isOnline, pendingOperations.length, syncPendingOperations]);

  return {
    pendingOperations,
    addPendingOperation,
    removePendingOperation,
    syncPendingOperations,
    hasPendingOperations: pendingOperations.length > 0,
  };
};