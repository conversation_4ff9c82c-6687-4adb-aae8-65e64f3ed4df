import { useCallback, useEffect } from 'react';
import type { ApiError } from '../types';
import { errorHandler, type ErrorHandlerOptions } from '../utils/errorHandler';
import { useToast } from './useUI';

export interface UseErrorHandlerOptions extends ErrorHandlerOptions {
  autoHandle?: boolean;
}

export const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {
  const { showToast } = useToast();
  const { autoHandle = true, ...errorOptions } = options;

  // Set up toast handler for error handler
  useEffect(() => {
    if (autoHandle) {
      errorHandler.setToastHandler(showToast);
    }
  }, [autoHandle, showToast]);

  const handleError = useCallback((error: ApiError, customOptions?: ErrorHandlerOptions) => {
    const finalOptions = { ...errorOptions, ...customOptions };
    errorHandler.handleError(error, finalOptions);
  }, [errorOptions]);

  const handleNetworkError = useCallback(() => {
    errorHandler.handleNetworkError();
  }, []);

  const handleAuthError = useCallback(() => {
    errorHandler.handleAuthError();
  }, []);

  const handleValidationError = useCallback((message: string) => {
    errorHandler.handleValidationError(message);
  }, []);

  const handleServerError = useCallback(() => {
    errorHandler.handleServerError();
  }, []);

  const retryOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    retryOptions?: {
      maxRetries?: number;
      delay?: number;
      backoff?: 'linear' | 'exponential';
      retryCondition?: (error: ApiError) => boolean;
    }
  ): Promise<T> => {
    return errorHandler.retryOperation(operation, retryOptions);
  }, []);

  return {
    handleError,
    handleNetworkError,
    handleAuthError,
    handleValidationError,
    handleServerError,
    retryOperation,
  };
};

// Hook for handling async operations with error handling
export const useAsyncOperation = <T = any>() => {
  const { handleError } = useErrorHandler();

  const execute = useCallback(async (
    operation: () => Promise<T>,
    options?: {
      onSuccess?: (result: T) => void;
      onError?: (error: ApiError) => void;
      showErrorToast?: boolean;
      retryOptions?: {
        maxRetries?: number;
        delay?: number;
        backoff?: 'linear' | 'exponential';
      };
    }
  ): Promise<T | null> => {
    try {
      let result: T;
      
      if (options?.retryOptions) {
        result = await errorHandler.retryOperation(operation, options.retryOptions);
      } else {
        result = await operation();
      }

      if (options?.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error) {
      const apiError = error as ApiError;
      
      if (options?.onError) {
        options.onError(apiError);
      }

      if (options?.showErrorToast !== false) {
        handleError(apiError);
      }

      return null;
    }
  }, [handleError]);

  return { execute };
};

// Hook for handling form errors
export const useFormErrorHandler = () => {
  const { handleError } = useErrorHandler();

  const handleFormError = useCallback((error: ApiError) => {
    // Handle validation errors differently
    if (error.status === 400 && error.details?.errors) {
      // Return field-specific errors for form handling
      return error.details.errors;
    }

    // Handle other errors with toast
    handleError(error);
    return null;
  }, [handleError]);

  const getFieldError = useCallback((fieldErrors: Record<string, string[]> | null, fieldName: string): string | undefined => {
    if (!fieldErrors || !fieldErrors[fieldName]) {
      return undefined;
    }

    return fieldErrors[fieldName][0]; // Return first error for field
  }, []);

  return {
    handleFormError,
    getFieldError,
  };
};