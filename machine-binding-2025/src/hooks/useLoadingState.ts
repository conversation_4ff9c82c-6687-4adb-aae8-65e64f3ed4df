import { useState, useCallback, useRef } from 'react';

export interface LoadingState {
  isLoading: boolean;
  progress?: number;
  message?: string;
  error?: string;
}

export interface UseLoadingStateOptions {
  initialLoading?: boolean;
  initialMessage?: string;
  autoReset?: boolean;
  resetDelay?: number;
}

export const useLoadingState = (options: UseLoadingStateOptions = {}) => {
  const {
    initialLoading = false,
    initialMessage,
    autoReset = false,
    resetDelay = 3000,
  } = options;

  const [state, setState] = useState<LoadingState>({
    isLoading: initialLoading,
    message: initialMessage,
  });

  const resetTimeoutRef = useRef<NodeJS.Timeout>();

  const setLoading = useCallback((loading: boolean, message?: string) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
      message,
      error: loading ? undefined : prev.error, // Clear error when starting new loading
    }));
  }, []);

  const setProgress = useCallback((progress: number, message?: string) => {
    setState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
      message: message || prev.message,
    }));
  }, []);

  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
      progress: undefined,
    }));

    if (autoReset) {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
      resetTimeoutRef.current = setTimeout(() => {
        reset();
      }, resetDelay);
    }
  }, [autoReset, resetDelay]);

  const setSuccess = useCallback((message?: string) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      message,
      error: undefined,
      progress: 100,
    }));

    if (autoReset) {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
      resetTimeoutRef.current = setTimeout(() => {
        reset();
      }, resetDelay);
    }
  }, [autoReset, resetDelay]);

  const reset = useCallback(() => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = undefined;
    }
    setState({
      isLoading: false,
      progress: undefined,
      message: undefined,
      error: undefined,
    });
  }, []);

  const startLoading = useCallback((message?: string) => {
    setLoading(true, message);
  }, [setLoading]);

  const stopLoading = useCallback(() => {
    setLoading(false);
  }, [setLoading]);

  return {
    ...state,
    setLoading,
    setProgress,
    setError,
    setSuccess,
    reset,
    startLoading,
    stopLoading,
  };
};

// Hook for managing multiple loading states
export const useMultipleLoadingStates = () => {
  const [states, setStates] = useState<Record<string, LoadingState>>({});

  const setLoadingState = useCallback((key: string, state: Partial<LoadingState>) => {
    setStates(prev => ({
      ...prev,
      [key]: {
        isLoading: false,
        ...prev[key],
        ...state,
      },
    }));
  }, []);

  const startLoading = useCallback((key: string, message?: string) => {
    setLoadingState(key, { isLoading: true, message, error: undefined });
  }, [setLoadingState]);

  const stopLoading = useCallback((key: string) => {
    setLoadingState(key, { isLoading: false });
  }, [setLoadingState]);

  const setProgress = useCallback((key: string, progress: number, message?: string) => {
    setLoadingState(key, { progress, message });
  }, [setLoadingState]);

  const setError = useCallback((key: string, error: string) => {
    setLoadingState(key, { isLoading: false, error, progress: undefined });
  }, [setLoadingState]);

  const setSuccess = useCallback((key: string, message?: string) => {
    setLoadingState(key, { isLoading: false, message, error: undefined, progress: 100 });
  }, [setLoadingState]);

  const reset = useCallback((key: string) => {
    setStates(prev => {
      const newStates = { ...prev };
      delete newStates[key];
      return newStates;
    });
  }, []);

  const resetAll = useCallback(() => {
    setStates({});
  }, []);

  const getState = useCallback((key: string): LoadingState => {
    return states[key] || { isLoading: false };
  }, [states]);

  const isAnyLoading = useCallback((): boolean => {
    return Object.values(states).some(state => state.isLoading);
  }, [states]);

  const getLoadingKeys = useCallback((): string[] => {
    return Object.keys(states).filter(key => states[key].isLoading);
  }, [states]);

  return {
    states,
    setLoadingState,
    startLoading,
    stopLoading,
    setProgress,
    setError,
    setSuccess,
    reset,
    resetAll,
    getState,
    isAnyLoading,
    getLoadingKeys,
  };
};

// Hook for async operations with loading state
export const useAsyncWithLoading = <T = any>() => {
  const loadingState = useLoadingState();

  const execute = useCallback(async (
    asyncFn: () => Promise<T>,
    options?: {
      loadingMessage?: string;
      successMessage?: string;
      errorMessage?: string;
      onSuccess?: (result: T) => void;
      onError?: (error: Error) => void;
    }
  ): Promise<T | null> => {
    const {
      loadingMessage = '处理中...',
      successMessage = '操作成功',
      errorMessage,
      onSuccess,
      onError,
    } = options || {};

    try {
      loadingState.startLoading(loadingMessage);
      const result = await asyncFn();
      
      loadingState.setSuccess(successMessage);
      onSuccess?.(result);
      
      return result;
    } catch (error) {
      const errorMsg = errorMessage || (error as Error).message || '操作失败';
      loadingState.setError(errorMsg);
      onError?.(error as Error);
      
      return null;
    }
  }, [loadingState]);

  return {
    ...loadingState,
    execute,
  };
};

// Hook for upload progress
export const useUploadProgress = () => {
  const [uploads, setUploads] = useState<Record<string, {
    name: string;
    progress: number;
    status: 'uploading' | 'completed' | 'error';
    error?: string;
  }>>({});

  const startUpload = useCallback((id: string, name: string) => {
    setUploads(prev => ({
      ...prev,
      [id]: {
        name,
        progress: 0,
        status: 'uploading',
      },
    }));
  }, []);

  const updateProgress = useCallback((id: string, progress: number) => {
    setUploads(prev => ({
      ...prev,
      [id]: {
        ...prev[id],
        progress: Math.max(0, Math.min(100, progress)),
      },
    }));
  }, []);

  const completeUpload = useCallback((id: string) => {
    setUploads(prev => ({
      ...prev,
      [id]: {
        ...prev[id],
        progress: 100,
        status: 'completed',
      },
    }));
  }, []);

  const failUpload = useCallback((id: string, error: string) => {
    setUploads(prev => ({
      ...prev,
      [id]: {
        ...prev[id],
        status: 'error',
        error,
      },
    }));
  }, []);

  const removeUpload = useCallback((id: string) => {
    setUploads(prev => {
      const newUploads = { ...prev };
      delete newUploads[id];
      return newUploads;
    });
  }, []);

  const clearCompleted = useCallback(() => {
    setUploads(prev => {
      const newUploads: typeof prev = {};
      Object.keys(prev).forEach(id => {
        if (prev[id].status !== 'completed') {
          newUploads[id] = prev[id];
        }
      });
      return newUploads;
    });
  }, []);

  const clearAll = useCallback(() => {
    setUploads({});
  }, []);

  const getUploadList = useCallback(() => {
    return Object.keys(uploads).map(id => ({
      id,
      ...uploads[id],
    }));
  }, [uploads]);

  const getTotalProgress = useCallback(() => {
    const uploadList = Object.values(uploads);
    if (uploadList.length === 0) return 0;
    
    const totalProgress = uploadList.reduce((sum, upload) => sum + upload.progress, 0);
    return totalProgress / uploadList.length;
  }, [uploads]);

  const getCompletedCount = useCallback(() => {
    return Object.values(uploads).filter(upload => upload.status === 'completed').length;
  }, [uploads]);

  const getErrorCount = useCallback(() => {
    return Object.values(uploads).filter(upload => upload.status === 'error').length;
  }, [uploads]);

  const isAllCompleted = useCallback(() => {
    const uploadList = Object.values(uploads);
    return uploadList.length > 0 && uploadList.every(upload => upload.status === 'completed');
  }, [uploads]);

  const hasErrors = useCallback(() => {
    return Object.values(uploads).some(upload => upload.status === 'error');
  }, [uploads]);

  return {
    uploads: getUploadList(),
    startUpload,
    updateProgress,
    completeUpload,
    failUpload,
    removeUpload,
    clearCompleted,
    clearAll,
    getTotalProgress,
    getCompletedCount,
    getErrorCount,
    isAllCompleted,
    hasErrors,
  };
};