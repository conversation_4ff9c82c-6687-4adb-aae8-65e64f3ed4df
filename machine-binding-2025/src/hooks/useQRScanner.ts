import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from './useUI';
import { deviceService } from '../services';
import { qrCodeUtils } from '../utils';

export interface QRScanResult {
  qrCode: string;
  isValid: boolean;
  displayCode: string; // Last 20 characters for display
  fullCode: string; // Complete QR code
}

export const useQRScanner = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [isValidating, setIsValidating] = useState(false);

  // Validate QR code with backend
  const validateQRCode = useCallback(async (qrCode: string): Promise<QRScanResult> => {
    try {
      setIsValidating(true);
      
      // First validate format and business rules
      const formatValidation = qrCodeUtils.validateBusinessRules(qrCode);
      if (!formatValidation.isValid) {
        return {
          qrCode: formatValidation.code,
          isValid: false,
          displayCode: formatValidation.displayCode,
          fullCode: formatValidation.code,
        };
      }

      // Call API to validate QR code
      const response = await deviceService.validateQRCode(formatValidation.code);
      
      if (response.data.detail) {
        // QR code is invalid
        return {
          qrCode: formatValidation.code,
          isValid: false,
          displayCode: formatValidation.displayCode,
          fullCode: formatValidation.code,
        };
      }

      // Extract QR code from response URL
      const qrCodeUrl = response.data.qrcode_url;
      const extractedCode = qrCodeUtils.extractFromUrl(qrCodeUrl);

      return {
        qrCode: extractedCode,
        isValid: true,
        displayCode: qrCodeUtils.formatForDisplay(extractedCode),
        fullCode: extractedCode,
      };
    } catch (error) {
      console.error('QR code validation failed:', error);
      const sanitizedCode = qrCodeUtils.sanitize(qrCode);
      return {
        qrCode: sanitizedCode,
        isValid: false,
        displayCode: qrCodeUtils.formatForDisplay(sanitizedCode),
        fullCode: sanitizedCode,
      };
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Handle successful QR scan
  const handleQRScan = useCallback(async (scannedCode: string) => {
    if (!scannedCode.trim()) {
      showToast({
        type: 'error',
        title: '扫描失败',
        message: '二维码内容为空',
      });
      return;
    }

    showToast({
      type: 'info',
      title: '正在验证',
      message: '正在验证二维码...',
    });

    const result = await validateQRCode(scannedCode);

    if (result.isValid) {
      showToast({
        type: 'success',
        title: '扫描成功',
        message: '二维码验证通过，正在跳转...',
      });

      // Navigate to device binding page with QR code
      navigate(`/device-binding/${encodeURIComponent(result.fullCode)}`);
    } else {
      showToast({
        type: 'error',
        title: '二维码无效',
        message: '该二维码不存在或已失效，请检查后重试',
      });
    }
  }, [validateQRCode, navigate, showToast]);

  // Handle QR scan error
  const handleQRError = useCallback((error: string) => {
    console.error('QR scan error:', error);
    showToast({
      type: 'error',
      title: '扫描失败',
      message: error || '二维码扫描失败，请重试',
    });
  }, [showToast]);

  // Process QR code for display (show last 20 characters)
  const formatQRCodeForDisplay = useCallback((qrCode: string): string => {
    return qrCodeUtils.formatForDisplay(qrCode);
  }, []);

  // Extract QR code from URL
  const extractQRCodeFromUrl = useCallback((url: string): string => {
    return qrCodeUtils.extractFromUrl(url);
  }, []);

  return {
    isValidating,
    validateQRCode,
    handleQRScan,
    handleQRError,
    formatQRCodeForDisplay,
    extractQRCodeFromUrl,
  };
};