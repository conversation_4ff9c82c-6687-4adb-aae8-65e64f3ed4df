import { useContext } from 'react';
import { UIContext } from '../contexts/UIContext';

export const useUI = () => {
  const context = useContext(UIContext);
  if (context === undefined) {
    throw new Error('useUI must be used within a UIProvider');
  }
  return context;
};

// Convenience hooks for specific UI features
export const useToast = () => {
  const { showToast, hideToast, clearToasts, toasts } = useUI();
  return { showToast, hideToast, clearToasts, toasts };
};

export const useModal = (modalId: string) => {
  const { openModal, closeModal, isModalOpen } = useUI();
  return {
    isOpen: isModalOpen(modalId),
    open: () => openModal(modalId),
    close: () => closeModal(modalId),
  };
};

export const useLoading = () => {
  const { setLoading, clearLoading, loading } = useUI();
  return { setLoading, clearLoading, loading };
};