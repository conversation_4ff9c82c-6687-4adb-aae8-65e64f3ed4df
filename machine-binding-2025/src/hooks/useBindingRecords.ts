import { useState, useEffect, useCallback, useMemo } from 'react';
import { useToast } from './useUI';
import { deviceService } from '../services';
import { BindingRecord } from '../types';

export interface SearchFilters {
  query: string;
  status: 'all' | 'pending' | 'confirmed' | 'rejected';
  dateRange: 'all' | 'today' | 'week' | 'month';
  sortBy: 'date' | 'name' | 'code';
  sortOrder: 'asc' | 'desc';
}

export interface PaginationConfig {
  currentPage: number;
  recordsPerPage: number;
  totalRecords: number;
  totalPages: number;
}

export const useBindingRecords = (initialRecordsPerPage: number = 10) => {
  const { showToast } = useToast();

  // State management
  const [records, setRecords] = useState<BindingRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: '',
    status: 'all',
    dateRange: 'all',
    sortBy: 'date',
    sortOrder: 'desc',
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage] = useState(initialRecordsPerPage);

  // Load binding records from API
  const loadRecords = useCallback(async (showLoadingToast = false) => {
    try {
      setLoading(true);
      setError(null);

      if (showLoadingToast) {
        showToast({
          type: 'info',
          title: '正在刷新',
          message: '正在加载最新的绑定记录...',
        });
      }
      
      const response = await deviceService.getBindingRecords();
      const recordsData = response.data;

      // Transform data to match our interface
      const transformedRecords: BindingRecord[] = recordsData.map(record => ({
        id: record.id || Math.random().toString(),
        name: record.customer_name || record.name || '未知设备',
        time: deviceService.formatDate(record.created_at || record.time),
        code: record.serial_num || record.code || '',
        customer_name: record.customer_name,
        serial_num: record.serial_num,
        created_at: record.created_at,
      }));

      setRecords(transformedRecords);

      if (showLoadingToast) {
        showToast({
          type: 'success',
          title: '刷新成功',
          message: `已加载 ${transformedRecords.length} 条记录`,
        });
      }
    } catch (error) {
      console.error('Failed to load binding records:', error);
      const errorMessage = '加载绑定记录失败，请重试';
      setError(errorMessage);
      
      showToast({
        type: 'error',
        title: '加载失败',
        message: '无法加载绑定记录，请检查网络连接',
      });
    } finally {
      setLoading(false);
    }
  }, [showToast]);

  // Search records using API
  const searchRecords = useCallback(async (searchParams: { name?: string; state?: string }) => {
    try {
      setLoading(true);
      setError(null);

      const response = await deviceService.searchBindingRecords(searchParams);
      const recordsData = response.data;

      const transformedRecords: BindingRecord[] = recordsData.map(record => ({
        id: record.id || Math.random().toString(),
        name: record.customer_name || record.name || '未知设备',
        time: deviceService.formatDate(record.created_at || record.time),
        code: record.serial_num || record.code || '',
        customer_name: record.customer_name,
        serial_num: record.serial_num,
        created_at: record.created_at,
      }));

      setRecords(transformedRecords);
    } catch (error) {
      console.error('Failed to search binding records:', error);
      // Fall back to local filtering if API search fails
      loadRecords();
    } finally {
      setLoading(false);
    }
  }, [loadRecords]);

  // Filter and sort records
  const filteredAndSortedRecords = useMemo(() => {
    let filtered = [...records];

    // Text search filter
    if (searchFilters.query.trim()) {
      const query = searchFilters.query.toLowerCase();
      filtered = filtered.filter(record =>
        record.name.toLowerCase().includes(query) ||
        record.code.toLowerCase().includes(query) ||
        (record.customer_name && record.customer_name.toLowerCase().includes(query))
      );
    }

    // Date range filter
    if (searchFilters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (searchFilters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }

      filtered = filtered.filter(record => {
        const recordDate = new Date(record.created_at || record.time);
        return recordDate >= filterDate;
      });
    }

    // Sort records
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (searchFilters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'code':
          comparison = a.code.localeCompare(b.code);
          break;
        case 'date':
        default:
          const dateA = new Date(a.created_at || a.time);
          const dateB = new Date(b.created_at || b.time);
          comparison = dateA.getTime() - dateB.getTime();
          break;
      }

      return searchFilters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [records, searchFilters]);

  // Pagination calculations
  const paginationConfig: PaginationConfig = useMemo(() => {
    const totalRecords = filteredAndSortedRecords.length;
    const totalPages = Math.ceil(totalRecords / recordsPerPage);

    return {
      currentPage,
      recordsPerPage,
      totalRecords,
      totalPages,
    };
  }, [filteredAndSortedRecords.length, recordsPerPage, currentPage]);

  // Get current page records
  const currentPageRecords = useMemo(() => {
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    return filteredAndSortedRecords.slice(startIndex, endIndex);
  }, [filteredAndSortedRecords, currentPage, recordsPerPage]);

  // Update search filters
  const updateSearchFilters = useCallback((updates: Partial<SearchFilters>) => {
    setSearchFilters(prev => ({ ...prev, ...updates }));
    setCurrentPage(1); // Reset to first page when filters change
  }, []);

  // Handle search with debouncing
  const handleSearch = useCallback((query: string) => {
    updateSearchFilters({ query });
    
    // If query is not empty, try API search
    if (query.trim()) {
      const searchParams = { name: query.trim() };
      searchRecords(searchParams);
    }
  }, [updateSearchFilters, searchRecords]);

  // Handle pagination
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= paginationConfig.totalPages) {
      setCurrentPage(page);
    }
  }, [paginationConfig.totalPages]);

  const goToNextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  const goToPreviousPage = useCallback(() => {
    goToPage(currentPage - 1);
  }, [currentPage, goToPage]);

  // Refresh records
  const refreshRecords = useCallback(() => {
    loadRecords(true);
  }, [loadRecords]);

  // Clear search and filters
  const clearFilters = useCallback(() => {
    setSearchFilters({
      query: '',
      status: 'all',
      dateRange: 'all',
      sortBy: 'date',
      sortOrder: 'desc',
    });
    setCurrentPage(1);
  }, []);

  // Load records on mount
  useEffect(() => {
    loadRecords();
  }, [loadRecords]);

  // Statistics
  const statistics = useMemo(() => {
    const total = records.length;
    const filtered = filteredAndSortedRecords.length;
    
    return {
      total,
      filtered,
      hasFilters: searchFilters.query !== '' || 
                  searchFilters.status !== 'all' || 
                  searchFilters.dateRange !== 'all',
    };
  }, [records.length, filteredAndSortedRecords.length, searchFilters]);

  return {
    // Data
    records: currentPageRecords,
    allRecords: records,
    filteredRecords: filteredAndSortedRecords,
    
    // State
    loading,
    error,
    searchFilters,
    paginationConfig,
    statistics,
    
    // Actions
    loadRecords,
    refreshRecords,
    handleSearch,
    updateSearchFilters,
    clearFilters,
    
    // Pagination
    goToPage,
    goToNextPage,
    goToPreviousPage,
  };
};