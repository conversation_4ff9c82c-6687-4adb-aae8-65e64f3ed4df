import { useContext } from 'react';
import { ApiContext } from '../contexts/ApiContext';

export const useApi = () => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

// Custom hook for network status
export const useNetworkStatus = () => {
  const { isOnline } = useApi();
  return { isOnline };
};

// Custom hook for API errors
export const useApiError = () => {
  const { lastError, setError, clearError } = useApi();
  return { lastError, setError, clearError };
};

// Custom hook for request loading state
export const useRequestLoading = () => {
  const { requestCount } = useApi();
  return { isLoading: requestCount > 0 };
};