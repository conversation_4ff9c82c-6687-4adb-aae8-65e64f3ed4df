import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from './useUI';
import { deviceService, uploadService } from '../services';
import { DeviceBindingFormData, SAPDeviceData } from '../types';

export interface DeviceBindingSubmission {
  formData: DeviceBindingFormData;
  sapData?: SAPDeviceData[];
  selectedSapRecord?: SAPDeviceData;
}

export interface UploadedPhotos {
  nameplateUrls: string[];
  wholeUrls: string[];
  gpsUrls: string[];
}

export const useDeviceBinding = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Upload photos to server
  const uploadPhotos = useCallback(async (formData: DeviceBindingFormData): Promise<UploadedPhotos> => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const totalFiles = formData.nameplatePhotos.length + 
                        formData.wholePhotos.length + 
                        formData.gpsPhotos.length;
      let uploadedCount = 0;

      // Upload nameplate photos
      const nameplateUrls: string[] = [];
      for (const photo of formData.nameplatePhotos) {
        const response = await deviceService.uploadDeviceImage(photo, (progress) => {
          const overallProgress = ((uploadedCount + progress / 100) / totalFiles) * 100;
          setUploadProgress(Math.round(overallProgress));
        });
        nameplateUrls.push(response.data.url);
        uploadedCount++;
      }

      // Upload whole machine photos
      const wholeUrls: string[] = [];
      for (const photo of formData.wholePhotos) {
        const response = await deviceService.uploadDeviceImage(photo, (progress) => {
          const overallProgress = ((uploadedCount + progress / 100) / totalFiles) * 100;
          setUploadProgress(Math.round(overallProgress));
        });
        wholeUrls.push(response.data.url);
        uploadedCount++;
      }

      // Upload GPS photos
      const gpsUrls: string[] = [];
      for (const photo of formData.gpsPhotos) {
        const response = await deviceService.uploadDeviceImage(photo, (progress) => {
          const overallProgress = ((uploadedCount + progress / 100) / totalFiles) * 100;
          setUploadProgress(Math.round(overallProgress));
        });
        gpsUrls.push(response.data.url);
        uploadedCount++;
      }

      setUploadProgress(100);
      return { nameplateUrls, wholeUrls, gpsUrls };
    } finally {
      setIsUploading(false);
    }
  }, []);

  // Search SAP data for device
  const searchSAPData = useCallback(async (brand: string, serialNumber: string): Promise<SAPDeviceData[]> => {
    try {
      const response = await deviceService.searchSAPData(brand.toUpperCase(), serialNumber);
      return response.data;
    } catch (error) {
      console.error('SAP search failed:', error);
      throw new Error('SAP数据查询失败，请检查网络连接');
    }
  }, []);

  // Submit device binding
  const submitDeviceBinding = useCallback(async (
    formData: DeviceBindingFormData,
    sapData?: SAPDeviceData,
    uploadedPhotos?: UploadedPhotos
  ) => {
    try {
      // Process photo URLs for API submission
      const processedPhotos = uploadedPhotos ? {
        nameplate: deviceService.processPhotoUrls(uploadedPhotos.nameplateUrls).join(','),
        whole: deviceService.processPhotoUrls(uploadedPhotos.wholeUrls).join(','),
        pt_gps: deviceService.processPhotoUrls(uploadedPhotos.gpsUrls).join(','),
      } : {
        nameplate: '',
        whole: '',
        pt_gps: '',
      };

      const submissionData = {
        brand: formData.brand.toUpperCase(),
        serial_num: formData.serialNumber,
        qr_code_complete: formData.qrCode,
        ...processedPhotos,
        // Include SAP data if available
        ...(sapData && {
          fl_street: sapData.fl_street || '',
          equnr: sapData.equnr || '',
          pgroup_name: sapData.pgroup_name || '',
          sales_off: sapData.sales_off || '',
          plangroup: sapData.plangroup || '',
          enduser_name: sapData.enduser_name || '',
          enduser: sapData.enduser || '',
          inbdt: sapData.inbdt || '',
          typbz: sapData.typbz || '',
          end_date: sapData.end_date || '',
        }),
      };

      const response = await deviceService.submitBinding(submissionData);
      return response;
    } catch (error) {
      console.error('Device binding submission failed:', error);
      throw new Error('设备绑定提交失败，请重试');
    }
  }, []);

  // Complete device binding workflow
  const processDeviceBinding = useCallback(async (formData: DeviceBindingFormData) => {
    setIsSubmitting(true);

    try {
      // Step 1: Upload photos
      showToast({
        type: 'info',
        title: '正在上传照片',
        message: '请稍候，正在上传设备照片...',
      });

      const uploadedPhotos = await uploadPhotos(formData);

      // Step 2: Search SAP data
      showToast({
        type: 'info',
        title: '正在验证设备信息',
        message: '正在SAP系统中查询设备信息...',
      });

      const sapResults = await searchSAPData(formData.brand, formData.serialNumber);

      if (sapResults.length === 0) {
        showToast({
          type: 'error',
          title: 'SAP数据未找到',
          message: '请检查品牌和机身编号是否正确',
        });
        return { success: false, error: 'SAP数据未找到' };
      }

      // Step 3: Handle SAP results
      if (sapResults.length === 1) {
        // Single SAP record - submit directly
        showToast({
          type: 'info',
          title: '正在提交绑定申请',
          message: '设备信息验证成功，正在提交...',
        });

        await submitDeviceBinding(formData, sapResults[0], uploadedPhotos);

        showToast({
          type: 'success',
          title: '绑定申请提交成功',
          message: '设备绑定申请已提交，请等待审核',
        });

        // Navigate to success page or binding records
        navigate('/binding-records');
        return { success: true, sapData: sapResults };

      } else {
        // Multiple SAP records - need user selection
        showToast({
          type: 'warning',
          title: '找到多个匹配设备',
          message: '请选择正确的设备信息',
        });

        // Navigate to SAP selection page with data
        const queryParams = new URLSearchParams({
          qrCode: formData.qrCode,
          brand: formData.brand.toUpperCase(),
          serialNumber: formData.serialNumber,
          nameplateUrls: uploadedPhotos.nameplateUrls.join(','),
          wholeUrls: uploadedPhotos.wholeUrls.join(','),
          gpsUrls: uploadedPhotos.gpsUrls.join(','),
          sapData: JSON.stringify(sapResults),
        });

        navigate(`/device-binding/select?${queryParams.toString()}`);
        return { success: true, sapData: sapResults, requiresSelection: true };
      }

    } catch (error) {
      console.error('Device binding process failed:', error);
      const errorMessage = error instanceof Error ? error.message : '设备绑定失败，请重试';
      
      showToast({
        type: 'error',
        title: '绑定失败',
        message: errorMessage,
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsSubmitting(false);
    }
  }, [uploadPhotos, searchSAPData, submitDeviceBinding, navigate, showToast]);

  // Validate form before submission
  const validateFormForSubmission = useCallback((formData: DeviceBindingFormData): string[] => {
    const errors: string[] = [];

    if (!formData.qrCode) {
      errors.push('请扫描或输入二维码');
    }

    if (!formData.brand) {
      errors.push('请输入设备品牌');
    }

    if (!formData.serialNumber) {
      errors.push('请输入机身编号');
    }

    if (formData.nameplatePhotos.length === 0) {
      errors.push('请上传机台铁牌照片');
    }

    if (formData.wholePhotos.length === 0) {
      errors.push('请上传机台全身照片');
    }

    if (formData.gpsPhotos.length === 0) {
      errors.push('请上传GPS照片');
    }

    return errors;
  }, []);

  return {
    isSubmitting,
    isUploading,
    uploadProgress,
    processDeviceBinding,
    validateFormForSubmission,
    uploadPhotos,
    searchSAPData,
    submitDeviceBinding,
  };
};