// Breakpoints (Mobile-first approach)
$mobile: 320px;
$mobile-large: 480px;
$tablet: 768px;
$tablet-large: 1024px;
$desktop: 1200px;
$desktop-large: 1440px;

// Colors
$primary-color: #007bff;
$secondary-color: #6c757d;
$success-color: #28a745;
$danger-color: #dc3545;
$warning-color: #ffc107;
$info-color: #17a2b8;
$light-color: #f8f9fa;
$dark-color: #343a40;

// Additional color aliases for compatibility
$error-color: $danger-color;
$color-white: #ffffff;
$color-background: #f8f9fa;
$color-border: #dee2e6;

// Background colors
$background-primary: #ffffff;
$background-secondary: #f8f9fa;
$background-gray: #ededed;

// Text colors
$text-primary: #212529;
$text-secondary: #6c757d;
$text-muted: #868e96;

// Additional text color aliases for compatibility
$color-text-primary: $text-primary;
$color-text-secondary: $text-secondary;
$color-primary: $primary-color;
$color-success: $success-color;
$color-error: $danger-color;
$color-warning: $warning-color;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
$shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
$shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

// Typography
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Touch targets
$touch-target-min: 44px;
$touch-target-comfortable: 48px;

// Container widths
$container-mobile: 100%;
$container-tablet: 750px;
$container-desktop: 1140px;
$container-large: 1320px;

// Grid system
$grid-columns: 12;
$grid-gutter: $spacing-md;

// Component sizing
$button-height-sm: 32px;
$button-height-md: 40px;
$button-height-lg: 48px;

$input-height-sm: 32px;
$input-height-md: 40px;
$input-height-lg: 48px;

// Mobile-specific spacing
$spacing-mobile-xs: 2px;
$spacing-mobile-sm: 4px;
$spacing-mobile-md: 8px;
$spacing-mobile-lg: 12px;
$spacing-mobile-xl: 16px;

// Border and radius
$border-width: 1px;
$border-color: #dee2e6;
$border-color-light: #e9ecef;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;