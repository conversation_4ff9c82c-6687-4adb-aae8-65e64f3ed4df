@use 'sass:math';
@import './variables.scss';

// Reset and base styles
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: $text-primary;
  background-color: $background-gray;
  line-height: 1.5;
  min-height: 100vh;
}

// App container
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// Typography
h1, h2, h3, h4, h5, h6 {
  margin-bottom: $spacing-md;
  font-weight: $font-weight-semibold;
  line-height: 1.2;
}

h1 { font-size: $font-size-xxl; }
h2 { font-size: $font-size-xl; }
h3 { font-size: $font-size-lg; }
h4 { font-size: $font-size-md; }
h5 { font-size: $font-size-sm; }
h6 { font-size: $font-size-xs; }

p {
  margin-bottom: $spacing-md;
}

// Links
a {
  color: $primary-color;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

// Lists
ul, ol {
  margin-bottom: $spacing-md;
  padding-left: $spacing-lg;
}

li {
  margin-bottom: $spacing-xs;
}

// Images
img {
  max-width: 100%;
  height: auto;
}

// Buttons and interactive elements
button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// Touch targets - Mobile first
button, a, input, select, textarea {
  min-height: $touch-target-min;
  
  // Ensure interactive elements are touch-friendly
  @media (max-width: $tablet - 1px) {
    min-height: $touch-target-comfortable;
  }
}

// Clickable elements should have adequate spacing
button, a[role="button"] {
  min-width: $touch-target-min;
  padding: $spacing-sm $spacing-md;
  
  @media (max-width: $tablet - 1px) {
    min-width: $touch-target-comfortable;
    padding: $spacing-md $spacing-lg;
  }
}

// Focus styles
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

// Spacing utilities
.m-0 { margin: 0; }
.m-1 { margin: $spacing-xs; }
.m-2 { margin: $spacing-sm; }
.m-3 { margin: $spacing-md; }
.m-4 { margin: $spacing-lg; }
.m-5 { margin: $spacing-xl; }

.p-0 { padding: 0; }
.p-1 { padding: $spacing-xs; }
.p-2 { padding: $spacing-sm; }
.p-3 { padding: $spacing-md; }
.p-4 { padding: $spacing-lg; }
.p-5 { padding: $spacing-xl; }

// Container classes
.container {
  width: 100%;
  padding-left: $spacing-md;
  padding-right: $spacing-md;
  margin-left: auto;
  margin-right: auto;
  
  @media (min-width: $tablet) {
    max-width: $container-tablet;
    padding-left: $spacing-lg;
    padding-right: $spacing-lg;
  }
  
  @media (min-width: $desktop) {
    max-width: $container-desktop;
  }
  
  @media (min-width: $desktop-large) {
    max-width: $container-large;
  }
}

.container-fluid {
  width: 100%;
  padding-left: $spacing-md;
  padding-right: $spacing-md;
  
  @media (min-width: $tablet) {
    padding-left: $spacing-lg;
    padding-right: $spacing-lg;
  }
}

// Grid system
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -#{math.div($grid-gutter, 2)};
  margin-right: -#{math.div($grid-gutter, 2)};
}

.col {
  flex: 1;
  padding-left: #{math.div($grid-gutter, 2)};
  padding-right: #{math.div($grid-gutter, 2)};
}

// Generate responsive columns
@for $i from 1 through $grid-columns {
  .col-#{$i} {
    flex: 0 0 math.percentage(math.div($i, $grid-columns));
    max-width: math.percentage(math.div($i, $grid-columns));
    padding-left: #{math.div($grid-gutter, 2)};
    padding-right: #{math.div($grid-gutter, 2)};
  }
  
  @media (min-width: $tablet) {
    .col-md-#{$i} {
      flex: 0 0 math.percentage(math.div($i, $grid-columns));
      max-width: math.percentage(math.div($i, $grid-columns));
    }
  }
  
  @media (min-width: $desktop) {
    .col-lg-#{$i} {
      flex: 0 0 math.percentage(math.div($i, $grid-columns));
      max-width: math.percentage(math.div($i, $grid-columns));
    }
  }
}

// Responsive display utilities
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

@media (max-width: $tablet - 1px) {
  .d-mobile-none { display: none !important; }
  .d-mobile-block { display: block !important; }
  .d-mobile-flex { display: flex !important; }
  .d-mobile-inline { display: inline !important; }
  .d-mobile-inline-block { display: inline-block !important; }
}

@media (min-width: $tablet) {
  .d-tablet-none { display: none !important; }
  .d-tablet-block { display: block !important; }
  .d-tablet-flex { display: flex !important; }
  .d-tablet-inline { display: inline !important; }
  .d-tablet-inline-block { display: inline-block !important; }
}

@media (min-width: $desktop) {
  .d-desktop-none { display: none !important; }
  .d-desktop-block { display: block !important; }
  .d-desktop-flex { display: flex !important; }
  .d-desktop-inline { display: inline !important; }
  .d-desktop-inline-block { display: inline-block !important; }
}

// Responsive text alignment
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

@media (max-width: $tablet - 1px) {
  .text-mobile-left { text-align: left !important; }
  .text-mobile-center { text-align: center !important; }
  .text-mobile-right { text-align: right !important; }
}

@media (min-width: $tablet) {
  .text-tablet-left { text-align: left !important; }
  .text-tablet-center { text-align: center !important; }
  .text-tablet-right { text-align: right !important; }
}

// Responsive spacing utilities
@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in (0: 0, 1: $spacing-xs, 2: $spacing-sm, 3: $spacing-md, 4: $spacing-lg, 5: $spacing-xl) {
    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }
    .#{$abbrev}t-#{$size} { #{$prop}-top: $length !important; }
    .#{$abbrev}r-#{$size} { #{$prop}-right: $length !important; }
    .#{$abbrev}b-#{$size} { #{$prop}-bottom: $length !important; }
    .#{$abbrev}l-#{$size} { #{$prop}-left: $length !important; }
    .#{$abbrev}x-#{$size} { 
      #{$prop}-left: $length !important;
      #{$prop}-right: $length !important;
    }
    .#{$abbrev}y-#{$size} { 
      #{$prop}-top: $length !important;
      #{$prop}-bottom: $length !important;
    }
    
    // Mobile-specific spacing
    @media (max-width: $tablet - 1px) {
      .#{$abbrev}-mobile-#{$size} { #{$prop}: $length !important; }
      .#{$abbrev}t-mobile-#{$size} { #{$prop}-top: $length !important; }
      .#{$abbrev}r-mobile-#{$size} { #{$prop}-right: $length !important; }
      .#{$abbrev}b-mobile-#{$size} { #{$prop}-bottom: $length !important; }
      .#{$abbrev}l-mobile-#{$size} { #{$prop}-left: $length !important; }
      .#{$abbrev}x-mobile-#{$size} { 
        #{$prop}-left: $length !important;
        #{$prop}-right: $length !important;
      }
      .#{$abbrev}y-mobile-#{$size} { 
        #{$prop}-top: $length !important;
        #{$prop}-bottom: $length !important;
      }
    }
    
    // Tablet and up spacing
    @media (min-width: $tablet) {
      .#{$abbrev}-tablet-#{$size} { #{$prop}: $length !important; }
      .#{$abbrev}t-tablet-#{$size} { #{$prop}-top: $length !important; }
      .#{$abbrev}r-tablet-#{$size} { #{$prop}-right: $length !important; }
      .#{$abbrev}b-tablet-#{$size} { #{$prop}-bottom: $length !important; }
      .#{$abbrev}l-tablet-#{$size} { #{$prop}-left: $length !important; }
      .#{$abbrev}x-tablet-#{$size} { 
        #{$prop}-left: $length !important;
        #{$prop}-right: $length !important;
      }
      .#{$abbrev}y-tablet-#{$size} { 
        #{$prop}-top: $length !important;
        #{$prop}-bottom: $length !important;
      }
    }
  }
}

// Responsive flexbox utilities
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }
.align-center { align-items: center !important; }
.align-baseline { align-items: baseline !important; }
.align-stretch { align-items: stretch !important; }

@media (max-width: $tablet - 1px) {
  .flex-mobile-column { flex-direction: column !important; }
  .flex-mobile-row { flex-direction: row !important; }
  .justify-mobile-start { justify-content: flex-start !important; }
  .justify-mobile-center { justify-content: center !important; }
  .justify-mobile-between { justify-content: space-between !important; }
  .align-mobile-start { align-items: flex-start !important; }
  .align-mobile-center { align-items: center !important; }
  .align-mobile-end { align-items: flex-end !important; }
}

// Mobile-first typography scaling
@media (max-width: $tablet - 1px) {
  h1 { font-size: $font-size-xl; }
  h2 { font-size: $font-size-lg; }
  h3 { font-size: $font-size-md; }
  
  // Ensure text is readable on mobile
  body {
    font-size: $font-size-md;
    line-height: 1.6;
  }
}

// Touch-friendly form elements
@media (max-width: $tablet - 1px) {
  input, select, textarea {
    font-size: 16px; // Prevents zoom on iOS
    padding: $spacing-md;
  }
  
  button {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-md;
  }
}

// Enhanced orientation-specific utilities
@media (orientation: portrait) {
  .portrait-only { display: block !important; }
  .landscape-only { display: none !important; }
  
  .portrait-stack {
    flex-direction: column !important;
    gap: $spacing-md !important;
  }
  
  .portrait-center {
    text-align: center !important;
    align-items: center !important;
  }
  
  .portrait-full-width {
    width: 100% !important;
  }
  
  .portrait-grid-1 {
    grid-template-columns: 1fr !important;
  }
  
  .portrait-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  // Optimize spacing for portrait
  .portrait-spacing-compact {
    gap: $spacing-sm !important;
    padding: $spacing-sm !important;
  }
  
  .portrait-spacing-comfortable {
    gap: $spacing-lg !important;
    padding: $spacing-lg !important;
  }
}

@media (orientation: landscape) {
  .portrait-only { display: none !important; }
  .landscape-only { display: block !important; }
  
  .landscape-row {
    flex-direction: row !important;
    gap: $spacing-md !important;
  }
  
  .landscape-spread {
    justify-content: space-between !important;
  }
  
  .landscape-center {
    justify-content: center !important;
    align-items: center !important;
  }
  
  .landscape-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .landscape-grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }
  
  .landscape-grid-4 {
    grid-template-columns: repeat(4, 1fr) !important;
  }
  
  // Optimize spacing for landscape
  .landscape-spacing-compact {
    gap: $spacing-xs !important;
    padding: $spacing-xs $spacing-md !important;
  }
  
  .landscape-spacing-comfortable {
    gap: $spacing-md !important;
    padding: $spacing-md $spacing-lg !important;
  }
}

// Enhanced device-specific utilities
@media (max-width: 479px) {
  .phone-only { display: block !important; }
  .phone-large-up-only { display: none !important; }
  .tablet-up-only { display: none !important; }
  
  .phone-full-width {
    width: 100% !important;
  }
  
  .phone-center {
    text-align: center !important;
  }
  
  .phone-stack {
    flex-direction: column !important;
    gap: $spacing-sm !important;
  }
  
  .phone-grid-1 {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: $spacing-sm !important;
  }
  
  // Phone-specific spacing
  .phone-padding-sm {
    padding: $spacing-sm !important;
  }
  
  .phone-padding-md {
    padding: $spacing-md !important;
  }
  
  // Phone-specific typography
  .phone-text-sm {
    font-size: $font-size-sm !important;
  }
  
  .phone-text-md {
    font-size: $font-size-md !important;
  }
}

@media (min-width: 480px) and (max-width: 767px) {
  .phone-only { display: none !important; }
  .phone-large-only { display: block !important; }
  .tablet-up-only { display: none !important; }
  
  .phone-large-grid-2 {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: $spacing-md !important;
  }
  
  .phone-large-flex {
    display: flex !important;
    gap: $spacing-md !important;
  }
  
  .phone-large-padding {
    padding: $spacing-md !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .phone-only { display: none !important; }
  .phone-large-only { display: none !important; }
  .tablet-small-only { display: block !important; }
  .tablet-large-up-only { display: none !important; }
  
  .tablet-small-grid-2 {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: $spacing-lg !important;
  }
  
  .tablet-small-grid-3 {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: $spacing-lg !important;
  }
  
  .tablet-small-flex {
    display: flex !important;
    gap: $spacing-lg !important;
  }
}

@media (min-width: 1024px) {
  .phone-only { display: none !important; }
  .phone-large-only { display: none !important; }
  .tablet-small-only { display: none !important; }
  .tablet-large-up-only { display: block !important; }
  
  .tablet-large-grid-3 {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: $spacing-xl !important;
  }
  
  .tablet-large-grid-4 {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: $spacing-xl !important;
  }
  
  .tablet-large-flex {
    display: flex !important;
    gap: $spacing-xl !important;
  }
}

// Touch vs hover device optimizations
@media (hover: none) and (pointer: coarse) {
  // Touch devices
  .touch-optimized {
    padding: $spacing-md !important;
    min-height: $touch-target-comfortable !important;
  }
  
  .hover-effects {
    display: none !important;
  }
  
  // Remove hover states on touch devices
  button:hover,
  a:hover,
  .interactive:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

@media (hover: hover) and (pointer: fine) {
  // Hover-capable devices
  .hover-effects {
    display: block !important;
  }
  
  .touch-only {
    display: none !important;
  }
}

// High DPI optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-optimized {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  // Thinner borders on high DPI displays
  .thin-border {
    border-width: 0.5px !important;
  }
}

// Enhanced adaptive spacing based on screen size and orientation
@media (max-width: 479px) and (orientation: portrait) {
  .adaptive-spacing {
    padding: $spacing-sm !important;
    margin: $spacing-sm 0 !important;
  }
  
  .adaptive-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: $spacing-sm !important;
  }
  
  .adaptive-flex {
    display: flex !important;
    flex-direction: column !important;
    gap: $spacing-sm !important;
  }
  
  .adaptive-container {
    padding: $spacing-sm !important;
    margin: 0 !important;
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-spacing {
    padding: $spacing-xs $spacing-md !important;
    margin: $spacing-xs 0 !important;
  }
  
  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: $spacing-sm !important;
  }
  
  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: $spacing-sm !important;
  }
  
  .adaptive-container {
    padding: $spacing-xs $spacing-md !important;
    margin: 0 !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
  .adaptive-spacing {
    padding: $spacing-lg !important;
    margin: $spacing-lg 0 !important;
  }
  
  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: $spacing-lg !important;
  }
  
  .adaptive-flex {
    display: flex !important;
    flex-direction: column !important;
    gap: $spacing-lg !important;
  }
  
  .adaptive-container {
    padding: $spacing-lg !important;
    max-width: 600px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) and (orientation: landscape) {
  .adaptive-spacing {
    padding: $spacing-md $spacing-xl !important;
    margin: $spacing-md 0 !important;
  }
  
  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: $spacing-lg !important;
  }
  
  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: $spacing-lg !important;
  }
  
  .adaptive-container {
    padding: $spacing-md $spacing-xl !important;
    max-width: 900px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 1024px) and (orientation: portrait) {
  .adaptive-spacing {
    padding: $spacing-xl !important;
    margin: $spacing-xl 0 !important;
  }
  
  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: $spacing-xl !important;
  }
  
  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: $spacing-xl !important;
  }
  
  .adaptive-container {
    padding: $spacing-xl !important;
    max-width: 800px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 1024px) and (orientation: landscape) {
  .adaptive-spacing {
    padding: $spacing-lg $spacing-xxl !important;
    margin: $spacing-lg 0 !important;
  }
  
  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: $spacing-xl !important;
  }
  
  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: $spacing-xl !important;
  }
  
  .adaptive-container {
    padding: $spacing-lg $spacing-xxl !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
  }
}

// Device and orientation specific form layouts
@media (max-width: 767px) and (orientation: portrait) {
  .adaptive-form {
    .form-row {
      flex-direction: column !important;
      gap: $spacing-md !important;
    }
    
    .form-group {
      width: 100% !important;
    }
    
    .form-actions {
      flex-direction: column !important;
      gap: $spacing-sm !important;
      
      button {
        width: 100% !important;
      }
    }
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-form {
    .form-row {
      flex-direction: row !important;
      gap: $spacing-sm !important;
    }
    
    .form-group {
      flex: 1 !important;
      min-width: 150px !important;
    }
    
    .form-actions {
      flex-direction: row !important;
      gap: $spacing-sm !important;
      justify-content: flex-end !important;
      
      button {
        min-width: 100px !important;
      }
    }
  }
}

@media (min-width: 768px) {
  .adaptive-form {
    .form-row {
      flex-direction: row !important;
      gap: $spacing-lg !important;
    }
    
    .form-group {
      flex: 1 !important;
      min-width: 200px !important;
    }
    
    .form-actions {
      flex-direction: row !important;
      gap: $spacing-md !important;
      justify-content: flex-end !important;
    }
  }
}

// Adaptive image and media layouts
@media (max-width: 767px) and (orientation: portrait) {
  .adaptive-media {
    .image-grid {
      grid-template-columns: 1fr !important;
      gap: $spacing-md !important;
    }
    
    .image-item {
      aspect-ratio: 16/9 !important;
    }
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-media {
    .image-grid {
      grid-template-columns: repeat(2, 1fr) !important;
      gap: $spacing-sm !important;
    }
    
    .image-item {
      aspect-ratio: 4/3 !important;
    }
  }
}

@media (min-width: 768px) {
  .adaptive-media {
    .image-grid {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: $spacing-lg !important;
    }
    
    .image-item {
      aspect-ratio: 1/1 !important;
    }
  }
}

// Adaptive navigation layouts
@media (max-width: 767px) and (orientation: portrait) {
  .adaptive-nav {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    flex-direction: row !important;
    justify-content: space-around !important;
    padding: $spacing-sm !important;
    background: rgba(white, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-top: 1px solid $border-color-light !important;
    z-index: $z-index-fixed !important;
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-nav {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    padding: $spacing-xs $spacing-md !important;
    background: rgba(white, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid $border-color-light !important;
    z-index: $z-index-fixed !important;
    height: 56px !important;
  }
}

@media (min-width: 768px) {
  .adaptive-nav {
    position: relative !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    padding: $spacing-md $spacing-lg !important;
    background: white !important;
    border-bottom: 1px solid $border-color-light !important;
  }
}