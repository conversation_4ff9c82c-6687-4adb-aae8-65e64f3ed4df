@use 'sass:math';
@use './variables' as vars;

// Reset and base styles
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: vars.$text-primary;
  background-color: vars.$background-gray;
  line-height: 1.5;
  min-height: 100vh;
}

// App container
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: vars.$spacing-md;
  font-weight: vars.$font-weight-semibold;
  line-height: 1.2;
}

h1 {
  font-size: vars.$font-size-xxl;
}

h2 {
  font-size: vars.$font-size-xl;
}

h3 {
  font-size: vars.$font-size-lg;
}

h4 {
  font-size: vars.$font-size-md;
}

h5 {
  font-size: vars.$font-size-sm;
}

h6 {
  font-size: vars.$font-size-xs;
}

p {
  margin-bottom: vars.$spacing-md;
}

// Links
a {
  color: vars.$primary-color;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// Lists
ul,
ol {
  margin-bottom: vars.$spacing-md;
  padding-left: vars.$spacing-lg;
}

li {
  margin-bottom: vars.$spacing-xs;
}

// Images
img {
  max-width: 100%;
  height: auto;
}

// Buttons and interactive elements
button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// Touch targets - Mobile first
button,
a,
input,
select,
textarea {
  min-height: vars.$touch-target-min;

  // Ensure interactive elements are touch-friendly
  @media (max-width: vars.$tablet - 1px) {
    min-height: vars.$touch-target-comfortable;
  }
}

// Clickable elements should have adequate spacing
button,
a[role="button"] {
  min-width: vars.$touch-target-min;
  padding: vars.$spacing-sm vars.$spacing-md;

  @media (max-width: vars.$tablet - 1px) {
    min-width: vars.$touch-target-comfortable;
    padding: vars.$spacing-md vars.$spacing-lg;
  }
}

// Focus styles
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
  outline: 2px solid vars.$primary-color;
  outline-offset: 2px;
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.d-block {
  display: block;
}

.d-none {
  display: none;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

// Spacing utilities
.m-0 {
  margin: 0;
}

.m-1 {
  margin: vars.$spacing-xs;
}

.m-2 {
  margin: vars.$spacing-sm;
}

.m-3 {
  margin: vars.$spacing-md;
}

.m-4 {
  margin: vars.$spacing-lg;
}

.m-5 {
  margin: vars.$spacing-xl;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: vars.$spacing-xs;
}

.p-2 {
  padding: vars.$spacing-sm;
}

.p-3 {
  padding: vars.$spacing-md;
}

.p-4 {
  padding: vars.$spacing-lg;
}

.p-5 {
  padding: vars.$spacing-xl;
}

// Container classes
.container {
  width: 100%;
  padding-left: vars.$spacing-md;
  padding-right: vars.$spacing-md;
  margin-left: auto;
  margin-right: auto;

  @media (min-width: vars.$tablet) {
    max-width: vars.$container-tablet;
    padding-left: vars.$spacing-lg;
    padding-right: vars.$spacing-lg;
  }

  @media (min-width: vars.$desktop) {
    max-width: vars.$container-desktop;
  }

  @media (min-width: vars.$desktop-large) {
    max-width: vars.$container-large;
  }
}

.container-fluid {
  width: 100%;
  padding-left: vars.$spacing-md;
  padding-right: vars.$spacing-md;

  @media (min-width: vars.$tablet) {
    padding-left: vars.$spacing-lg;
    padding-right: vars.$spacing-lg;
  }
}

// Grid system
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -#{math.div(vars.$grid-gutter, 2)};
  margin-right: -#{math.div(vars.$grid-gutter, 2)};
}

.col {
  flex: 1;
  padding-left: #{math.div(vars.$grid-gutter, 2)};
  padding-right: #{math.div(vars.$grid-gutter, 2)};
}

// Generate responsive columns
@for vars.$i from 1 through vars.$grid-columns {
  .col-#{vars.$i} {
    flex: 0 0 math.percentage(math.div(vars.$i, vars.$grid-columns));
    max-width: math.percentage(math.div(vars.$i, vars.$grid-columns));
    padding-left: #{math.div(vars.$grid-gutter, 2)};
    padding-right: #{math.div(vars.$grid-gutter, 2)};
  }

  @media (min-width: vars.$tablet) {
    .col-md-#{vars.$i} {
      flex: 0 0 math.percentage(math.div(vars.$i, vars.$grid-columns));
      max-width: math.percentage(math.div(vars.$i, vars.$grid-columns));
    }
  }

  @media (min-width: vars.$desktop) {
    .col-lg-#{vars.$i} {
      flex: 0 0 math.percentage(math.div(vars.$i, vars.$grid-columns));
      max-width: math.percentage(math.div(vars.$i, vars.$grid-columns));
    }
  }
}

// Responsive display utilities
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

@media (max-width: vars.$tablet - 1px) {
  .d-mobile-none {
    display: none !important;
  }

  .d-mobile-block {
    display: block !important;
  }

  .d-mobile-flex {
    display: flex !important;
  }

  .d-mobile-inline {
    display: inline !important;
  }

  .d-mobile-inline-block {
    display: inline-block !important;
  }
}

@media (min-width: vars.$tablet) {
  .d-tablet-none {
    display: none !important;
  }

  .d-tablet-block {
    display: block !important;
  }

  .d-tablet-flex {
    display: flex !important;
  }

  .d-tablet-inline {
    display: inline !important;
  }

  .d-tablet-inline-block {
    display: inline-block !important;
  }
}

@media (min-width: vars.$desktop) {
  .d-desktop-none {
    display: none !important;
  }

  .d-desktop-block {
    display: block !important;
  }

  .d-desktop-flex {
    display: flex !important;
  }

  .d-desktop-inline {
    display: inline !important;
  }

  .d-desktop-inline-block {
    display: inline-block !important;
  }
}

// Responsive text alignment
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

@media (max-width: vars.$tablet - 1px) {
  .text-mobile-left {
    text-align: left !important;
  }

  .text-mobile-center {
    text-align: center !important;
  }

  .text-mobile-right {
    text-align: right !important;
  }
}

@media (min-width: vars.$tablet) {
  .text-tablet-left {
    text-align: left !important;
  }

  .text-tablet-center {
    text-align: center !important;
  }

  .text-tablet-right {
    text-align: right !important;
  }
}

// Responsive spacing utilities
@each vars.$prop, vars.$abbrev in (margin: m, padding: p) {
  @each vars.$size, vars.$length in (0: 0, 1: vars.$spacing-xs, 2: vars.$spacing-sm, 3: vars.$spacing-md, 4: vars.$spacing-lg, 5: vars.$spacing-xl) {
    .#{vars.$abbrev}-#{vars.$size} {
      #{vars.$prop}: vars.$length !important;
    }

    .#{vars.$abbrev}t-#{vars.$size} {
      #{vars.$prop}-top: vars.$length !important;
    }

    .#{vars.$abbrev}r-#{vars.$size} {
      #{vars.$prop}-right: vars.$length !important;
    }

    .#{vars.$abbrev}b-#{vars.$size} {
      #{vars.$prop}-bottom: vars.$length !important;
    }

    .#{vars.$abbrev}l-#{vars.$size} {
      #{vars.$prop}-left: vars.$length !important;
    }

    .#{vars.$abbrev}x-#{vars.$size} {
      #{vars.$prop}-left: vars.$length !important;
      #{vars.$prop}-right: vars.$length !important;
    }

    .#{vars.$abbrev}y-#{vars.$size} {
      #{vars.$prop}-top: vars.$length !important;
      #{vars.$prop}-bottom: vars.$length !important;
    }

    // Mobile-specific spacing
    @media (max-width: vars.$tablet - 1px) {
      .#{vars.$abbrev}-mobile-#{vars.$size} {
        #{vars.$prop}: vars.$length !important;
      }

      .#{vars.$abbrev}t-mobile-#{vars.$size} {
        #{vars.$prop}-top: vars.$length !important;
      }

      .#{vars.$abbrev}r-mobile-#{vars.$size} {
        #{vars.$prop}-right: vars.$length !important;
      }

      .#{vars.$abbrev}b-mobile-#{vars.$size} {
        #{vars.$prop}-bottom: vars.$length !important;
      }

      .#{vars.$abbrev}l-mobile-#{vars.$size} {
        #{vars.$prop}-left: vars.$length !important;
      }

      .#{vars.$abbrev}x-mobile-#{vars.$size} {
        #{vars.$prop}-left: vars.$length !important;
        #{vars.$prop}-right: vars.$length !important;
      }

      .#{vars.$abbrev}y-mobile-#{vars.$size} {
        #{vars.$prop}-top: vars.$length !important;
        #{vars.$prop}-bottom: vars.$length !important;
      }
    }

    // Tablet and up spacing
    @media (min-width: vars.$tablet) {
      .#{vars.$abbrev}-tablet-#{vars.$size} {
        #{vars.$prop}: vars.$length !important;
      }

      .#{vars.$abbrev}t-tablet-#{vars.$size} {
        #{vars.$prop}-top: vars.$length !important;
      }

      .#{vars.$abbrev}r-tablet-#{vars.$size} {
        #{vars.$prop}-right: vars.$length !important;
      }

      .#{vars.$abbrev}b-tablet-#{vars.$size} {
        #{vars.$prop}-bottom: vars.$length !important;
      }

      .#{vars.$abbrev}l-tablet-#{vars.$size} {
        #{vars.$prop}-left: vars.$length !important;
      }

      .#{vars.$abbrev}x-tablet-#{vars.$size} {
        #{vars.$prop}-left: vars.$length !important;
        #{vars.$prop}-right: vars.$length !important;
      }

      .#{vars.$abbrev}y-tablet-#{vars.$size} {
        #{vars.$prop}-top: vars.$length !important;
        #{vars.$prop}-bottom: vars.$length !important;
      }
    }
  }
}

// Responsive flexbox utilities
.flex-column {
  flex-direction: column !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-start {
  justify-content: flex-start !important;
}

.justify-end {
  justify-content: flex-end !important;
}

.justify-center {
  justify-content: center !important;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-around {
  justify-content: space-around !important;
}

.justify-evenly {
  justify-content: space-evenly !important;
}

.align-start {
  align-items: flex-start !important;
}

.align-end {
  align-items: flex-end !important;
}

.align-center {
  align-items: center !important;
}

.align-baseline {
  align-items: baseline !important;
}

.align-stretch {
  align-items: stretch !important;
}

@media (max-width: vars.$tablet - 1px) {
  .flex-mobile-column {
    flex-direction: column !important;
  }

  .flex-mobile-row {
    flex-direction: row !important;
  }

  .justify-mobile-start {
    justify-content: flex-start !important;
  }

  .justify-mobile-center {
    justify-content: center !important;
  }

  .justify-mobile-between {
    justify-content: space-between !important;
  }

  .align-mobile-start {
    align-items: flex-start !important;
  }

  .align-mobile-center {
    align-items: center !important;
  }

  .align-mobile-end {
    align-items: flex-end !important;
  }
}

// Mobile-first typography scaling
@media (max-width: vars.$tablet - 1px) {
  h1 {
    font-size: vars.$font-size-xl;
  }

  h2 {
    font-size: vars.$font-size-lg;
  }

  h3 {
    font-size: vars.$font-size-md;
  }

  // Ensure text is readable on mobile
  body {
    font-size: vars.$font-size-md;
    line-height: 1.6;
  }
}

// Touch-friendly form elements
@media (max-width: vars.$tablet - 1px) {

  input,
  select,
  textarea {
    font-size: 16px; // Prevents zoom on iOS
    padding: vars.$spacing-md;
  }

  button {
    padding: vars.$spacing-md vars.$spacing-lg;
    font-size: vars.$font-size-md;
  }
}

// Enhanced orientation-specific utilities
@media (orientation: portrait) {
  .portrait-only {
    display: block !important;
  }

  .landscape-only {
    display: none !important;
  }

  .portrait-stack {
    flex-direction: column !important;
    gap: vars.$spacing-md !important;
  }

  .portrait-center {
    text-align: center !important;
    align-items: center !important;
  }

  .portrait-full-width {
    width: 100% !important;
  }

  .portrait-grid-1 {
    grid-template-columns: 1fr !important;
  }

  .portrait-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  // Optimize spacing for portrait
  .portrait-spacing-compact {
    gap: vars.$spacing-sm !important;
    padding: vars.$spacing-sm !important;
  }

  .portrait-spacing-comfortable {
    gap: vars.$spacing-lg !important;
    padding: vars.$spacing-lg !important;
  }
}

@media (orientation: landscape) {
  .portrait-only {
    display: none !important;
  }

  .landscape-only {
    display: block !important;
  }

  .landscape-row {
    flex-direction: row !important;
    gap: vars.$spacing-md !important;
  }

  .landscape-spread {
    justify-content: space-between !important;
  }

  .landscape-center {
    justify-content: center !important;
    align-items: center !important;
  }

  .landscape-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .landscape-grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .landscape-grid-4 {
    grid-template-columns: repeat(4, 1fr) !important;
  }

  // Optimize spacing for landscape
  .landscape-spacing-compact {
    gap: vars.$spacing-xs !important;
    padding: vars.$spacing-xs vars.$spacing-md !important;
  }

  .landscape-spacing-comfortable {
    gap: vars.$spacing-md !important;
    padding: vars.$spacing-md vars.$spacing-lg !important;
  }
}

// Enhanced device-specific utilities
@media (max-width: 479px) {
  .phone-only {
    display: block !important;
  }

  .phone-large-up-only {
    display: none !important;
  }

  .tablet-up-only {
    display: none !important;
  }

  .phone-full-width {
    width: 100% !important;
  }

  .phone-center {
    text-align: center !important;
  }

  .phone-stack {
    flex-direction: column !important;
    gap: vars.$spacing-sm !important;
  }

  .phone-grid-1 {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: vars.$spacing-sm !important;
  }

  // Phone-specific spacing
  .phone-padding-sm {
    padding: vars.$spacing-sm !important;
  }

  .phone-padding-md {
    padding: vars.$spacing-md !important;
  }

  // Phone-specific typography
  .phone-text-sm {
    font-size: vars.$font-size-sm !important;
  }

  .phone-text-md {
    font-size: vars.$font-size-md !important;
  }
}

@media (min-width: 480px) and (max-width: 767px) {
  .phone-only {
    display: none !important;
  }

  .phone-large-only {
    display: block !important;
  }

  .tablet-up-only {
    display: none !important;
  }

  .phone-large-grid-2 {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: vars.$spacing-md !important;
  }

  .phone-large-flex {
    display: flex !important;
    gap: vars.$spacing-md !important;
  }

  .phone-large-padding {
    padding: vars.$spacing-md !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .phone-only {
    display: none !important;
  }

  .phone-large-only {
    display: none !important;
  }

  .tablet-small-only {
    display: block !important;
  }

  .tablet-large-up-only {
    display: none !important;
  }

  .tablet-small-grid-2 {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: vars.$spacing-lg !important;
  }

  .tablet-small-grid-3 {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: vars.$spacing-lg !important;
  }

  .tablet-small-flex {
    display: flex !important;
    gap: vars.$spacing-lg !important;
  }
}

@media (min-width: 1024px) {
  .phone-only {
    display: none !important;
  }

  .phone-large-only {
    display: none !important;
  }

  .tablet-small-only {
    display: none !important;
  }

  .tablet-large-up-only {
    display: block !important;
  }

  .tablet-large-grid-3 {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: vars.$spacing-xl !important;
  }

  .tablet-large-grid-4 {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: vars.$spacing-xl !important;
  }

  .tablet-large-flex {
    display: flex !important;
    gap: vars.$spacing-xl !important;
  }
}

// Touch vs hover device optimizations
@media (hover: none) and (pointer: coarse) {

  // Touch devices
  .touch-optimized {
    padding: vars.$spacing-md !important;
    min-height: vars.$touch-target-comfortable !important;
  }

  .hover-effects {
    display: none !important;
  }

  // Remove hover states on touch devices
  button:hover,
  a:hover,
  .interactive:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

@media (hover: hover) and (pointer: fine) {

  // Hover-capable devices
  .hover-effects {
    display: block !important;
  }

  .touch-only {
    display: none !important;
  }
}

// High DPI optimizations
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .retina-optimized {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  // Thinner borders on high DPI displays
  .thin-border {
    border-width: 0.5px !important;
  }
}

// Enhanced adaptive spacing based on screen size and orientation
@media (max-width: 479px) and (orientation: portrait) {
  .adaptive-spacing {
    padding: vars.$spacing-sm !important;
    margin: vars.$spacing-sm 0 !important;
  }

  .adaptive-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: vars.$spacing-sm !important;
  }

  .adaptive-flex {
    display: flex !important;
    flex-direction: column !important;
    gap: vars.$spacing-sm !important;
  }

  .adaptive-container {
    padding: vars.$spacing-sm !important;
    margin: 0 !important;
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-spacing {
    padding: vars.$spacing-xs vars.$spacing-md !important;
    margin: vars.$spacing-xs 0 !important;
  }

  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: vars.$spacing-sm !important;
  }

  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: vars.$spacing-sm !important;
  }

  .adaptive-container {
    padding: vars.$spacing-xs vars.$spacing-md !important;
    margin: 0 !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
  .adaptive-spacing {
    padding: vars.$spacing-lg !important;
    margin: vars.$spacing-lg 0 !important;
  }

  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: vars.$spacing-lg !important;
  }

  .adaptive-flex {
    display: flex !important;
    flex-direction: column !important;
    gap: vars.$spacing-lg !important;
  }

  .adaptive-container {
    padding: vars.$spacing-lg !important;
    max-width: 600px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) and (orientation: landscape) {
  .adaptive-spacing {
    padding: vars.$spacing-md vars.$spacing-xl !important;
    margin: vars.$spacing-md 0 !important;
  }

  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: vars.$spacing-lg !important;
  }

  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: vars.$spacing-lg !important;
  }

  .adaptive-container {
    padding: vars.$spacing-md vars.$spacing-xl !important;
    max-width: 900px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 1024px) and (orientation: portrait) {
  .adaptive-spacing {
    padding: vars.$spacing-xl !important;
    margin: vars.$spacing-xl 0 !important;
  }

  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: vars.$spacing-xl !important;
  }

  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: vars.$spacing-xl !important;
  }

  .adaptive-container {
    padding: vars.$spacing-xl !important;
    max-width: 800px !important;
    margin: 0 auto !important;
  }
}

@media (min-width: 1024px) and (orientation: landscape) {
  .adaptive-spacing {
    padding: vars.$spacing-lg vars.$spacing-xxl !important;
    margin: vars.$spacing-lg 0 !important;
  }

  .adaptive-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: vars.$spacing-xl !important;
  }

  .adaptive-flex {
    display: flex !important;
    flex-direction: row !important;
    gap: vars.$spacing-xl !important;
  }

  .adaptive-container {
    padding: vars.$spacing-lg vars.$spacing-xxl !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
  }
}

// Device and orientation specific form layouts
@media (max-width: 767px) and (orientation: portrait) {
  .adaptive-form {
    .form-row {
      flex-direction: column !important;
      gap: vars.$spacing-md !important;
    }

    .form-group {
      width: 100% !important;
    }

    .form-actions {
      flex-direction: column !important;
      gap: vars.$spacing-sm !important;

      button {
        width: 100% !important;
      }
    }
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-form {
    .form-row {
      flex-direction: row !important;
      gap: vars.$spacing-sm !important;
    }

    .form-group {
      flex: 1 !important;
      min-width: 150px !important;
    }

    .form-actions {
      flex-direction: row !important;
      gap: vars.$spacing-sm !important;
      justify-content: flex-end !important;

      button {
        min-width: 100px !important;
      }
    }
  }
}

@media (min-width: 768px) {
  .adaptive-form {
    .form-row {
      flex-direction: row !important;
      gap: vars.$spacing-lg !important;
    }

    .form-group {
      flex: 1 !important;
      min-width: 200px !important;
    }

    .form-actions {
      flex-direction: row !important;
      gap: vars.$spacing-md !important;
      justify-content: flex-end !important;
    }
  }
}

// Adaptive image and media layouts
@media (max-width: 767px) and (orientation: portrait) {
  .adaptive-media {
    .image-grid {
      grid-template-columns: 1fr !important;
      gap: vars.$spacing-md !important;
    }

    .image-item {
      aspect-ratio: 16/9 !important;
    }
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-media {
    .image-grid {
      grid-template-columns: repeat(2, 1fr) !important;
      gap: vars.$spacing-sm !important;
    }

    .image-item {
      aspect-ratio: 4/3 !important;
    }
  }
}

@media (min-width: 768px) {
  .adaptive-media {
    .image-grid {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: vars.$spacing-lg !important;
    }

    .image-item {
      aspect-ratio: 1/1 !important;
    }
  }
}

// Adaptive navigation layouts
@media (max-width: 767px) and (orientation: portrait) {
  .adaptive-nav {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    flex-direction: row !important;
    justify-content: space-around !important;
    padding: vars.$spacing-sm !important;
    background: rgba(white, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-top: 1px solid vars.$border-color-light !important;
    z-index: vars.$z-index-fixed !important;
  }
}

@media (max-width: 767px) and (orientation: landscape) {
  .adaptive-nav {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    padding: vars.$spacing-xs vars.$spacing-md !important;
    background: rgba(white, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid vars.$border-color-light !important;
    z-index: vars.$z-index-fixed !important;
    height: 56px !important;
  }
}

@media (min-width: 768px) {
  .adaptive-nav {
    position: relative !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    padding: vars.$spacing-md vars.$spacing-lg !important;
    background: white !important;
    border-bottom: 1px solid vars.$border-color-light !important;
  }
}