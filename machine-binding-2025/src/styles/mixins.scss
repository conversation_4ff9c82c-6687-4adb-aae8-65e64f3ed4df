@use './variables' as vars;

// Responsive breakpoint mixins
@mixin mobile-only {
  @media (max-width: #{vars.$tablet - 1px}) {
    @content;
  }
}

@mixin mobile-large-up {
  @media (min-width: #{vars.$mobile-large}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{vars.$tablet}) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: #{vars.$tablet}) and (max-width: #{vars.$desktop - 1px}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{vars.$desktop}) {
    @content;
  }
}

@mixin desktop-large-up {
  @media (min-width: #{vars.$desktop-large}) {
    @content;
  }
}

// Orientation-specific mixins
@mixin portrait {
  @media (orientation: portrait) {
    @content;
  }
}

@mixin landscape {
  @media (orientation: landscape) {
    @content;
  }
}

@mixin mobile-portrait {
  @media (max-width: #{vars.$tablet - 1px}) and (orientation: portrait) {
    @content;
  }
}

@mixin mobile-landscape {
  @media (max-width: #{vars.$tablet - 1px}) and (orientation: landscape) {
    @content;
  }
}

@mixin tablet-portrait {
  @media (min-width: #{vars.$tablet}) and (max-width: #{vars.$desktop - 1px}) and (orientation: portrait) {
    @content;
  }
}

@mixin tablet-landscape {
  @media (min-width: #{vars.$tablet}) and (max-width: #{vars.$desktop - 1px}) and (orientation: landscape) {
    @content;
  }
}

// Device-specific mixins
@mixin phone-only {
  @media (max-width: #{vars.$mobile-large - 1px}) {
    @content;
  }
}

@mixin phone-large {
  @media (min-width: #{vars.$mobile-large}) and (max-width: #{vars.$tablet - 1px}) {
    @content;
  }
}

@mixin tablet-small {
  @media (min-width: #{vars.$tablet}) and (max-width: #{vars.$tablet-large - 1px}) {
    @content;
  }
}

@mixin tablet-large {
  @media (min-width: #{vars.$tablet-large}) and (max-width: #{vars.$desktop - 1px}) {
    @content;
  }
}

// High DPI / Retina display mixin
@mixin retina {

  @media (-webkit-min-device-pixel-ratio: 2),
  (min-resolution: 192dpi) {
    @content;
  }
}

// Touch device detection
@mixin touch-device {
  @media (hover: none) and (pointer: coarse) {
    @content;
  }
}

@mixin hover-device {
  @media (hover: hover) and (pointer: fine) {
    @content;
  }
}

// Touch target mixin
@mixin touch-target($size: vars.$touch-target-min) {
  min-height: $size;
  min-width: $size;

  @include mobile-only {
    min-height: vars.$touch-target-comfortable;
    min-width: vars.$touch-target-comfortable;
  }
}

// Button sizing mixins
@mixin button-size($size: 'md') {
  @if $size =='sm' {
    height: vars.$button-height-sm;
    padding: 0 vars.$spacing-md;
    font-size: vars.$font-size-sm;
  }

  @else if $size =='lg' {
    height: vars.$button-height-lg;
    padding: 0 vars.$spacing-xl;
    font-size: vars.$font-size-lg;

    @include mobile-only {
      height: vars.$button-height-lg + 8px;
      padding: 0 vars.$spacing-xxl;
    }
  }

  @else {
    height: vars.$button-height-md;
    padding: 0 vars.$spacing-lg;
    font-size: vars.$font-size-md;

    @include mobile-only {
      height: vars.$button-height-lg;
      padding: 0 vars.$spacing-xl;
    }
  }
}

// Input sizing mixins
@mixin input-size($size: 'md') {
  @if $size =='sm' {
    height: vars.$input-height-sm;
    padding: 0 vars.$spacing-sm;
    font-size: vars.$font-size-sm;
  }

  @else if $size =='lg' {
    height: vars.$input-height-lg;
    padding: 0 vars.$spacing-lg;
    font-size: vars.$font-size-lg;
  }

  @else {
    height: vars.$input-height-md;
    padding: 0 vars.$spacing-md;
    font-size: vars.$font-size-md;

    @include mobile-only {
      height: vars.$input-height-lg;
      padding: 0 vars.$spacing-lg;
      font-size: 16px; // Prevents zoom on iOS
    }
  }
}

// Container mixin
@mixin container($fluid: false) {
  width: 100%;
  padding-left: vars.$spacing-md;
  padding-right: vars.$spacing-md;
  margin-left: auto;
  margin-right: auto;

  @if not $fluid {
    @include tablet-up {
      max-width: vars.$container-tablet;
      padding-left: vars.$spacing-lg;
      padding-right: vars.$spacing-lg;
    }

    @include desktop-up {
      max-width: vars.$container-desktop;
    }

    @include desktop-large-up {
      max-width: vars.$container-large;
    }
  }

  @else {
    @include tablet-up {
      padding-left: vars.$spacing-lg;
      padding-right: vars.$spacing-lg;
    }
  }
}

// Adaptive grid mixins
@mixin make-row($gutter: vars.$grid-gutter) {
  display: flex;
  flex-wrap: wrap;
  margin-left: -#{$gutter / 2};
  margin-right: -#{$gutter / 2};
}

@mixin make-col($size: null, $columns: vars.$grid-columns, $gutter: vars.$grid-gutter) {
  padding-left: #{$gutter / 2};
  padding-right: #{$gutter / 2};

  @if $size {
    flex: 0 0 percentage($size / $columns);
    max-width: percentage($size / $columns);
  }

  @else {
    flex: 1;
  }
}

// Adaptive grid system that scales based on device
@mixin adaptive-grid($mobile-cols: 1, $tablet-cols: 2, $desktop-cols: 3) {
  display: grid;
  gap: vars.$grid-gutter;

  // Mobile: Single column or specified
  grid-template-columns: repeat($mobile-cols, 1fr);

  @include mobile-landscape {
    // In landscape, we can fit more columns on mobile
    grid-template-columns: repeat(min($mobile-cols + 1, $tablet-cols), 1fr);
  }

  @include tablet-up {
    grid-template-columns: repeat($tablet-cols, 1fr);
  }

  @include tablet-landscape {
    // Tablets in landscape can fit more columns
    grid-template-columns: repeat(min($tablet-cols + 1, $desktop-cols), 1fr);
  }

  @include desktop-up {
    grid-template-columns: repeat($desktop-cols, 1fr);
  }
}

// Responsive card grid
@mixin card-grid($min-width: 280px, $max-cols: 4) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
  gap: vars.$grid-gutter;

  @include mobile-only {
    grid-template-columns: 1fr;
    gap: vars.$spacing-md;
  }

  @include mobile-landscape {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  @include tablet-up {
    grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
  }

  @include desktop-up {
    grid-template-columns: repeat(auto-fit, minmax($min-width, #{100% / $max-cols}));
  }
}

// Card/Section styling mixin
@mixin card-style {
  background: white;
  border-radius: vars.$border-radius-lg;
  box-shadow: vars.$shadow-sm;
  padding: vars.$spacing-lg;

  @include mobile-only {
    border-radius: vars.$border-radius-md;
    padding: vars.$spacing-md;
  }

  @include tablet-up {
    padding: vars.$spacing-xl;
  }
}

// Text truncation mixin
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-truncate-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Focus styles mixin
@mixin focus-style($color: vars.$primary-color) {
  &:focus {
    outline: 2px solid $color;
    outline-offset: 2px;
  }

  &:focus:not(:focus-visible) {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid $color;
    outline-offset: 2px;
  }
}

// Hover effects mixin
@mixin hover-lift($distance: 2px) {
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-#{$distance});
    box-shadow: vars.$shadow-md;
  }

  &:active {
    transform: translateY(0);
  }
}

// Loading state mixin
@mixin loading-state {
  position: relative;
  pointer-events: none;
  opacity: 0.6;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid vars.$primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Responsive font size mixin
@mixin responsive-font-size($mobile-size, $tablet-size: null, $desktop-size: null) {
  font-size: $mobile-size;

  @if $tablet-size {
    @include tablet-up {
      font-size: $tablet-size;
    }
  }

  @if $desktop-size {
    @include desktop-up {
      font-size: $desktop-size;
    }
  }
}

// Aspect ratio mixin
@mixin aspect-ratio($width: 16, $height: 9) {
  position: relative;

  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }

  >* {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// Safe area mixin for mobile devices with notches
@mixin safe-area-padding($property: padding, $direction: all) {
  @if $direction ==all {
    #{$property}: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }

  @else if $direction ==top {
    #{$property}-top: env(safe-area-inset-top);
  }

  @else if $direction ==right {
    #{$property}-right: env(safe-area-inset-right);
  }

  @else if $direction ==bottom {
    #{$property}-bottom: env(safe-area-inset-bottom);
  }

  @else if $direction ==left {
    #{$property}-left: env(safe-area-inset-left);
  }
}

// Scrollable area mixin
@mixin scrollable($direction: vertical) {
  @if $direction ==vertical {
    overflow-y: auto;
    overflow-x: hidden;
  }

  @else if $direction ==horizontal {
    overflow-x: auto;
    overflow-y: hidden;
  }

  @else {
    overflow: auto;
  }

  // Smooth scrolling on iOS
  -webkit-overflow-scrolling: touch;

  // Hide scrollbar on mobile
  @include mobile-only {
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// Enhanced device-specific optimizations
@mixin phone-optimized {
  @include phone-only {
    // Optimize for small screens (iPhone SE, etc.)
    font-size: vars.$font-size-sm;
    padding: vars.$spacing-sm;

    // Ensure touch targets are adequate
    button,
    a,
    input,
    select {
      min-height: vars.$touch-target-comfortable;
      min-width: vars.$touch-target-comfortable;
      font-size: 16px; // Prevent zoom on iOS
    }

    // Optimize layouts for small screens
    .grid-layout {
      grid-template-columns: 1fr;
      gap: vars.$spacing-sm;
    }

    // Stack navigation items
    .nav-items {
      flex-direction: column;
      gap: vars.$spacing-xs;
    }

    // Larger text for readability
    h1 {
      font-size: vars.$font-size-lg;
    }

    h2 {
      font-size: vars.$font-size-md;
    }

    h3 {
      font-size: vars.$font-size-sm;
    }

    // Optimize images
    img {
      border-radius: vars.$border-radius-sm;
    }
  }

  @include phone-large {
    // Larger phones (iPhone 12, etc.)
    font-size: vars.$font-size-md;
    padding: vars.$spacing-md;

    button,
    a,
    input,
    select {
      min-height: vars.$touch-target-min;
      font-size: 16px; // Prevent zoom on iOS
    }

    .grid-layout {
      grid-template-columns: repeat(2, 1fr);
      gap: vars.$spacing-md;
    }

    .nav-items {
      flex-direction: row;
      gap: vars.$spacing-sm;
    }
  }
}

@mixin tablet-optimized {
  @include tablet-small {
    // Small tablets (iPad mini, Surface Go, etc.)
    font-size: vars.$font-size-md;
    padding: vars.$spacing-lg;

    button,
    a,
    input,
    select {
      min-height: vars.$touch-target-min;
    }

    .grid-layout {
      grid-template-columns: repeat(2, 1fr);
      gap: vars.$spacing-lg;
    }

    // Optimize for portrait reading
    @include portrait {
      max-width: 600px;
      margin: 0 auto;

      .content-columns {
        grid-template-columns: 1fr;
      }
    }

    // Better use of landscape space
    @include landscape {
      .content-columns {
        grid-template-columns: 1fr 1fr;
        gap: vars.$spacing-xl;
      }
    }
  }

  @include tablet-large {
    // Large tablets (iPad Pro, Surface Pro, etc.)
    font-size: vars.$font-size-lg;
    padding: vars.$spacing-xl;

    .grid-layout {
      grid-template-columns: repeat(3, 1fr);
      gap: vars.$spacing-xl;
    }

    // Take advantage of larger screen
    @include portrait {
      max-width: 800px;
      margin: 0 auto;

      .content-columns {
        grid-template-columns: repeat(2, 1fr);
        gap: vars.$spacing-lg;
      }
    }

    @include landscape {
      .content-columns {
        grid-template-columns: repeat(3, 1fr);
        gap: vars.$spacing-xl;
      }

      // Sidebar layouts work well
      .main-sidebar-layout {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: vars.$spacing-xxl;
      }
    }
  }
}

// Device and orientation specific card layouts
@mixin adaptive-card-layout {
  @include mobile-portrait {
    .card {
      margin-bottom: vars.$spacing-md;
      padding: vars.$spacing-md;
      border-radius: vars.$border-radius-md;

      .card-header {
        margin-bottom: vars.$spacing-sm;
      }

      .card-actions {
        margin-top: vars.$spacing-md;
        display: flex;
        flex-direction: column;
        gap: vars.$spacing-sm;

        button {
          width: 100%;
        }
      }
    }
  }

  @include mobile-landscape {
    .card {
      margin-bottom: vars.$spacing-sm;
      padding: vars.$spacing-sm vars.$spacing-md;
      border-radius: vars.$border-radius-sm;

      .card-content {
        display: flex;
        align-items: center;
        gap: vars.$spacing-md;
      }

      .card-actions {
        margin-top: vars.$spacing-sm;
        display: flex;
        flex-direction: row;
        gap: vars.$spacing-sm;
        justify-content: flex-end;

        button {
          min-width: 100px;
        }
      }
    }
  }

  @include tablet-portrait {
    .card {
      margin-bottom: vars.$spacing-lg;
      padding: vars.$spacing-lg;
      border-radius: vars.$border-radius-lg;

      .card-actions {
        margin-top: vars.$spacing-lg;
        display: flex;
        flex-direction: row;
        gap: vars.$spacing-md;
        justify-content: flex-end;
      }
    }
  }

  @include tablet-landscape {
    .card {
      margin-bottom: vars.$spacing-lg;
      padding: vars.$spacing-lg vars.$spacing-xl;
      border-radius: vars.$border-radius-lg;

      .card-content {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: vars.$spacing-lg;
        align-items: center;
      }

      .card-actions {
        margin-top: 0;
        display: flex;
        flex-direction: row;
        gap: vars.$spacing-md;
      }
    }
  }
}

// Adaptive typography based on device and orientation
@mixin adaptive-typography {
  @include mobile-portrait {
    h1 {
      font-size: vars.$font-size-xl;
      line-height: 1.2;
      margin-bottom: vars.$spacing-md;
    }

    h2 {
      font-size: vars.$font-size-lg;
      line-height: 1.3;
      margin-bottom: vars.$spacing-sm;
    }

    h3 {
      font-size: vars.$font-size-md;
      line-height: 1.4;
      margin-bottom: vars.$spacing-sm;
    }

    p,
    li {
      font-size: vars.$font-size-md;
      line-height: 1.6;
      margin-bottom: vars.$spacing-md;
    }

    .lead-text {
      font-size: vars.$font-size-lg;
      line-height: 1.5;
    }
  }

  @include mobile-landscape {
    h1 {
      font-size: vars.$font-size-lg;
      line-height: 1.2;
      margin-bottom: vars.$spacing-sm;
    }

    h2 {
      font-size: vars.$font-size-md;
      line-height: 1.3;
      margin-bottom: vars.$spacing-sm;
    }

    h3 {
      font-size: vars.$font-size-sm;
      line-height: 1.4;
      margin-bottom: vars.$spacing-xs;
    }

    p,
    li {
      font-size: vars.$font-size-sm;
      line-height: 1.5;
      margin-bottom: vars.$spacing-sm;
    }
  }

  @include tablet-portrait {
    h1 {
      font-size: vars.$font-size-xxl;
      line-height: 1.2;
      margin-bottom: vars.$spacing-lg;
    }

    h2 {
      font-size: vars.$font-size-xl;
      line-height: 1.3;
      margin-bottom: vars.$spacing-md;
    }

    h3 {
      font-size: vars.$font-size-lg;
      line-height: 1.4;
      margin-bottom: vars.$spacing-md;
    }

    p,
    li {
      font-size: vars.$font-size-md;
      line-height: 1.6;
      margin-bottom: vars.$spacing-md;
    }

    .lead-text {
      font-size: vars.$font-size-xl;
      line-height: 1.4;
    }
  }

  @include tablet-landscape {
    h1 {
      font-size: vars.$font-size-xxl;
      line-height: 1.1;
      margin-bottom: vars.$spacing-lg;
    }

    h2 {
      font-size: vars.$font-size-xl;
      line-height: 1.2;
      margin-bottom: vars.$spacing-md;
    }

    h3 {
      font-size: vars.$font-size-lg;
      line-height: 1.3;
      margin-bottom: vars.$spacing-md;
    }

    p,
    li {
      font-size: vars.$font-size-md;
      line-height: 1.5;
      margin-bottom: vars.$spacing-md;
    }
  }
}

// Enhanced orientation-specific layouts
@mixin orientation-adaptive($portrait-cols: 1, $landscape-cols: 2, $tablet-portrait-cols: 2, $tablet-landscape-cols: 3) {
  @include mobile-portrait {
    grid-template-columns: repeat($portrait-cols, 1fr);
    flex-direction: column;
    gap: vars.$spacing-md;

    // Optimize spacing for portrait mobile
    padding: vars.$spacing-md;

    // Ensure adequate touch targets
    .interactive-element {
      min-height: vars.$touch-target-comfortable;
      padding: vars.$spacing-md;
    }
  }

  @include mobile-landscape {
    grid-template-columns: repeat($landscape-cols, 1fr);
    flex-direction: row;
    gap: vars.$spacing-sm;

    // More compact spacing in landscape
    padding: vars.$spacing-sm vars.$spacing-md;

    // Adjust for landscape constraints
    .interactive-element {
      min-height: vars.$touch-target-min;
      padding: vars.$spacing-sm vars.$spacing-md;
    }
  }

  @include tablet-portrait {
    grid-template-columns: repeat($tablet-portrait-cols, 1fr);
    flex-direction: column;
    gap: vars.$spacing-lg;
    padding: vars.$spacing-lg;

    // Optimize for tablet portrait reading
    max-width: 600px;
    margin: 0 auto;
  }

  @include tablet-landscape {
    grid-template-columns: repeat($tablet-landscape-cols, 1fr);
    flex-direction: row;
    gap: vars.$spacing-lg;
    padding: vars.$spacing-md vars.$spacing-xl;

    // Take advantage of wider screen
    max-width: 900px;
    margin: 0 auto;
  }
}

// Enhanced navigation adaptation for different orientations
@mixin adaptive-navigation {
  @include mobile-portrait {
    // Bottom navigation for portrait mobile
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    flex-direction: row;
    justify-content: space-around;
    padding: vars.$spacing-sm vars.$spacing-md;
    background: rgba(white, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid vars.$border-color-light;
    z-index: vars.$z-index-fixed;

    // Safe area support for devices with home indicators
    @include safe-area-padding(padding, bottom);

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: vars.$spacing-xs;
      min-height: vars.$touch-target-comfortable;
      border-radius: vars.$border-radius-sm;
      transition: background-color 0.2s ease;

      &:active {
        background: rgba(vars.$primary-color, 0.1);
      }
    }
  }

  @include mobile-landscape {
    // Compact top navigation for landscape mobile
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: vars.$spacing-xs vars.$spacing-md;
    background: rgba(white, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid vars.$border-color-light;
    z-index: vars.$z-index-fixed;
    height: 56px; // Compact height for landscape

    // Safe area support for devices with notches
    @include safe-area-padding(padding, top);

    .nav-item {
      padding: vars.$spacing-xs vars.$spacing-sm;
      min-height: vars.$touch-target-min;
      border-radius: vars.$border-radius-sm;
      display: flex;
      align-items: center;
      gap: vars.$spacing-xs;

      &:active {
        background: rgba(vars.$primary-color, 0.1);
      }
    }
  }

  @include tablet-portrait {
    // Top navigation optimized for tablet portrait
    position: relative;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: vars.$spacing-md vars.$spacing-lg;
    background: white;
    border-bottom: 1px solid vars.$border-color-light;

    .nav-item {
      padding: vars.$spacing-sm vars.$spacing-md;
      min-height: vars.$touch-target-min;
      border-radius: vars.$border-radius-md;
      display: flex;
      align-items: center;
      gap: vars.$spacing-sm;

      &:hover {
        background: rgba(vars.$primary-color, 0.05);
      }
    }
  }

  @include tablet-landscape {
    // Horizontal navigation for tablet landscape
    position: relative;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: vars.$spacing-md vars.$spacing-xl;
    background: white;
    border-bottom: 1px solid vars.$border-color-light;

    .nav-item {
      padding: vars.$spacing-sm vars.$spacing-lg;
      min-height: vars.$touch-target-min;
      border-radius: vars.$border-radius-md;
      display: flex;
      align-items: center;
      gap: vars.$spacing-sm;
      margin: 0 vars.$spacing-xs;

      &:hover {
        background: rgba(vars.$primary-color, 0.05);
      }
    }
  }
}

// Enhanced content area adaptation
@mixin adaptive-content-area {
  @include mobile-portrait {
    padding: vars.$spacing-md;
    padding-bottom: calc(80px + env(safe-area-inset-bottom)); // Account for bottom navigation + safe area
    min-height: calc(100vh - 80px - env(safe-area-inset-bottom));

    // Optimize for single-handed use
    .primary-actions {
      position: sticky;
      bottom: calc(80px + env(safe-area-inset-bottom) + vars.$spacing-md);
      z-index: vars.$z-index-sticky;
    }
  }

  @include mobile-landscape {
    padding: vars.$spacing-sm vars.$spacing-md;
    padding-top: calc(56px + env(safe-area-inset-top)); // Account for top navigation + safe area
    min-height: calc(100vh - 56px - env(safe-area-inset-top));

    // Optimize for landscape constraints
    .content-sections {
      display: flex;
      flex-direction: row;
      gap: vars.$spacing-md;

      .main-content {
        flex: 2;
      }

      .sidebar-content {
        flex: 1;
        min-width: 200px;
      }
    }
  }

  @include tablet-portrait {
    padding: vars.$spacing-lg vars.$spacing-xl;
    max-width: 700px;
    margin: 0 auto;

    // Optimize for reading and form interaction
    .form-sections {
      .form-group {
        margin-bottom: vars.$spacing-lg;
      }

      .form-row {
        display: flex;
        gap: vars.$spacing-md;

        .form-group {
          flex: 1;
        }
      }
    }

    // Better use of vertical space
    .content-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: vars.$spacing-lg;
    }
  }

  @include tablet-landscape {
    padding: vars.$spacing-lg vars.$spacing-xxl;
    max-width: 1000px;
    margin: 0 auto;

    // Take advantage of wider screen
    .content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: vars.$spacing-xl;
      align-items: start;
    }

    .form-sections {
      .form-row {
        display: flex;
        gap: vars.$spacing-lg;

        .form-group {
          flex: 1;
          min-width: 200px;
        }
      }
    }

    // Sidebar layout for complex interfaces
    .main-layout {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: vars.$spacing-xl;
    }
  }

  @include desktop-up {
    padding: vars.$spacing-xl;
    max-width: 1200px;
    margin: 0 auto;

    .content-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: vars.$spacing-xl;
    }

    .main-layout {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: vars.$spacing-xxl;
    }
  }
}

// Form layout adaptation
@mixin adaptive-form-layout {
  @include mobile-only {
    .form-row {
      flex-direction: column;
      gap: vars.$spacing-md;
    }

    .form-group {
      width: 100%;
    }
  }

  @include tablet-up {
    .form-row {
      flex-direction: row;
      gap: vars.$spacing-lg;
    }

    .form-group {
      flex: 1;
    }
  }

  @include landscape {
    .form-row {
      flex-wrap: wrap;
    }

    .form-group {
      min-width: 250px;
    }
  }
}

// Image and media adaptation
@mixin adaptive-media {

  img,
  video {
    width: 100%;
    height: auto;

    @include mobile-only {
      border-radius: vars.$border-radius-sm;
    }

    @include tablet-up {
      border-radius: vars.$border-radius-md;
    }
  }

  // Optimize images for different screen densities
  @include retina {
    img {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }
}