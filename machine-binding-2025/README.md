# React Device Binding App

A mobile-first React application for industrial device binding and management, converted from Vue.js.

## Features

- 📱 Mobile-first responsive design
- 🔍 QR code scanning for device identification
- 📋 Device binding forms with image uploads
- 👤 User authentication and profile management
- 📊 Binding records management
- 🔧 Administrative functions
- 🌐 SAP system integration

## Tech Stack

- **Frontend**: React 18 + TypeScript
- **Routing**: React Router v6
- **Styling**: Sass/SCSS with CSS Modules
- **HTTP Client**: Axios
- **Build Tool**: Vite
- **Code Quality**: ESLint + Prettier

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Type checking
npm run type-check
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components
│   ├── forms/          # Form components
│   ├── navigation/     # Navigation components
│   └── media/          # Media components
├── contexts/           # React contexts
├── hooks/              # Custom hooks
├── pages/              # Page components
├── services/           # API services
├── types/              # TypeScript types
├── utils/              # Utility functions
├── styles/             # Global styles
└── assets/             # Static assets
```

## Development

This project follows mobile-first responsive design principles and uses modern React patterns including:

- Functional components with hooks
- Context API for state management
- TypeScript for type safety
- SCSS for styling with CSS variables
- Axios for API integration

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking

## Contributing

1. Follow the existing code style and patterns
2. Write TypeScript types for all new code
3. Ensure responsive design works on mobile and tablet
4. Test on actual mobile devices when possible
5. Run linting and type checking before committing